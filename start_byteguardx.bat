@echo off
echo.
echo ========================================
echo    ByteGuardX Application Launcher
echo ========================================
echo.

echo Starting ByteGuardX Backend Server...
start "ByteGuardX Backend" cmd /k "python simple_backend.py"

echo Waiting for backend to start...
timeout /t 3 /nobreak >nul

echo Starting ByteGuardX Frontend...
start "ByteGuardX Frontend" cmd /k "cd frontend && npm start"

echo.
echo ========================================
echo    ByteGuardX is starting up...
echo ========================================
echo.
echo Backend:  http://localhost:5000
echo Frontend: http://localhost:3000
echo.
echo Both servers will open in separate windows.
echo Close those windows to stop the servers.
echo.
pause
