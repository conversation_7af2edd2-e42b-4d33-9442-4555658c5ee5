# ByteGuardX Core Dependencies (Production)
# For development dependencies, see dev-requirements.txt

# Core Web Framework
Flask==2.3.3
Flask-CORS==4.0.0
Flask-JWT-Extended==4.5.3
Flask-SocketIO==5.3.6
Werkzeug==2.3.7

# Database & ORM (Core)
SQLAlchemy==2.0.23
bcrypt==4.1.2

# Security & Validation (Essential)
Flask-Limiter==3.5.0  # Rate limiting
Flask-Talisman==1.1.0  # Security headers
cryptography==41.0.7
python-magic==0.4.27
validators==0.22.0
PyJWT==2.8.0
pyotp==2.9.0  # TOTP for 2FA
passlib[bcrypt]==1.7.4  # Enhanced password hashing
email-validator==2.1.0  # Email validation
pathvalidate==3.2.0  # Cross-platform path validation for folder uploads

# System Monitoring (Lightweight)
psutil==5.9.6  # System resource monitoring

# CLI Interface (Essential)
click==8.1.7
rich==13.6.0

# Data Processing (Lightweight)
httpx==0.25.2  # Lighter alternative to requests
pyyaml==6.0.1
jsonschema==4.19.2

# Environment & Configuration
python-dotenv==1.0.0

# Optional Dependencies (install separately if needed)
# Install with: pip install byteguardx[feature]

# PDF Generation (optional)
# WeasyPrint==60.1
# Jinja2==3.1.2

# Database Adapters (optional)
# psycopg2-binary==2.9.9  # PostgreSQL
# pymysql==1.1.0  # MySQL

# QR Code Generation (optional for 2FA)
# qrcode[pil]==7.4.2

# Async Support (optional)
# aiofiles==23.2.1
# aiohttp==3.9.1

# Message Queue (optional)
# redis==4.6.0
# celery==5.3.4

# Enterprise Features (optional)
# python-saml==1.15.0
# xmlsec==1.3.13

# Container Support (optional)
# docker==6.1.3

# ML Dependencies (optional - install only if using AI features)
# numpy==1.24.3
# scikit-learn==1.3.0

# Heavy ML Dependencies (optional - install separately)
# pandas==2.0.3
# matplotlib==3.7.2
# seaborn==0.12.2
# transformers==4.35.0
# torch==2.1.0+cpu
# onnxruntime==1.16.0
