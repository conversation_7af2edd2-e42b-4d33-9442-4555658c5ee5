# 🚀 ByteGuardX Quick Start Guide

## **Prerequisites**

Before running ByteGuardX, ensure you have:

- **Python 3.8+** - [Download from python.org](https://python.org)
- **Node.js 16+** - [Download from nodejs.org](https://nodejs.org)
- **Git** (optional) - For cloning repositories

## **🎯 One-Command Launch**

### **Windows:**
```cmd
run_byteguardx.bat
```

### **Linux/macOS:**
```bash
./run_byteguardx.sh
```

### **Cross-Platform Python:**
```bash
python setup_and_run_byteguardx.py
```

## **🌐 Access Points**

Once started, ByteGuardX will be available at:

| Service | URL | Description |
|---------|-----|-------------|
| **Web Dashboard** | http://localhost:3000 | Main React frontend with glassmorphism UI |
| **API Server** | http://localhost:5000 | REST API + WebSocket server |
| **Health Check** | http://localhost:5000/health | System status and diagnostics |
| **API Docs** | http://localhost:5000/docs | Interactive API documentation |

## **✨ What Gets Started**

The launcher automatically:

1. ✅ **Checks system requirements** (Python, Node.js)
2. ✅ **Installs Python dependencies** (Flask, AI libraries, security tools)
3. ✅ **Installs frontend dependencies** (React, TypeScript, UI libraries)
4. ✅ **Creates required directories** (data, logs, reports, models)
5. ✅ **Initializes database** (SQLite fallback if PostgreSQL unavailable)
6. ✅ **Starts backend API server** (Port 5000)
7. ✅ **Starts React frontend** (Port 3000)
8. ✅ **Opens browser interfaces** automatically

## **🛡️ Features Available**

### **Web Dashboard (localhost:3000)**
- Real-time vulnerability analytics
- File/folder upload scanning (2GB limit)
- Interactive charts and visualizations
- Glassmorphism dark UI design
- Live WebSocket updates

### **API Server (localhost:5000)**
- REST API endpoints for scanning
- WebSocket for real-time updates
- File upload support (single files + ZIP folders)
- Rate limiting and security headers
- Health monitoring endpoints

### **Security Scanning**
- 20+ production-grade security plugins
- AI-powered vulnerability detection
- Multi-language support (Python, JS, Java, Go, etc.)
- Real-time code analysis
- Comprehensive reporting (PDF, SARIF, JSON, HTML)

## **🔧 Manual Setup (Alternative)**

If the automatic launcher doesn't work, you can set up manually:

### **1. Install Python Dependencies**
```bash
pip install -r requirements.txt
pip install aiofiles aiohttp numpy pandas matplotlib plotly Jinja2 reportlab Pillow
```

### **2. Install Frontend Dependencies**
```bash
cd frontend
npm install
cd ..
```

### **3. Start Backend**
```bash
python byteguardx/api/app.py
```

### **4. Start Frontend (New Terminal)**
```bash
cd frontend
npm start
```

## **📱 Additional Platforms**

### **Desktop App**
```bash
cd desktop
npm install
npm start
```

### **Mobile App**
```bash
cd mobile
npm install
npx react-native run-android  # or run-ios
```

### **Browser Extension**
1. Open Chrome/Firefox
2. Go to Extensions → Developer Mode
3. Load unpacked: `extensions/browser/`

## **🐳 Docker Deployment**

For production deployment:

```bash
docker-compose up -d
```

This starts:
- ByteGuardX API + Frontend
- PostgreSQL database
- Redis cache
- Nginx reverse proxy
- ELK stack (logging)
- Prometheus + Grafana (monitoring)

## **🔍 Testing the Installation**

### **1. Check Health Status**
```bash
curl http://localhost:5000/health
```

### **2. Test File Scanning**
Upload a file through the web interface at http://localhost:3000

### **3. Test API Scanning**
```bash
curl -X POST http://localhost:5000/api/v1/scan \
  -H "Content-Type: application/json" \
  -d '{"content": "eval(user_input)", "file_path": "test.py"}'
```

## **🛠️ Troubleshooting**

### **Port Already in Use**
If ports 3000 or 5000 are busy:
```bash
# Kill processes on Windows
netstat -ano | findstr :3000
taskkill /PID <PID> /F

# Kill processes on Linux/macOS
lsof -ti:3000 | xargs kill -9
```

### **Python Dependencies Issues**
```bash
# Upgrade pip
python -m pip install --upgrade pip

# Install with verbose output
pip install -r requirements.txt -v
```

### **Node.js Issues**
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
cd frontend
rm -rf node_modules package-lock.json
npm install
```

### **Database Issues**
The system automatically falls back to SQLite if PostgreSQL is unavailable.

## **📊 System Requirements**

### **Minimum:**
- Python 3.8+
- Node.js 16+
- 4GB RAM
- 2GB disk space

### **Recommended:**
- Python 3.11+
- Node.js 18+
- 8GB RAM
- 10GB disk space
- SSD storage

## **🔒 Security Notes**

- ByteGuardX runs locally by default (no external connections)
- All scanning is performed offline
- No data is sent to external servers
- Audit logs are stored locally in `data/audit_logs/`

## **📞 Support**

If you encounter issues:

1. Check the console output for error messages
2. Verify all prerequisites are installed
3. Try the manual setup steps
4. Check the troubleshooting section above

## **🎉 Success!**

When everything is working, you should see:

```
🎉 ByteGuardX Successfully Started!
======================================
🌐 Frontend Dashboard:  http://localhost:3000
🔧 API Server:          http://localhost:5000
📊 Health Check:        http://localhost:5000/health
📚 API Documentation:   http://localhost:5000/docs
======================================
🚀 All services are running!
📝 Press Ctrl+C to stop all services
```

**Happy scanning! 🛡️**
