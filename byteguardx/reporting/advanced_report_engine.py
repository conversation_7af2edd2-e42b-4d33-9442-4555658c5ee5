"""
ByteGuardX Advanced Reporting Engine
Comprehensive reporting system with PDF, SARIF, JSON, and custom formats
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
import json
import uuid
from jinja2 import Environment, FileSystemLoader, Template
import matplotlib.pyplot as plt
import seaborn as sns
from reportlab.lib import colors
from reportlab.lib.pagesizes import letter, A4
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import base64
from io import BytesIO

logger = logging.getLogger(__name__)

@dataclass
class ReportConfig:
    """Report generation configuration"""
    report_type: str  # 'executive', 'technical', 'compliance', 'custom'
    output_format: str  # 'pdf', 'html', 'json', 'sarif', 'csv', 'xlsx'
    template_name: str
    include_charts: bool = True
    include_code_snippets: bool = True
    include_remediation: bool = True
    include_trends: bool = True
    severity_filter: List[str] = field(default_factory=lambda: ['critical', 'high', 'medium', 'low'])
    date_range: Optional[Dict[str, datetime]] = None
    custom_branding: Optional[Dict[str, str]] = None
    language: str = 'en'
    timezone: str = 'UTC'

@dataclass
class ReportData:
    """Structured report data"""
    scan_summary: Dict[str, Any]
    findings: List[Dict[str, Any]]
    trends: Dict[str, Any]
    metrics: Dict[str, Any]
    compliance_status: Dict[str, Any]
    recommendations: List[str]
    metadata: Dict[str, Any]

class AdvancedReportEngine:
    """
    Advanced reporting engine with multiple output formats and customization
    """
    
    def __init__(self, templates_dir: str = "templates/reports"):
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(parents=True, exist_ok=True)
        
        self.output_dir = Path("reports/generated")
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize Jinja2 environment
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)),
            autoescape=True
        )
        
        # Report styles
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
        
        # Chart configuration
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        logger.info("Advanced Report Engine initialized")
    
    def _setup_custom_styles(self):
        """Setup custom ReportLab styles"""
        self.styles.add(ParagraphStyle(
            name='CustomTitle',
            parent=self.styles['Heading1'],
            fontSize=24,
            spaceAfter=30,
            textColor=colors.HexColor('#1f2937')
        ))
        
        self.styles.add(ParagraphStyle(
            name='CustomHeading',
            parent=self.styles['Heading2'],
            fontSize=16,
            spaceAfter=12,
            textColor=colors.HexColor('#374151')
        ))
        
        self.styles.add(ParagraphStyle(
            name='CustomBody',
            parent=self.styles['Normal'],
            fontSize=11,
            spaceAfter=6,
            textColor=colors.HexColor('#4b5563')
        ))
    
    async def generate_report(self, config: ReportConfig, data: ReportData) -> Dict[str, Any]:
        """Generate report based on configuration"""
        try:
            logger.info(f"Generating {config.report_type} report in {config.output_format} format")
            
            # Prepare report data
            processed_data = await self._process_report_data(data, config)
            
            # Generate report based on format
            if config.output_format == 'pdf':
                result = await self._generate_pdf_report(config, processed_data)
            elif config.output_format == 'html':
                result = await self._generate_html_report(config, processed_data)
            elif config.output_format == 'json':
                result = await self._generate_json_report(config, processed_data)
            elif config.output_format == 'sarif':
                result = await self._generate_sarif_report(config, processed_data)
            elif config.output_format == 'csv':
                result = await self._generate_csv_report(config, processed_data)
            elif config.output_format == 'xlsx':
                result = await self._generate_xlsx_report(config, processed_data)
            else:
                raise ValueError(f"Unsupported output format: {config.output_format}")
            
            logger.info(f"Report generated successfully: {result['file_path']}")
            return result
            
        except Exception as e:
            logger.error(f"Report generation failed: {e}")
            raise
    
    async def _process_report_data(self, data: ReportData, config: ReportConfig) -> Dict[str, Any]:
        """Process and enrich report data"""
        try:
            # Filter findings by severity
            filtered_findings = [
                finding for finding in data.findings
                if finding.get('severity', '').lower() in [s.lower() for s in config.severity_filter]
            ]
            
            # Calculate summary statistics
            severity_counts = {}
            for severity in ['critical', 'high', 'medium', 'low']:
                severity_counts[severity] = len([
                    f for f in filtered_findings 
                    if f.get('severity', '').lower() == severity
                ])
            
            # Generate charts if requested
            charts = {}
            if config.include_charts:
                charts = await self._generate_charts(filtered_findings, data.trends)
            
            # Calculate risk score
            risk_score = self._calculate_risk_score(severity_counts)
            
            # Generate recommendations
            recommendations = await self._generate_recommendations(filtered_findings, data.compliance_status)
            
            processed_data = {
                'config': config,
                'scan_summary': {
                    **data.scan_summary,
                    'total_findings': len(filtered_findings),
                    'severity_counts': severity_counts,
                    'risk_score': risk_score
                },
                'findings': filtered_findings,
                'trends': data.trends,
                'metrics': data.metrics,
                'compliance_status': data.compliance_status,
                'recommendations': recommendations,
                'charts': charts,
                'metadata': {
                    **data.metadata,
                    'generated_at': datetime.now(),
                    'report_id': str(uuid.uuid4()),
                    'generator': 'ByteGuardX Advanced Report Engine'
                }
            }
            
            return processed_data
            
        except Exception as e:
            logger.error(f"Data processing failed: {e}")
            raise
    
    async def _generate_pdf_report(self, config: ReportConfig, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate PDF report"""
        try:
            filename = f"{config.report_type}_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
            file_path = self.output_dir / filename
            
            # Create PDF document
            doc = SimpleDocTemplate(str(file_path), pagesize=A4)
            story = []
            
            # Title page
            story.append(Paragraph("Security Vulnerability Report", self.styles['CustomTitle']))
            story.append(Spacer(1, 20))
            
            # Executive summary
            if config.report_type == 'executive':
                story.extend(await self._create_executive_summary(data))
            elif config.report_type == 'technical':
                story.extend(await self._create_technical_summary(data))
            elif config.report_type == 'compliance':
                story.extend(await self._create_compliance_summary(data))
            
            # Summary statistics table
            summary_data = [
                ['Metric', 'Value'],
                ['Total Findings', str(data['scan_summary']['total_findings'])],
                ['Critical Issues', str(data['scan_summary']['severity_counts']['critical'])],
                ['High Issues', str(data['scan_summary']['severity_counts']['high'])],
                ['Medium Issues', str(data['scan_summary']['severity_counts']['medium'])],
                ['Low Issues', str(data['scan_summary']['severity_counts']['low'])],
                ['Risk Score', f"{data['scan_summary']['risk_score']:.1f}/10"]
            ]
            
            summary_table = Table(summary_data)
            summary_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 14),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            
            story.append(summary_table)
            story.append(Spacer(1, 20))
            
            # Charts
            if config.include_charts and data['charts']:
                for chart_name, chart_data in data['charts'].items():
                    if chart_data:
                        story.append(Paragraph(f"{chart_name.replace('_', ' ').title()}", self.styles['CustomHeading']))
                        story.append(Image(chart_data, width=6*inch, height=4*inch))
                        story.append(Spacer(1, 12))
            
            # Detailed findings
            if config.include_code_snippets:
                story.append(Paragraph("Detailed Findings", self.styles['CustomHeading']))
                
                for i, finding in enumerate(data['findings'][:20]):  # Limit to first 20
                    story.append(Paragraph(f"Finding {i+1}: {finding['title']}", self.styles['Heading3']))
                    story.append(Paragraph(f"Severity: {finding['severity'].upper()}", self.styles['CustomBody']))
                    story.append(Paragraph(f"File: {finding['file_path']}", self.styles['CustomBody']))
                    story.append(Paragraph(f"Description: {finding['description']}", self.styles['CustomBody']))
                    
                    if config.include_remediation and finding.get('remediation'):
                        story.append(Paragraph(f"Remediation: {finding['remediation']}", self.styles['CustomBody']))
                    
                    story.append(Spacer(1, 12))
            
            # Recommendations
            if data['recommendations']:
                story.append(Paragraph("Recommendations", self.styles['CustomHeading']))
                for i, rec in enumerate(data['recommendations'], 1):
                    story.append(Paragraph(f"{i}. {rec}", self.styles['CustomBody']))
                    story.append(Spacer(1, 6))
            
            # Build PDF
            doc.build(story)
            
            return {
                'success': True,
                'file_path': str(file_path),
                'file_size': file_path.stat().st_size,
                'format': 'pdf'
            }
            
        except Exception as e:
            logger.error(f"PDF generation failed: {e}")
            raise
    
    async def _generate_html_report(self, config: ReportConfig, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate HTML report"""
        try:
            template_name = f"{config.template_name}.html" if config.template_name else "default.html"
            template = self.jinja_env.get_template(template_name)
            
            # Render HTML
            html_content = template.render(
                config=config,
                data=data,
                generated_at=datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            )
            
            filename = f"{config.report_type}_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
            file_path = self.output_dir / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(html_content)
            
            return {
                'success': True,
                'file_path': str(file_path),
                'file_size': file_path.stat().st_size,
                'format': 'html'
            }
            
        except Exception as e:
            logger.error(f"HTML generation failed: {e}")
            raise
    
    async def _generate_json_report(self, config: ReportConfig, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate JSON report"""
        try:
            # Convert datetime objects to strings
            json_data = self._serialize_for_json(data)
            
            filename = f"{config.report_type}_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            file_path = self.output_dir / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            
            return {
                'success': True,
                'file_path': str(file_path),
                'file_size': file_path.stat().st_size,
                'format': 'json'
            }
            
        except Exception as e:
            logger.error(f"JSON generation failed: {e}")
            raise
    
    async def _generate_sarif_report(self, config: ReportConfig, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate SARIF (Static Analysis Results Interchange Format) report"""
        try:
            sarif_data = {
                "version": "2.1.0",
                "$schema": "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json",
                "runs": [
                    {
                        "tool": {
                            "driver": {
                                "name": "ByteGuardX",
                                "version": "1.0.0",
                                "informationUri": "https://byteguardx.com",
                                "rules": []
                            }
                        },
                        "results": []
                    }
                ]
            }
            
            # Convert findings to SARIF format
            rule_ids = set()
            for finding in data['findings']:
                rule_id = finding.get('type', 'unknown')
                rule_ids.add(rule_id)
                
                result = {
                    "ruleId": rule_id,
                    "level": self._severity_to_sarif_level(finding.get('severity', 'info')),
                    "message": {
                        "text": finding.get('description', '')
                    },
                    "locations": [
                        {
                            "physicalLocation": {
                                "artifactLocation": {
                                    "uri": finding.get('file_path', '')
                                },
                                "region": {
                                    "startLine": finding.get('line_start', 1),
                                    "endLine": finding.get('line_end', 1),
                                    "startColumn": finding.get('column_start', 1),
                                    "endColumn": finding.get('column_end', 1)
                                }
                            }
                        }
                    ]
                }
                
                if finding.get('evidence'):
                    result["locations"][0]["physicalLocation"]["region"]["snippet"] = {
                        "text": finding['evidence']
                    }
                
                sarif_data["runs"][0]["results"].append(result)
            
            # Add rules
            for rule_id in rule_ids:
                rule = {
                    "id": rule_id,
                    "name": rule_id.replace('_', ' ').title(),
                    "shortDescription": {
                        "text": f"{rule_id.replace('_', ' ').title()} vulnerability"
                    },
                    "helpUri": f"https://docs.byteguardx.com/rules/{rule_id}"
                }
                sarif_data["runs"][0]["tool"]["driver"]["rules"].append(rule)
            
            filename = f"{config.report_type}_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sarif"
            file_path = self.output_dir / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(sarif_data, f, indent=2)
            
            return {
                'success': True,
                'file_path': str(file_path),
                'file_size': file_path.stat().st_size,
                'format': 'sarif'
            }
            
        except Exception as e:
            logger.error(f"SARIF generation failed: {e}")
            raise
    
    async def _generate_csv_report(self, config: ReportConfig, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate CSV report"""
        try:
            # Convert findings to DataFrame
            df = pd.DataFrame(data['findings'])
            
            filename = f"{config.report_type}_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            file_path = self.output_dir / filename
            
            df.to_csv(file_path, index=False, encoding='utf-8')
            
            return {
                'success': True,
                'file_path': str(file_path),
                'file_size': file_path.stat().st_size,
                'format': 'csv'
            }
            
        except Exception as e:
            logger.error(f"CSV generation failed: {e}")
            raise
    
    async def _generate_xlsx_report(self, config: ReportConfig, data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate Excel report"""
        try:
            filename = f"{config.report_type}_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
            file_path = self.output_dir / filename
            
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # Summary sheet
                summary_df = pd.DataFrame([
                    ['Total Findings', data['scan_summary']['total_findings']],
                    ['Critical', data['scan_summary']['severity_counts']['critical']],
                    ['High', data['scan_summary']['severity_counts']['high']],
                    ['Medium', data['scan_summary']['severity_counts']['medium']],
                    ['Low', data['scan_summary']['severity_counts']['low']],
                    ['Risk Score', data['scan_summary']['risk_score']]
                ], columns=['Metric', 'Value'])
                summary_df.to_sheet(writer, sheet_name='Summary', index=False)
                
                # Findings sheet
                findings_df = pd.DataFrame(data['findings'])
                findings_df.to_sheet(writer, sheet_name='Findings', index=False)
                
                # Trends sheet (if available)
                if data['trends']:
                    trends_df = pd.DataFrame(data['trends'])
                    trends_df.to_sheet(writer, sheet_name='Trends', index=False)
            
            return {
                'success': True,
                'file_path': str(file_path),
                'file_size': file_path.stat().st_size,
                'format': 'xlsx'
            }
            
        except Exception as e:
            logger.error(f"Excel generation failed: {e}")
            raise
    
    async def _generate_charts(self, findings: List[Dict[str, Any]], trends: Dict[str, Any]) -> Dict[str, str]:
        """Generate charts for report"""
        charts = {}
        
        try:
            # Severity distribution pie chart
            severity_counts = {}
            for finding in findings:
                severity = finding.get('severity', 'unknown')
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            if severity_counts:
                fig = px.pie(
                    values=list(severity_counts.values()),
                    names=list(severity_counts.keys()),
                    title="Vulnerability Distribution by Severity"
                )
                charts['severity_distribution'] = self._fig_to_base64(fig)
            
            # Vulnerability types bar chart
            type_counts = {}
            for finding in findings:
                vuln_type = finding.get('type', 'unknown')
                type_counts[vuln_type] = type_counts.get(vuln_type, 0) + 1
            
            if type_counts:
                fig = px.bar(
                    x=list(type_counts.keys()),
                    y=list(type_counts.values()),
                    title="Vulnerabilities by Type"
                )
                charts['vulnerability_types'] = self._fig_to_base64(fig)
            
            # Trends chart (if data available)
            if trends and 'timeline' in trends:
                timeline_data = trends['timeline']
                fig = px.line(
                    x=[item['date'] for item in timeline_data],
                    y=[item['count'] for item in timeline_data],
                    title="Vulnerability Trends Over Time"
                )
                charts['trends'] = self._fig_to_base64(fig)
            
        except Exception as e:
            logger.error(f"Chart generation failed: {e}")
        
        return charts
    
    def _fig_to_base64(self, fig) -> str:
        """Convert Plotly figure to base64 string"""
        img_bytes = fig.to_image(format="png")
        img_base64 = base64.b64encode(img_bytes).decode()
        return f"data:image/png;base64,{img_base64}"
    
    def _calculate_risk_score(self, severity_counts: Dict[str, int]) -> float:
        """Calculate overall risk score (0-10)"""
        weights = {'critical': 4.0, 'high': 3.0, 'medium': 2.0, 'low': 1.0}
        total_score = sum(count * weights.get(severity, 0) for severity, count in severity_counts.items())
        total_findings = sum(severity_counts.values())
        
        if total_findings == 0:
            return 0.0
        
        # Normalize to 0-10 scale
        max_possible = total_findings * 4.0  # All critical
        return min(10.0, (total_score / max_possible) * 10.0)
    
    def _severity_to_sarif_level(self, severity: str) -> str:
        """Convert severity to SARIF level"""
        mapping = {
            'critical': 'error',
            'high': 'error',
            'medium': 'warning',
            'low': 'note',
            'info': 'note'
        }
        return mapping.get(severity.lower(), 'note')
    
    def _serialize_for_json(self, obj: Any) -> Any:
        """Serialize objects for JSON output"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, dict):
            return {k: self._serialize_for_json(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._serialize_for_json(item) for item in obj]
        else:
            return obj
    
    async def _create_executive_summary(self, data: Dict[str, Any]) -> List:
        """Create executive summary content"""
        content = []
        content.append(Paragraph("Executive Summary", self.styles['CustomHeading']))
        
        summary_text = f"""
        This security assessment identified {data['scan_summary']['total_findings']} vulnerabilities 
        across the scanned codebase. The overall risk score is {data['scan_summary']['risk_score']:.1f}/10.
        
        Critical issues requiring immediate attention: {data['scan_summary']['severity_counts']['critical']}
        High-priority issues: {data['scan_summary']['severity_counts']['high']}
        
        Immediate action is recommended to address critical and high-severity vulnerabilities.
        """
        
        content.append(Paragraph(summary_text, self.styles['CustomBody']))
        content.append(Spacer(1, 12))
        
        return content
    
    async def _create_technical_summary(self, data: Dict[str, Any]) -> List:
        """Create technical summary content"""
        content = []
        content.append(Paragraph("Technical Summary", self.styles['CustomHeading']))
        
        # Add technical details
        return content
    
    async def _create_compliance_summary(self, data: Dict[str, Any]) -> List:
        """Create compliance summary content"""
        content = []
        content.append(Paragraph("Compliance Summary", self.styles['CustomHeading']))
        
        # Add compliance details
        return content
    
    async def _generate_recommendations(self, findings: List[Dict[str, Any]], 
                                      compliance_status: Dict[str, Any]) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []
        
        # Analyze findings and generate recommendations
        critical_count = len([f for f in findings if f.get('severity') == 'critical'])
        if critical_count > 0:
            recommendations.append(f"Address {critical_count} critical vulnerabilities immediately")
        
        # Add more intelligent recommendations based on finding patterns
        vuln_types = {}
        for finding in findings:
            vuln_type = finding.get('type', 'unknown')
            vuln_types[vuln_type] = vuln_types.get(vuln_type, 0) + 1
        
        if vuln_types.get('sql_injection', 0) > 0:
            recommendations.append("Implement parameterized queries to prevent SQL injection")
        
        if vuln_types.get('xss', 0) > 0:
            recommendations.append("Implement proper input sanitization and output encoding")
        
        if vuln_types.get('secret', 0) > 0:
            recommendations.append("Remove hardcoded secrets and use secure configuration management")
        
        return recommendations

# Global report engine instance
report_engine = AdvancedReportEngine()
