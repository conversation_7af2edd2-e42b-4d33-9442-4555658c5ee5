"""
ByteGuardX ML Model Training Pipeline
Advanced machine learning pipeline for training custom security models
"""

import logging
import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from pathlib import Path
import json
import pickle
import joblib
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.feature_extraction.text import TfidfVectorizer
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, Dataset
from transformers import AutoTokenizer, AutoModel, TrainingArguments, Trainer
import onnx
import onnxruntime as ort
from tensorboard.backend.event_processing.event_accumulator import EventAccumulator

logger = logging.getLogger(__name__)

@dataclass
class TrainingConfig:
    """ML model training configuration"""
    model_type: str  # 'traditional', 'deep_learning', 'transformer'
    algorithm: str  # 'random_forest', 'gradient_boosting', 'cnn', 'bert', etc.
    hyperparameters: Dict[str, Any]
    training_data_path: str
    validation_split: float
    test_split: float
    batch_size: int
    epochs: int
    learning_rate: float
    early_stopping_patience: int
    model_output_path: str
    tensorboard_log_dir: str
    cross_validation_folds: int
    feature_selection: bool
    data_augmentation: bool
    quantization: bool
    export_onnx: bool

@dataclass
class TrainingMetrics:
    """Training metrics and results"""
    accuracy: float
    precision: float
    recall: float
    f1_score: float
    auc_roc: float
    confusion_matrix: List[List[int]]
    training_loss: List[float]
    validation_loss: List[float]
    training_time: float
    model_size_mb: float
    inference_time_ms: float

class SecurityDataset(Dataset):
    """PyTorch dataset for security vulnerability data"""
    
    def __init__(self, texts: List[str], labels: List[int], tokenizer, max_length: int = 512):
        self.texts = texts
        self.labels = labels
        self.tokenizer = tokenizer
        self.max_length = max_length
    
    def __len__(self):
        return len(self.texts)
    
    def __getitem__(self, idx):
        text = str(self.texts[idx])
        label = self.labels[idx]
        
        encoding = self.tokenizer(
            text,
            truncation=True,
            padding='max_length',
            max_length=self.max_length,
            return_tensors='pt'
        )
        
        return {
            'input_ids': encoding['input_ids'].flatten(),
            'attention_mask': encoding['attention_mask'].flatten(),
            'labels': torch.tensor(label, dtype=torch.long)
        }

class SecurityCNN(nn.Module):
    """CNN model for security vulnerability detection"""
    
    def __init__(self, vocab_size: int, embedding_dim: int, num_classes: int):
        super(SecurityCNN, self).__init__()
        self.embedding = nn.Embedding(vocab_size, embedding_dim)
        self.conv1 = nn.Conv1d(embedding_dim, 128, kernel_size=3, padding=1)
        self.conv2 = nn.Conv1d(128, 64, kernel_size=3, padding=1)
        self.pool = nn.AdaptiveMaxPool1d(1)
        self.dropout = nn.Dropout(0.5)
        self.fc = nn.Linear(64, num_classes)
    
    def forward(self, x):
        x = self.embedding(x)
        x = x.transpose(1, 2)  # (batch, embedding_dim, seq_len)
        x = torch.relu(self.conv1(x))
        x = torch.relu(self.conv2(x))
        x = self.pool(x).squeeze(-1)
        x = self.dropout(x)
        x = self.fc(x)
        return x

class ModelTrainingPipeline:
    """
    Advanced ML model training pipeline for security vulnerability detection
    """
    
    def __init__(self, config: TrainingConfig):
        self.config = config
        self.models_dir = Path("models/trained")
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        self.data_dir = Path("data/training")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize components
        self.scaler = StandardScaler()
        self.label_encoder = LabelEncoder()
        self.vectorizer = TfidfVectorizer(max_features=10000, stop_words='english')
        
        # Training state
        self.training_history = []
        self.best_model = None
        self.best_metrics = None
        
        logger.info(f"Model training pipeline initialized for {config.model_type}")
    
    async def prepare_training_data(self, data_sources: List[str]) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare and preprocess training data"""
        try:
            logger.info("Preparing training data...")
            
            # Load data from multiple sources
            all_data = []
            all_labels = []
            
            for source in data_sources:
                data, labels = await self._load_data_source(source)
                all_data.extend(data)
                all_labels.extend(labels)
            
            logger.info(f"Loaded {len(all_data)} training samples")
            
            # Data preprocessing
            if self.config.model_type == 'traditional':
                # Feature extraction for traditional ML
                X = await self._extract_traditional_features(all_data)
            elif self.config.model_type == 'deep_learning':
                # Prepare data for deep learning
                X = await self._prepare_deep_learning_data(all_data)
            elif self.config.model_type == 'transformer':
                # Prepare data for transformer models
                X = await self._prepare_transformer_data(all_data)
            else:
                raise ValueError(f"Unknown model type: {self.config.model_type}")
            
            # Encode labels
            y = self.label_encoder.fit_transform(all_labels)
            
            # Data augmentation if enabled
            if self.config.data_augmentation:
                X, y = await self._augment_data(X, y)
            
            # Feature selection if enabled
            if self.config.feature_selection and self.config.model_type == 'traditional':
                X = await self._select_features(X, y)
            
            logger.info(f"Prepared data shape: X={X.shape}, y={y.shape}")
            return X, y
            
        except Exception as e:
            logger.error(f"Data preparation failed: {e}")
            raise
    
    async def train_model(self, X: np.ndarray, y: np.ndarray) -> TrainingMetrics:
        """Train the ML model"""
        try:
            logger.info(f"Starting model training: {self.config.algorithm}")
            start_time = datetime.now()
            
            # Split data
            X_train, X_temp, y_train, y_temp = train_test_split(
                X, y, test_size=self.config.validation_split + self.config.test_split, 
                random_state=42, stratify=y
            )
            
            val_size = self.config.validation_split / (self.config.validation_split + self.config.test_split)
            X_val, X_test, y_val, y_test = train_test_split(
                X_temp, y_temp, test_size=1-val_size, random_state=42, stratify=y_temp
            )
            
            # Train based on model type
            if self.config.model_type == 'traditional':
                model, metrics = await self._train_traditional_model(X_train, y_train, X_val, y_val, X_test, y_test)
            elif self.config.model_type == 'deep_learning':
                model, metrics = await self._train_deep_learning_model(X_train, y_train, X_val, y_val, X_test, y_test)
            elif self.config.model_type == 'transformer':
                model, metrics = await self._train_transformer_model(X_train, y_train, X_val, y_val, X_test, y_test)
            
            training_time = (datetime.now() - start_time).total_seconds()
            metrics.training_time = training_time
            
            # Save model
            await self._save_model(model, metrics)
            
            # Export to ONNX if requested
            if self.config.export_onnx:
                await self._export_to_onnx(model)
            
            # Quantize model if requested
            if self.config.quantization:
                await self._quantize_model(model)
            
            self.best_model = model
            self.best_metrics = metrics
            
            logger.info(f"Model training completed in {training_time:.2f}s")
            logger.info(f"Best accuracy: {metrics.accuracy:.4f}")
            
            return metrics
            
        except Exception as e:
            logger.error(f"Model training failed: {e}")
            raise
    
    async def _train_traditional_model(self, X_train, y_train, X_val, y_val, X_test, y_test) -> Tuple[Any, TrainingMetrics]:
        """Train traditional ML model"""
        try:
            # Scale features
            X_train_scaled = self.scaler.fit_transform(X_train)
            X_val_scaled = self.scaler.transform(X_val)
            X_test_scaled = self.scaler.transform(X_test)
            
            # Initialize model
            if self.config.algorithm == 'random_forest':
                model = RandomForestClassifier(**self.config.hyperparameters, random_state=42)
            elif self.config.algorithm == 'gradient_boosting':
                model = GradientBoostingClassifier(**self.config.hyperparameters, random_state=42)
            elif self.config.algorithm == 'logistic_regression':
                model = LogisticRegression(**self.config.hyperparameters, random_state=42)
            elif self.config.algorithm == 'svm':
                model = SVC(**self.config.hyperparameters, random_state=42, probability=True)
            else:
                raise ValueError(f"Unknown algorithm: {self.config.algorithm}")
            
            # Hyperparameter tuning with cross-validation
            if self.config.cross_validation_folds > 1:
                param_grid = self._get_param_grid(self.config.algorithm)
                grid_search = GridSearchCV(
                    model, param_grid, 
                    cv=self.config.cross_validation_folds, 
                    scoring='f1_weighted',
                    n_jobs=-1
                )
                grid_search.fit(X_train_scaled, y_train)
                model = grid_search.best_estimator_
                logger.info(f"Best parameters: {grid_search.best_params_}")
            else:
                model.fit(X_train_scaled, y_train)
            
            # Evaluate model
            y_pred = model.predict(X_test_scaled)
            y_pred_proba = model.predict_proba(X_test_scaled)
            
            # Calculate metrics
            metrics = self._calculate_metrics(y_test, y_pred, y_pred_proba)
            
            return model, metrics
            
        except Exception as e:
            logger.error(f"Traditional model training failed: {e}")
            raise
    
    async def _train_deep_learning_model(self, X_train, y_train, X_val, y_val, X_test, y_test) -> Tuple[Any, TrainingMetrics]:
        """Train deep learning model"""
        try:
            # Convert to PyTorch tensors
            X_train_tensor = torch.FloatTensor(X_train)
            y_train_tensor = torch.LongTensor(y_train)
            X_val_tensor = torch.FloatTensor(X_val)
            y_val_tensor = torch.LongTensor(y_val)
            X_test_tensor = torch.FloatTensor(X_test)
            y_test_tensor = torch.LongTensor(y_test)
            
            # Create data loaders
            train_dataset = torch.utils.data.TensorDataset(X_train_tensor, y_train_tensor)
            val_dataset = torch.utils.data.TensorDataset(X_val_tensor, y_val_tensor)
            test_dataset = torch.utils.data.TensorDataset(X_test_tensor, y_test_tensor)
            
            train_loader = DataLoader(train_dataset, batch_size=self.config.batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=self.config.batch_size)
            test_loader = DataLoader(test_dataset, batch_size=self.config.batch_size)
            
            # Initialize model
            num_classes = len(np.unique(y_train))
            if self.config.algorithm == 'cnn':
                vocab_size = X_train.shape[1]  # Assuming tokenized input
                model = SecurityCNN(vocab_size, 128, num_classes)
            else:
                # Simple feedforward network
                model = nn.Sequential(
                    nn.Linear(X_train.shape[1], 512),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(512, 256),
                    nn.ReLU(),
                    nn.Dropout(0.3),
                    nn.Linear(256, num_classes)
                )
            
            # Training setup
            criterion = nn.CrossEntropyLoss()
            optimizer = optim.Adam(model.parameters(), lr=self.config.learning_rate)
            scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, patience=5)
            
            # Training loop
            train_losses = []
            val_losses = []
            best_val_loss = float('inf')
            patience_counter = 0
            
            for epoch in range(self.config.epochs):
                # Training phase
                model.train()
                train_loss = 0.0
                for batch_X, batch_y in train_loader:
                    optimizer.zero_grad()
                    outputs = model(batch_X)
                    loss = criterion(outputs, batch_y)
                    loss.backward()
                    optimizer.step()
                    train_loss += loss.item()
                
                # Validation phase
                model.eval()
                val_loss = 0.0
                with torch.no_grad():
                    for batch_X, batch_y in val_loader:
                        outputs = model(batch_X)
                        loss = criterion(outputs, batch_y)
                        val_loss += loss.item()
                
                train_loss /= len(train_loader)
                val_loss /= len(val_loader)
                
                train_losses.append(train_loss)
                val_losses.append(val_loss)
                
                scheduler.step(val_loss)
                
                # Early stopping
                if val_loss < best_val_loss:
                    best_val_loss = val_loss
                    patience_counter = 0
                    # Save best model
                    torch.save(model.state_dict(), self.models_dir / "best_model.pth")
                else:
                    patience_counter += 1
                    if patience_counter >= self.config.early_stopping_patience:
                        logger.info(f"Early stopping at epoch {epoch}")
                        break
                
                if epoch % 10 == 0:
                    logger.info(f"Epoch {epoch}: Train Loss: {train_loss:.4f}, Val Loss: {val_loss:.4f}")
            
            # Load best model
            model.load_state_dict(torch.load(self.models_dir / "best_model.pth"))
            
            # Evaluate on test set
            model.eval()
            y_pred = []
            y_pred_proba = []
            
            with torch.no_grad():
                for batch_X, batch_y in test_loader:
                    outputs = model(batch_X)
                    probabilities = torch.softmax(outputs, dim=1)
                    predictions = torch.argmax(outputs, dim=1)
                    
                    y_pred.extend(predictions.cpu().numpy())
                    y_pred_proba.extend(probabilities.cpu().numpy())
            
            # Calculate metrics
            metrics = self._calculate_metrics(y_test, np.array(y_pred), np.array(y_pred_proba))
            metrics.training_loss = train_losses
            metrics.validation_loss = val_losses
            
            return model, metrics
            
        except Exception as e:
            logger.error(f"Deep learning model training failed: {e}")
            raise
    
    async def _train_transformer_model(self, X_train, y_train, X_val, y_val, X_test, y_test) -> Tuple[Any, TrainingMetrics]:
        """Train transformer-based model"""
        try:
            # Initialize tokenizer and model
            model_name = self.config.hyperparameters.get('model_name', 'microsoft/codebert-base')
            tokenizer = AutoTokenizer.from_pretrained(model_name)
            
            # Create datasets
            train_dataset = SecurityDataset(X_train, y_train, tokenizer)
            val_dataset = SecurityDataset(X_val, y_val, tokenizer)
            test_dataset = SecurityDataset(X_test, y_test, tokenizer)
            
            # Training arguments
            training_args = TrainingArguments(
                output_dir=str(self.models_dir / "transformer"),
                num_train_epochs=self.config.epochs,
                per_device_train_batch_size=self.config.batch_size,
                per_device_eval_batch_size=self.config.batch_size,
                learning_rate=self.config.learning_rate,
                warmup_steps=500,
                weight_decay=0.01,
                logging_dir=self.config.tensorboard_log_dir,
                logging_steps=100,
                evaluation_strategy="steps",
                eval_steps=500,
                save_strategy="steps",
                save_steps=500,
                load_best_model_at_end=True,
                metric_for_best_model="eval_loss",
                greater_is_better=False,
                save_total_limit=3,
            )
            
            # Initialize model
            from transformers import AutoModelForSequenceClassification
            model = AutoModelForSequenceClassification.from_pretrained(
                model_name,
                num_labels=len(np.unique(y_train))
            )
            
            # Initialize trainer
            trainer = Trainer(
                model=model,
                args=training_args,
                train_dataset=train_dataset,
                eval_dataset=val_dataset,
                tokenizer=tokenizer,
            )
            
            # Train model
            trainer.train()
            
            # Evaluate on test set
            test_results = trainer.evaluate(test_dataset)
            
            # Get predictions
            predictions = trainer.predict(test_dataset)
            y_pred = np.argmax(predictions.predictions, axis=1)
            y_pred_proba = torch.softmax(torch.tensor(predictions.predictions), dim=1).numpy()
            
            # Calculate metrics
            metrics = self._calculate_metrics(y_test, y_pred, y_pred_proba)
            
            return model, metrics
            
        except Exception as e:
            logger.error(f"Transformer model training failed: {e}")
            raise
    
    def _calculate_metrics(self, y_true, y_pred, y_pred_proba) -> TrainingMetrics:
        """Calculate comprehensive training metrics"""
        from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
        
        accuracy = accuracy_score(y_true, y_pred)
        precision = precision_score(y_true, y_pred, average='weighted')
        recall = recall_score(y_true, y_pred, average='weighted')
        f1 = f1_score(y_true, y_pred, average='weighted')
        
        # AUC-ROC for multiclass
        try:
            auc_roc = roc_auc_score(y_true, y_pred_proba, multi_class='ovr', average='weighted')
        except:
            auc_roc = 0.0
        
        conf_matrix = confusion_matrix(y_true, y_pred).tolist()
        
        return TrainingMetrics(
            accuracy=accuracy,
            precision=precision,
            recall=recall,
            f1_score=f1,
            auc_roc=auc_roc,
            confusion_matrix=conf_matrix,
            training_loss=[],
            validation_loss=[],
            training_time=0.0,
            model_size_mb=0.0,
            inference_time_ms=0.0
        )
    
    async def _load_data_source(self, source: str) -> Tuple[List[str], List[str]]:
        """Load data from various sources"""
        try:
            if source.endswith('.csv'):
                df = pd.read_csv(source)
                return df['code'].tolist(), df['label'].tolist()
            elif source.endswith('.json'):
                with open(source, 'r') as f:
                    data = json.load(f)
                return [item['code'] for item in data], [item['label'] for item in data]
            else:
                raise ValueError(f"Unsupported data source format: {source}")
        
        except Exception as e:
            logger.error(f"Failed to load data source {source}: {e}")
            raise
    
    async def _extract_traditional_features(self, data: List[str]) -> np.ndarray:
        """Extract features for traditional ML models"""
        # TF-IDF features
        tfidf_features = self.vectorizer.fit_transform(data).toarray()
        
        # Additional security-specific features
        security_features = []
        for code in data:
            features = [
                len(code),  # Code length
                code.count('eval('),  # Eval usage
                code.count('exec('),  # Exec usage
                code.count('input('),  # User input
                code.count('sql'),  # SQL keywords
                code.count('SELECT'),  # SQL SELECT
                code.count('INSERT'),  # SQL INSERT
                code.count('UPDATE'),  # SQL UPDATE
                code.count('DELETE'),  # SQL DELETE
                code.count('password'),  # Password references
                code.count('secret'),  # Secret references
                code.count('key'),  # Key references
            ]
            security_features.append(features)
        
        # Combine features
        combined_features = np.hstack([tfidf_features, np.array(security_features)])
        return combined_features
    
    async def _prepare_deep_learning_data(self, data: List[str]) -> np.ndarray:
        """Prepare data for deep learning models"""
        # For simplicity, use TF-IDF features
        # In practice, you might use word embeddings or other representations
        return self.vectorizer.fit_transform(data).toarray()
    
    async def _prepare_transformer_data(self, data: List[str]) -> List[str]:
        """Prepare data for transformer models"""
        # Return raw text for transformer tokenization
        return data
    
    async def _augment_data(self, X: np.ndarray, y: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """Perform data augmentation"""
        # Simple data augmentation - duplicate minority classes
        from collections import Counter
        
        class_counts = Counter(y)
        max_count = max(class_counts.values())
        
        augmented_X = []
        augmented_y = []
        
        for class_label, count in class_counts.items():
            class_indices = np.where(y == class_label)[0]
            class_X = X[class_indices]
            
            # Duplicate samples to balance classes
            multiplier = max_count // count
            for _ in range(multiplier):
                augmented_X.extend(class_X)
                augmented_y.extend([class_label] * len(class_X))
        
        return np.array(augmented_X), np.array(augmented_y)
    
    async def _select_features(self, X: np.ndarray, y: np.ndarray) -> np.ndarray:
        """Perform feature selection"""
        from sklearn.feature_selection import SelectKBest, f_classif
        
        selector = SelectKBest(score_func=f_classif, k=min(1000, X.shape[1]))
        X_selected = selector.fit_transform(X, y)
        
        logger.info(f"Selected {X_selected.shape[1]} features from {X.shape[1]}")
        return X_selected
    
    def _get_param_grid(self, algorithm: str) -> Dict[str, List]:
        """Get hyperparameter grid for tuning"""
        grids = {
            'random_forest': {
                'n_estimators': [100, 200, 300],
                'max_depth': [10, 20, None],
                'min_samples_split': [2, 5, 10]
            },
            'gradient_boosting': {
                'n_estimators': [100, 200],
                'learning_rate': [0.01, 0.1, 0.2],
                'max_depth': [3, 5, 7]
            },
            'logistic_regression': {
                'C': [0.1, 1.0, 10.0],
                'penalty': ['l1', 'l2']
            },
            'svm': {
                'C': [0.1, 1.0, 10.0],
                'kernel': ['rbf', 'linear']
            }
        }
        return grids.get(algorithm, {})
    
    async def _save_model(self, model: Any, metrics: TrainingMetrics):
        """Save trained model and metadata"""
        try:
            model_path = self.models_dir / f"{self.config.algorithm}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            model_path.mkdir(exist_ok=True)
            
            # Save model
            if self.config.model_type == 'traditional':
                joblib.dump(model, model_path / "model.pkl")
                joblib.dump(self.scaler, model_path / "scaler.pkl")
                joblib.dump(self.label_encoder, model_path / "label_encoder.pkl")
                joblib.dump(self.vectorizer, model_path / "vectorizer.pkl")
            elif self.config.model_type == 'deep_learning':
                torch.save(model.state_dict(), model_path / "model.pth")
            elif self.config.model_type == 'transformer':
                model.save_pretrained(model_path)
            
            # Save metadata
            metadata = {
                'config': self.config.__dict__,
                'metrics': metrics.__dict__,
                'timestamp': datetime.now().isoformat(),
                'model_type': self.config.model_type,
                'algorithm': self.config.algorithm
            }
            
            with open(model_path / "metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2, default=str)
            
            logger.info(f"Model saved to {model_path}")
            
        except Exception as e:
            logger.error(f"Failed to save model: {e}")
            raise
    
    async def _export_to_onnx(self, model: Any):
        """Export model to ONNX format"""
        try:
            if self.config.model_type == 'deep_learning':
                # Export PyTorch model to ONNX
                dummy_input = torch.randn(1, model[0].in_features)  # Adjust based on model
                onnx_path = self.models_dir / f"{self.config.algorithm}.onnx"
                
                torch.onnx.export(
                    model,
                    dummy_input,
                    str(onnx_path),
                    export_params=True,
                    opset_version=11,
                    do_constant_folding=True,
                    input_names=['input'],
                    output_names=['output']
                )
                
                logger.info(f"Model exported to ONNX: {onnx_path}")
        
        except Exception as e:
            logger.error(f"ONNX export failed: {e}")
    
    async def _quantize_model(self, model: Any):
        """Quantize model for faster inference"""
        try:
            if self.config.model_type == 'deep_learning':
                # PyTorch quantization
                quantized_model = torch.quantization.quantize_dynamic(
                    model, {nn.Linear}, dtype=torch.qint8
                )
                
                quantized_path = self.models_dir / f"{self.config.algorithm}_quantized.pth"
                torch.save(quantized_model.state_dict(), quantized_path)
                
                logger.info(f"Quantized model saved: {quantized_path}")
        
        except Exception as e:
            logger.error(f"Model quantization failed: {e}")

# Training pipeline factory
def create_training_pipeline(config: TrainingConfig) -> ModelTrainingPipeline:
    """Create a model training pipeline with the given configuration"""
    return ModelTrainingPipeline(config)
