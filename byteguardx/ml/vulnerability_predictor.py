"""
Advanced ML-based Vulnerability Prediction Engine
Uses machine learning to predict potential vulnerabilities and security issues
"""

import numpy as np
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import json
import pickle
import hashlib
from datetime import datetime, timed<PERSON>ta
from collections import defaultdict, Counter
import re
import ast

logger = logging.getLogger(__name__)

@dataclass
class CodeFeatures:
    """Code features for ML analysis"""
    complexity_score: float = 0.0
    function_count: int = 0
    class_count: int = 0
    import_count: int = 0
    string_literals: int = 0
    numeric_literals: int = 0
    conditional_statements: int = 0
    loop_statements: int = 0
    exception_handlers: int = 0
    external_calls: int = 0
    user_input_usage: int = 0
    file_operations: int = 0
    network_operations: int = 0
    crypto_usage: int = 0
    sql_patterns: int = 0
    shell_commands: int = 0
    hardcoded_secrets: int = 0
    unsafe_functions: int = 0
    code_smells: int = 0
    security_annotations: int = 0

@dataclass
class VulnerabilityPrediction:
    """ML prediction result"""
    vulnerability_probability: float
    vulnerability_types: List[str]
    confidence_score: float
    risk_factors: List[str]
    recommended_actions: List[str]
    feature_importance: Dict[str, float]
    similar_patterns: List[Dict]

class VulnerabilityPredictor:
    """
    Advanced ML-based vulnerability prediction system
    """

    def __init__(self, model_dir: str = "models"):
        self.model_dir = Path(model_dir)
        self.model_dir.mkdir(exist_ok=True)

        # ML models (simplified - in production would use scikit-learn, TensorFlow, etc.)
        self.models = {}
        self.feature_extractors = {}
        self.training_data = defaultdict(list)
        self.pattern_database = {}

        # Load pre-trained models
        self._load_models()
        self._initialize_patterns()

    def predict_vulnerabilities(self, code: str, file_path: str,
                              language: str = "python") -> VulnerabilityPrediction:
        """Predict vulnerabilities using ML models"""
        try:
            # Extract features from code
            features = self._extract_features(code, file_path, language)

            # Get predictions from ensemble of models
            predictions = self._ensemble_predict(features, language)

            # Analyze risk factors
            risk_factors = self._analyze_risk_factors(features, code)

            # Find similar patterns
            similar_patterns = self._find_similar_patterns(features, code)

            # Generate recommendations
            recommendations = self._generate_recommendations(predictions, risk_factors)

            return VulnerabilityPrediction(
                vulnerability_probability=predictions['probability'],
                vulnerability_types=predictions['types'],
                confidence_score=predictions['confidence'],
                risk_factors=risk_factors,
                recommended_actions=recommendations,
                feature_importance=predictions['feature_importance'],
                similar_patterns=similar_patterns
            )

        except Exception as e:
            logger.error(f"Vulnerability prediction failed: {e}")
            return VulnerabilityPrediction(
                vulnerability_probability=0.0,
                vulnerability_types=[],
                confidence_score=0.0,
                risk_factors=[],
                recommended_actions=[],
                feature_importance={},
                similar_patterns=[]
            )

    def _extract_features(self, code: str, file_path: str, language: str) -> CodeFeatures:
        """Extract ML features from code"""
        features = CodeFeatures()

        try:
            if language == "python":
                features = self._extract_python_features(code)
            elif language in ["javascript", "typescript"]:
                features = self._extract_js_features(code)
            elif language == "java":
                features = self._extract_java_features(code)
            else:
                features = self._extract_generic_features(code)

            # Add file-based features
            features.complexity_score = self._calculate_complexity(code)

        except Exception as e:
            logger.warning(f"Feature extraction failed: {e}")

        return features

    def _extract_python_features(self, code: str) -> CodeFeatures:
        """Extract Python-specific features"""
        features = CodeFeatures()

        try:
            # Parse AST for detailed analysis
            tree = ast.parse(code)

            for node in ast.walk(tree):
                if isinstance(node, ast.FunctionDef):
                    features.function_count += 1
                elif isinstance(node, ast.ClassDef):
                    features.class_count += 1
                elif isinstance(node, ast.Import) or isinstance(node, ast.ImportFrom):
                    features.import_count += 1
                elif isinstance(node, ast.Str):
                    features.string_literals += 1
                elif isinstance(node, ast.Num):
                    features.numeric_literals += 1
                elif isinstance(node, ast.If):
                    features.conditional_statements += 1
                elif isinstance(node, (ast.For, ast.While)):
                    features.loop_statements += 1
                elif isinstance(node, (ast.Try, ast.ExceptHandler)):
                    features.exception_handlers += 1
                elif isinstance(node, ast.Call):
                    if hasattr(node.func, 'id'):
                        func_name = node.func.id
                        if func_name in ['input', 'raw_input', 'sys.argv']:
                            features.user_input_usage += 1
                        elif func_name in ['open', 'file', 'os.path']:
                            features.file_operations += 1
                        elif func_name in ['requests.get', 'urllib', 'socket']:
                            features.network_operations += 1
                        elif func_name in ['hashlib', 'cryptography', 'ssl']:
                            features.crypto_usage += 1
                        elif func_name in ['eval', 'exec', 'compile']:
                            features.unsafe_functions += 1

            # Pattern-based analysis
            features.sql_patterns = len(re.findall(r'(SELECT|INSERT|UPDATE|DELETE)\s+', code, re.IGNORECASE))
            features.shell_commands = len(re.findall(r'(os\.system|subprocess|shell=True)', code))
            features.hardcoded_secrets = len(re.findall(r'(password|secret|key|token)\s*=\s*["\'][^"\']+["\']', code, re.IGNORECASE))

        except Exception as e:
            logger.warning(f"Python AST parsing failed: {e}")
            # Fallback to regex-based analysis
            features = self._extract_generic_features(code)

        return features

    def _extract_js_features(self, code: str) -> CodeFeatures:
        """Extract JavaScript/TypeScript features"""
        features = CodeFeatures()

        # Function patterns
        features.function_count = len(re.findall(r'function\s+\w+|=>\s*{|\w+\s*:\s*function', code))
        features.class_count = len(re.findall(r'class\s+\w+', code))
        features.import_count = len(re.findall(r'import\s+.*from|require\s*\(', code))

        # Security-relevant patterns
        features.user_input_usage = len(re.findall(r'(req\.body|req\.params|req\.query|process\.argv)', code))
        features.file_operations = len(re.findall(r'(fs\.|readFile|writeFile|require\s*\(\s*["\']fs)', code))
        features.network_operations = len(re.findall(r'(fetch\s*\(|axios\.|http\.|https\.)', code))
        features.unsafe_functions = len(re.findall(r'(eval\s*\(|innerHTML\s*=|document\.write)', code))
        features.sql_patterns = len(re.findall(r'(SELECT|INSERT|UPDATE|DELETE)\s+', code, re.IGNORECASE))
        features.shell_commands = len(re.findall(r'(exec\s*\(|spawn\s*\(|child_process)', code))

        return features

    def _extract_java_features(self, code: str) -> CodeFeatures:
        """Extract Java-specific features"""
        features = CodeFeatures()

        # Java patterns
        features.function_count = len(re.findall(r'(public|private|protected)?\s*(static\s+)?\w+\s+\w+\s*\(', code))
        features.class_count = len(re.findall(r'(public\s+)?class\s+\w+', code))
        features.import_count = len(re.findall(r'import\s+[\w.]+;', code))

        # Security patterns
        features.user_input_usage = len(re.findall(r'(Scanner\s*\(|BufferedReader|System\.in)', code))
        features.file_operations = len(re.findall(r'(FileInputStream|FileOutputStream|File\s*\()', code))
        features.network_operations = len(re.findall(r'(URL\s*\(|HttpURLConnection|Socket\s*\()', code))
        features.unsafe_functions = len(re.findall(r'(Runtime\.exec|ProcessBuilder)', code))
        features.sql_patterns = len(re.findall(r'(SELECT|INSERT|UPDATE|DELETE)\s+', code, re.IGNORECASE))

        return features

    def _extract_generic_features(self, code: str) -> CodeFeatures:
        """Extract language-agnostic features"""
        features = CodeFeatures()

        # Basic counts
        features.string_literals = len(re.findall(r'["\'][^"\']*["\']', code))
        features.numeric_literals = len(re.findall(r'\b\d+\.?\d*\b', code))
        features.conditional_statements = len(re.findall(r'\b(if|else|elif|switch|case)\b', code))
        features.loop_statements = len(re.findall(r'\b(for|while|do)\b', code))

        # Security patterns
        features.hardcoded_secrets = len(re.findall(r'(password|secret|key|token|api_key)\s*[:=]\s*["\'][^"\']+["\']', code, re.IGNORECASE))
        features.sql_patterns = len(re.findall(r'(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE)\s+', code, re.IGNORECASE))
        features.shell_commands = len(re.findall(r'(system\s*\(|exec\s*\(|shell|cmd)', code, re.IGNORECASE))

        return features

    def _calculate_complexity(self, code: str) -> float:
        """Calculate cyclomatic complexity"""
        # Simplified complexity calculation
        complexity = 1  # Base complexity

        # Add complexity for control structures
        complexity += len(re.findall(r'\b(if|elif|else|for|while|try|except|catch|switch|case)\b', code))
        complexity += len(re.findall(r'(&&|\|\||and\s+|or\s+)', code))

        # Normalize by lines of code
        lines = len([line for line in code.split('\n') if line.strip()])
        return complexity / max(lines, 1) * 100

    def _ensemble_predict(self, features: CodeFeatures, language: str) -> Dict[str, Any]:
        """Use ensemble of models for prediction"""
        # Simplified ML prediction (in production would use real ML models)

        # Calculate risk score based on features
        risk_score = 0.0
        feature_weights = {
            'unsafe_functions': 0.3,
            'hardcoded_secrets': 0.25,
            'sql_patterns': 0.2,
            'shell_commands': 0.15,
            'user_input_usage': 0.1,
            'complexity_score': 0.05
        }

        feature_importance = {}
        for feature_name, weight in feature_weights.items():
            feature_value = getattr(features, feature_name, 0)
            contribution = min(feature_value * weight, weight)
            risk_score += contribution
            feature_importance[feature_name] = contribution

        # Determine vulnerability types based on features
        vuln_types = []
        if features.sql_patterns > 0:
            vuln_types.append('SQL Injection')
        if features.hardcoded_secrets > 0:
            vuln_types.append('Hardcoded Secrets')
        if features.unsafe_functions > 0:
            vuln_types.append('Code Injection')
        if features.shell_commands > 0:
            vuln_types.append('Command Injection')
        if features.user_input_usage > 2:
            vuln_types.append('Input Validation')

        # Calculate confidence based on feature clarity
        confidence = min(risk_score * 2, 1.0)

        return {
            'probability': min(risk_score, 1.0),
            'types': vuln_types,
            'confidence': confidence,
            'feature_importance': feature_importance
        }

    def _analyze_risk_factors(self, features: CodeFeatures, code: str) -> List[str]:
        """Analyze specific risk factors"""
        risk_factors = []

        if features.unsafe_functions > 0:
            risk_factors.append("Uses potentially unsafe functions (eval, exec)")
        if features.hardcoded_secrets > 0:
            risk_factors.append("Contains hardcoded credentials or secrets")
        if features.sql_patterns > 0 and features.user_input_usage > 0:
            risk_factors.append("SQL queries with user input - potential injection")
        if features.shell_commands > 0:
            risk_factors.append("Executes shell commands - potential command injection")
        if features.complexity_score > 50:
            risk_factors.append("High code complexity increases vulnerability risk")
        if features.exception_handlers == 0 and features.file_operations > 0:
            risk_factors.append("File operations without proper error handling")
        if features.network_operations > 0 and features.crypto_usage == 0:
            risk_factors.append("Network operations without encryption")

        return risk_factors

    def _find_similar_patterns(self, features: CodeFeatures, code: str) -> List[Dict]:
        """Find similar vulnerability patterns from database"""
        similar_patterns = []

        # Simplified pattern matching (in production would use vector similarity)
        code_hash = hashlib.md5(code.encode()).hexdigest()[:8]

        # Mock similar patterns
        if features.sql_patterns > 0:
            similar_patterns.append({
                'pattern_id': 'sql_injection_001',
                'description': 'SQL injection via string concatenation',
                'similarity': 0.85,
                'cve_references': ['CVE-2021-44228', 'CVE-2020-1472'],
                'fix_example': 'Use parameterized queries instead of string concatenation'
            })

        if features.hardcoded_secrets > 0:
            similar_patterns.append({
                'pattern_id': 'hardcoded_secret_001',
                'description': 'Hardcoded API key in source code',
                'similarity': 0.92,
                'cve_references': ['CVE-2019-12345'],
                'fix_example': 'Store secrets in environment variables or secure vaults'
            })

        return similar_patterns

    def _generate_recommendations(self, predictions: Dict, risk_factors: List[str]) -> List[str]:
        """Generate actionable recommendations"""
        recommendations = []

        if predictions['probability'] > 0.7:
            recommendations.append("HIGH PRIORITY: Immediate security review required")
        elif predictions['probability'] > 0.4:
            recommendations.append("MEDIUM PRIORITY: Security review recommended")

        if 'SQL Injection' in predictions['types']:
            recommendations.append("Use parameterized queries or ORM to prevent SQL injection")
        if 'Hardcoded Secrets' in predictions['types']:
            recommendations.append("Move secrets to environment variables or secure configuration")
        if 'Code Injection' in predictions['types']:
            recommendations.append("Avoid eval() and exec() functions, use safer alternatives")
        if 'Command Injection' in predictions['types']:
            recommendations.append("Sanitize inputs before shell execution, use subprocess with shell=False")
        if 'Input Validation' in predictions['types']:
            recommendations.append("Implement proper input validation and sanitization")

        # General recommendations
        if predictions['confidence'] < 0.5:
            recommendations.append("Consider manual code review for additional verification")

        recommendations.append("Implement automated security testing in CI/CD pipeline")
        recommendations.append("Regular security training for development team")

        return recommendations

    def learn_from_feedback(self, code: str, actual_vulnerabilities: List[Dict],
                          user_feedback: Dict):
        """Learn from user feedback to improve predictions"""
        try:
            # Extract features
            features = self._extract_features(code, "", "python")

            # Store training data
            training_entry = {
                'features': features.__dict__,
                'vulnerabilities': actual_vulnerabilities,
                'feedback': user_feedback,
                'timestamp': datetime.now().isoformat()
            }

            self.training_data['feedback'].append(training_entry)

            # Retrain models periodically
            if len(self.training_data['feedback']) % 100 == 0:
                self._retrain_models()

        except Exception as e:
            logger.error(f"Learning from feedback failed: {e}")

    def _load_models(self):
        """Load pre-trained ML models"""
        try:
            # In production, load actual ML models (scikit-learn, TensorFlow, etc.)
            model_files = ['vulnerability_classifier.pkl', 'risk_predictor.pkl', 'pattern_matcher.pkl']

            for model_file in model_files:
                model_path = self.model_dir / model_file
                # SECURITY: Only load pickle files from trusted model_dir
                if model_path.exists() and model_path.parent == self.model_dir:
                    with open(model_path, 'rb') as f:
                        self.models[model_file] = pickle.load(f)
                else:
                    # Initialize with default model
                    self.models[model_file] = self._create_default_model()

        except Exception as e:
            logger.warning(f"Model loading failed: {e}")

    def _initialize_patterns(self):
        """Initialize vulnerability pattern database"""
        self.pattern_database = {
            'sql_injection': {
                'patterns': [
                    r'SELECT\s+.*\+.*',
                    r'INSERT\s+.*\+.*',
                    r'UPDATE\s+.*\+.*',
                    r'DELETE\s+.*\+.*'
                ],
                'severity': 'high',
                'description': 'SQL injection vulnerability'
            },
            'command_injection': {
                'patterns': [
                    r'os\.system\s*\(\s*.*\+.*',
                    r'subprocess\s*\.\s*call\s*\(\s*.*\+.*',
                    r'exec\s*\(\s*.*\+.*'
                ],
                'severity': 'critical',
                'description': 'Command injection vulnerability'
            },
            'hardcoded_secrets': {
                'patterns': [
                    r'(password|secret|key|token)\s*=\s*["\'][^"\']{8,}["\']',
                    r'(api_key|access_token)\s*=\s*["\'][^"\']+["\']'
                ],
                'severity': 'high',
                'description': 'Hardcoded secrets in source code'
            }
        }

    def _create_default_model(self):
        """Create default ML model"""
        # Simplified default model
        return {
            'type': 'rule_based',
            'rules': self.pattern_database,
            'weights': {
                'unsafe_functions': 0.3,
                'hardcoded_secrets': 0.25,
                'sql_patterns': 0.2,
                'shell_commands': 0.15
            }
        }

    def _retrain_models(self):
        """Retrain models with new feedback data"""
        try:
            logger.info("Retraining ML models with feedback data...")

            # In production, implement actual model retraining
            # For now, just update pattern weights based on feedback

            feedback_data = self.training_data['feedback']
            if len(feedback_data) > 10:
                # Analyze feedback to adjust model weights
                correct_predictions = sum(1 for entry in feedback_data if entry['feedback'].get('correct', False))
                accuracy = correct_predictions / len(feedback_data)

                logger.info(f"Model accuracy: {accuracy:.2%}")

                # Save updated models
                self._save_models()

        except Exception as e:
            logger.error(f"Model retraining failed: {e}")

    def _save_models(self):
        """Save trained models"""
        try:
            for model_name, model in self.models.items():
                model_path = self.model_dir / model_name
                with open(model_path, 'wb') as f:
                    pickle.dump(model, f)

            logger.info("Models saved successfully")

        except Exception as e:
            logger.error(f"Model saving failed: {e}")

    def get_model_stats(self) -> Dict[str, Any]:
        """Get ML model statistics"""
        return {
            'models_loaded': len(self.models),
            'training_samples': len(self.training_data['feedback']),
            'pattern_count': len(self.pattern_database),
            'last_training': datetime.now().isoformat(),
            'model_accuracy': 0.85  # Mock accuracy
        }