"""
ByteGuardX Enhanced CLI
Comprehensive command-line interface with AI co-design features
"""

import click
import json
import asyncio
import sys
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
import yaml
import toml
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, SpinnerColumn, TextColumn
from rich.panel import Panel
from rich.syntax import Syntax
from rich.prompt import Prompt, Confirm
import questionary

from byteguardx.core.system_architecture import byteguardx_core
from byteguardx.ai.inference_engine import AIInferenceEngine
from byteguardx.plugins.enhanced_plugin_manager import EnhancedPluginManager
from byteguardx.git.git_integration import GitIntegration
from byteguardx.dashboard.interactive_dashboard import dashboard

console = Console()

@click.group()
@click.option('--config', default='~/.byteguardx/config.toml', help='Configuration file path')
@click.option('--verbose', '-v', is_flag=True, help='Verbose output')
@click.option('--debug', is_flag=True, help='Debug mode')
@click.pass_context
def cli(ctx, config, verbose, debug):
    """ByteGuardX - AI-Powered Security Scanner"""
    ctx.ensure_object(dict)
    ctx.obj['config'] = Path(config).expanduser()
    ctx.obj['verbose'] = verbose
    ctx.obj['debug'] = debug
    
    if debug:
        import logging
        logging.basicConfig(level=logging.DEBUG)

@cli.command()
@click.argument('path', type=click.Path(exists=True))
@click.option('--format', type=click.Choice(['json', 'table', 'sarif', 'yaml']), default='table')
@click.option('--output', '-o', type=click.Path(), help='Output file path')
@click.option('--severity', type=click.Choice(['critical', 'high', 'medium', 'low']), 
              help='Minimum severity level')
@click.option('--staged', is_flag=True, help='Scan only staged Git changes')
@click.option('--interactive', is_flag=True, help='Interactive mode with AI assistance')
@click.option('--explain', is_flag=True, help='Include AI explanations')
@click.option('--fix', is_flag=True, help='Suggest automated fixes')
@click.pass_context
def scan(ctx, path, format, output, severity, staged, interactive, explain, fix):
    """Scan files or directories for security vulnerabilities"""
    
    async def run_scan():
        try:
            # Initialize system
            if not byteguardx_core.is_initialized:
                with Progress(
                    SpinnerColumn(),
                    TextColumn("[progress.description]{task.description}"),
                    console=console
                ) as progress:
                    task = progress.add_task("Initializing ByteGuardX...", total=None)
                    await byteguardx_core.initialize_system()
                    progress.update(task, completed=True)
            
            # Setup Git integration if scanning staged changes
            if staged:
                git_integration = GitIntegration(path)
                if not await git_integration.initialize():
                    console.print("[red]Error: Not a Git repository[/red]")
                    return
                
                scan_results = await git_integration.scan_staged_changes()
                findings = []
                for result in scan_results:
                    findings.extend(result.findings)
            else:
                # Regular file/directory scan
                if Path(path).is_file():
                    with open(path, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    
                    code_input = {
                        'content': content,
                        'file_path': str(path),
                        'metadata': {'language': _detect_language(path)}
                    }
                    
                    findings = await byteguardx_core.complete_scan_flow(code_input)
                else:
                    # Directory scan
                    findings = await _scan_directory(path)
            
            # Filter by severity
            if severity:
                severity_order = {'critical': 4, 'high': 3, 'medium': 2, 'low': 1}
                min_level = severity_order[severity]
                findings = [f for f in findings if severity_order.get(f.severity, 0) >= min_level]
            
            # Interactive mode
            if interactive:
                await _interactive_scan_review(findings, explain, fix)
            else:
                # Output results
                output_data = _format_scan_results(findings, format, explain)
                
                if output:
                    Path(output).write_text(output_data)
                    console.print(f"[green]Results written to {output}[/green]")
                else:
                    console.print(output_data)
            
        except Exception as e:
            console.print(f"[red]Scan failed: {e}[/red]")
            if ctx.obj['debug']:
                import traceback
                console.print(traceback.format_exc())
    
    asyncio.run(run_scan())

@cli.command()
@click.option('--port', default=8080, help='Dashboard port')
@click.option('--host', default='127.0.0.1', help='Dashboard host')
@click.option('--open-browser', is_flag=True, help='Open browser automatically')
def dashboard_cmd(port, host, open_browser):
    """Launch interactive security dashboard"""
    try:
        console.print(f"[cyan]Starting ByteGuardX Dashboard at http://{host}:{port}[/cyan]")
        
        if open_browser:
            import webbrowser
            webbrowser.open(f"http://{host}:{port}")
        
        dashboard.run(host=host, debug=False)
        
    except KeyboardInterrupt:
        console.print("\n[yellow]Dashboard stopped[/yellow]")
    except Exception as e:
        console.print(f"[red]Dashboard failed to start: {e}[/red]")

@cli.command()
@click.argument('plugin_path', type=click.Path(exists=True))
@click.option('--trust', is_flag=True, help='Mark plugin as trusted')
@click.option('--verify', is_flag=True, help='Verify plugin signature')
def install_plugin(plugin_path, trust, verify):
    """Install a security scanning plugin"""
    
    async def run_install():
        try:
            plugin_manager = EnhancedPluginManager()
            await plugin_manager.initialize(None)  # Trust manager would be injected
            
            with Progress(
                SpinnerColumn(),
                TextColumn("[progress.description]{task.description}"),
                console=console
            ) as progress:
                task = progress.add_task("Installing plugin...", total=None)
                
                plugin_info = await plugin_manager.install_plugin(
                    Path(plugin_path), 
                    trusted=trust
                )
                
                progress.update(task, completed=True)
            
            console.print(f"[green]✅ Installed plugin: {plugin_info.manifest.name} v{plugin_info.manifest.version}[/green]")
            
            # Show plugin details
            _display_plugin_info(plugin_info)
            
        except Exception as e:
            console.print(f"[red]❌ Plugin installation failed: {e}[/red]")
    
    asyncio.run(run_install())

@cli.command()
def list_plugins():
    """List all installed plugins"""
    
    async def run_list():
        try:
            plugin_manager = EnhancedPluginManager()
            await plugin_manager.initialize(None)
            
            plugins = plugin_manager.list_plugins()
            
            if not plugins:
                console.print("[yellow]No plugins installed[/yellow]")
                return
            
            table = Table(title="Installed Plugins")
            table.add_column("Name", style="cyan")
            table.add_column("Version", style="green")
            table.add_column("Author", style="blue")
            table.add_column("Status", style="yellow")
            table.add_column("Security Level", style="red")
            
            for name, info in plugins.items():
                status = "✅ Loaded" if info['is_loaded'] else "❌ Not Loaded"
                if info['is_trusted']:
                    status += " (Trusted)"
                
                table.add_row(
                    info['name'],
                    info['version'],
                    info['author'],
                    status,
                    info['security_level']
                )
            
            console.print(table)
            
        except Exception as e:
            console.print(f"[red]Failed to list plugins: {e}[/red]")
    
    asyncio.run(run_list())

@cli.command()
@click.option('--init-git', is_flag=True, help='Initialize Git hooks')
@click.option('--create-config', is_flag=True, help='Create default configuration')
def init(init_git, create_config):
    """Initialize ByteGuardX in current directory"""
    
    async def run_init():
        try:
            current_dir = Path.cwd()
            console.print(f"[cyan]Initializing ByteGuardX in {current_dir}[/cyan]")
            
            # Create configuration
            if create_config or not (current_dir / '.byteguardx.toml').exists():
                await _create_default_config(current_dir)
            
            # Initialize Git hooks
            if init_git:
                git_integration = GitIntegration(current_dir)
                if await git_integration.initialize():
                    hooks_installed = await git_integration.install_hooks()
                    
                    for hook, success in hooks_installed.items():
                        if success:
                            console.print(f"[green]✅ Installed {hook} hook[/green]")
                        else:
                            console.print(f"[red]❌ Failed to install {hook} hook[/red]")
                else:
                    console.print("[yellow]⚠️  Not a Git repository - skipping hook installation[/yellow]")
            
            console.print("[green]✅ ByteGuardX initialization complete[/green]")
            
        except Exception as e:
            console.print(f"[red]Initialization failed: {e}[/red]")
    
    asyncio.run(run_init())

@cli.command()
@click.argument('description', required=True)
@click.option('--language', default='python', help='Target programming language')
@click.option('--output-dir', default='generated_plugin', help='Output directory')
def generate_plugin(description, language, output_dir):
    """Generate plugin using AI assistance"""
    
    async def run_generate():
        try:
            console.print(f"[cyan]Generating plugin: {description}[/cyan]")
            
            # This would integrate with AI models for code generation
            # For now, create a basic template
            
            plugin_dir = Path(output_dir)
            plugin_dir.mkdir(exist_ok=True)
            
            # Create plugin manifest
            manifest = {
                'metadata': {
                    'name': description.lower().replace(' ', '_'),
                    'version': '1.0.0',
                    'description': description,
                    'author': 'AI Generated',
                    'license': 'MIT',
                    'homepage': ''
                },
                'build': {
                    'content_hash': 'sha256:placeholder',
                    'build_timestamp': datetime.now().isoformat(),
                    'reproducible_build': True,
                    'build_environment': f'{language}-env'
                },
                'security': {
                    'signing_key_id': 'ed25519:placeholder',
                    'signature': 'ed25519:placeholder',
                    'trusted': False,
                    'security_level': 'medium'
                },
                'capabilities': {
                    'filesystem': ['read'],
                    'network': ['none'],
                    'models': [],
                    'memory_limit_mb': 256,
                    'cpu_limit_percent': 10,
                    'execution_timeout_seconds': 60
                },
                'runtime': {
                    'entrypoint': 'src/main.py',
                    'python_version': '>=3.9,<4.0',
                    'dependencies': []
                },
                'api': {
                    'input_schema': 'schemas/input.json',
                    'output_schema': 'schemas/output.json',
                    'ipc_version': '1.2'
                }
            }
            
            # Write manifest
            with open(plugin_dir / 'plugin.toml', 'w') as f:
                toml.dump(manifest, f)
            
            # Create basic plugin structure
            (plugin_dir / 'src').mkdir(exist_ok=True)
            (plugin_dir / 'schemas').mkdir(exist_ok=True)
            (plugin_dir / 'tests').mkdir(exist_ok=True)
            
            # Create main plugin file
            plugin_code = f'''"""
AI-Generated Plugin: {description}
"""

import json
import sys
from typing import Dict, List, Any

def scan_content(content: str, file_path: str) -> List[Dict[str, Any]]:
    """
    Scan content for security issues
    
    Args:
        content: File content to scan
        file_path: Path to the file being scanned
    
    Returns:
        List of findings
    """
    findings = []
    
    # TODO: Implement actual scanning logic
    # This is a placeholder implementation
    
    if "password" in content.lower():
        findings.append({{
            "id": "hardcoded_password",
            "type": "secret",
            "severity": "high",
            "title": "Potential hardcoded password",
            "description": "Found potential hardcoded password in code",
            "file_path": file_path,
            "line_start": 1,
            "line_end": 1,
            "column_start": 1,
            "column_end": 10,
            "confidence": 0.8,
            "evidence": "password string found",
            "remediation": "Use environment variables or secure configuration",
            "references": ["https://owasp.org/www-community/vulnerabilities/Use_of_hard-coded_password"]
        }})
    
    return findings

def main():
    """Main plugin entry point"""
    try:
        # Read input from stdin
        input_data = json.loads(sys.stdin.read())
        
        # Extract content and file path
        content = input_data.get('content', '')
        file_path = input_data.get('file_path', '')
        
        # Perform scan
        findings = scan_content(content, file_path)
        
        # Output results
        result = {{
            'scan_id': input_data.get('scan_id'),
            'plugin_name': '{manifest["metadata"]["name"]}',
            'plugin_version': '{manifest["metadata"]["version"]}',
            'execution_time_ms': 100,  # Placeholder
            'findings': findings,
            'errors': [],
            'metrics': {{
                'lines_scanned': len(content.split('\\n')),
                'patterns_matched': len(findings)
            }}
        }}
        
        print(json.dumps(result))
        
    except Exception as e:
        error_result = {{
            'scan_id': 'unknown',
            'plugin_name': '{manifest["metadata"]["name"]}',
            'plugin_version': '{manifest["metadata"]["version"]}',
            'execution_time_ms': 0,
            'findings': [],
            'errors': [{{
                'code': 'PLUGIN_ERROR',
                'message': str(e)
            }}],
            'metrics': {{}}
        }}
        
        print(json.dumps(error_result))
        sys.exit(1)

if __name__ == '__main__':
    main()
'''
            
            with open(plugin_dir / 'src' / 'main.py', 'w') as f:
                f.write(plugin_code)
            
            console.print(f"[green]✅ Plugin generated in {plugin_dir}[/green]")
            console.print("[yellow]⚠️  Remember to implement actual scanning logic and test thoroughly[/yellow]")
            
        except Exception as e:
            console.print(f"[red]Plugin generation failed: {e}[/red]")
    
    asyncio.run(run_generate())

@cli.command()
@click.argument('format', type=click.Choice(['json', 'pdf', 'html', 'sarif']))
@click.option('--output', '-o', required=True, help='Output file path')
@click.option('--template', help='Report template to use')
def export_report(format, output, template):
    """Export security report in various formats"""
    console.print(f"[cyan]Exporting report in {format} format to {output}[/cyan]")
    
    # This would integrate with the reporting system
    console.print("[yellow]Report export functionality not yet implemented[/yellow]")

def _detect_language(file_path: str) -> str:
    """Detect programming language from file extension"""
    extension_map = {
        '.py': 'python',
        '.js': 'javascript',
        '.ts': 'typescript',
        '.java': 'java',
        '.go': 'go',
        '.rs': 'rust',
        '.cpp': 'cpp',
        '.c': 'c',
        '.cs': 'csharp',
        '.php': 'php',
        '.rb': 'ruby'
    }
    
    suffix = Path(file_path).suffix.lower()
    return extension_map.get(suffix, 'unknown')

async def _scan_directory(directory_path: str) -> List[Any]:
    """Scan all files in directory"""
    findings = []
    directory = Path(directory_path)
    
    # Get all supported files
    supported_extensions = {'.py', '.js', '.ts', '.java', '.go', '.rs', '.cpp', '.c', '.cs'}
    files = [f for f in directory.rglob('*') if f.suffix.lower() in supported_extensions]
    
    with Progress(console=console) as progress:
        task = progress.add_task("Scanning files...", total=len(files))
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                
                code_input = {
                    'content': content,
                    'file_path': str(file_path),
                    'metadata': {'language': _detect_language(str(file_path))}
                }
                
                file_findings = await byteguardx_core.complete_scan_flow(code_input)
                findings.extend(file_findings)
                
            except Exception as e:
                console.print(f"[red]Error scanning {file_path}: {e}[/red]")
            
            progress.advance(task)
    
    return findings

def _format_scan_results(findings: List[Any], format: str, include_explanations: bool = False) -> str:
    """Format scan results for output"""
    if format == 'json':
        data = []
        for finding in findings:
            finding_dict = {
                'id': finding.id,
                'type': finding.type,
                'severity': finding.severity,
                'title': finding.title,
                'description': finding.description,
                'file_path': finding.file_path,
                'line_start': finding.line_start,
                'line_end': finding.line_end,
                'confidence': finding.confidence,
                'remediation': finding.remediation
            }
            
            if include_explanations and hasattr(finding, 'explanation'):
                finding_dict['explanation'] = finding.explanation
            
            data.append(finding_dict)
        
        return json.dumps({'findings': data}, indent=2)
    
    elif format == 'table':
        if not findings:
            return "[green]✅ No security issues found[/green]"
        
        table = Table(title=f"Security Scan Results ({len(findings)} findings)")
        table.add_column("File", style="cyan")
        table.add_column("Line", style="yellow")
        table.add_column("Severity", style="red")
        table.add_column("Type", style="blue")
        table.add_column("Description", style="white")
        
        for finding in findings[:20]:  # Limit to first 20 for readability
            severity_color = {
                'critical': '[red]',
                'high': '[orange1]',
                'medium': '[yellow]',
                'low': '[green]'
            }.get(finding.severity, '[white]')
            
            table.add_row(
                finding.file_path,
                str(finding.line_start),
                f"{severity_color}{finding.severity}[/]",
                finding.type,
                finding.title[:50] + "..." if len(finding.title) > 50 else finding.title
            )
        
        if len(findings) > 20:
            table.add_row("...", "...", "...", "...", f"... and {len(findings) - 20} more")
        
        with console.capture() as capture:
            console.print(table)
        
        return capture.get()
    
    else:
        return f"Format {format} not implemented"

async def _interactive_scan_review(findings: List[Any], explain: bool, fix: bool):
    """Interactive review of scan findings with AI assistance"""
    if not findings:
        console.print("[green]✅ No security issues found[/green]")
        return
    
    console.print(f"[cyan]Found {len(findings)} security findings[/cyan]")
    
    for i, finding in enumerate(findings):
        console.print(f"\n[bold]Finding {i+1}/{len(findings)}[/bold]")
        
        panel_content = f"""
[red]{finding.severity.upper()}[/red] - {finding.type}
[bold]{finding.title}[/bold]

{finding.description}

File: {finding.file_path}:{finding.line_start}
Confidence: {finding.confidence:.1%}
        """
        
        console.print(Panel(panel_content, title="Security Finding"))
        
        # Show code context if available
        if hasattr(finding, 'evidence') and finding.evidence:
            syntax = Syntax(finding.evidence, _detect_language(finding.file_path), theme="monokai")
            console.print(Panel(syntax, title="Code Context"))
        
        # Interactive options
        action = questionary.select(
            "What would you like to do?",
            choices=[
                "Next finding",
                "Show explanation" if explain else None,
                "Show remediation",
                "Auto-fix" if fix else None,
                "Skip remaining",
                "Exit"
            ]
        ).ask()
        
        if action == "Show explanation" and explain:
            if hasattr(finding, 'explanation') and finding.explanation:
                console.print(Panel(str(finding.explanation), title="AI Explanation"))
            else:
                console.print("[yellow]No explanation available[/yellow]")
        
        elif action == "Show remediation":
            console.print(Panel(finding.remediation, title="Remediation"))
        
        elif action == "Auto-fix" and fix:
            console.print("[yellow]Auto-fix functionality not yet implemented[/yellow]")
        
        elif action == "Skip remaining":
            break
        
        elif action == "Exit":
            return

async def _create_default_config(directory: Path):
    """Create default configuration file"""
    config = {
        'system': {
            'max_concurrent_scans': 10,
            'cache_size': 1000,
            'audit_retention_days': 90
        },
        'security': {
            'require_plugin_signatures': True,
            'sandbox_timeout_seconds': 300,
            'trust_level_required': 'medium'
        },
        'ai': {
            'enable_explainability': True,
            'confidence_threshold': 0.7,
            'max_inference_time_seconds': 60
        },
        'scanning': {
            'default_severity': 'medium',
            'exclude_patterns': [
                'node_modules/**',
                '.git/**',
                '**/*.min.js',
                '**/vendor/**'
            ],
            'include_extensions': ['.py', '.js', '.ts', '.java', '.go', '.rs']
        }
    }
    
    config_file = directory / '.byteguardx.toml'
    with open(config_file, 'w') as f:
        toml.dump(config, f)
    
    console.print(f"[green]✅ Created configuration file: {config_file}[/green]")

def _display_plugin_info(plugin_info):
    """Display plugin information"""
    manifest = plugin_info.manifest
    
    info_text = f"""
Name: {manifest.name}
Version: {manifest.version}
Author: {manifest.author}
Description: {manifest.description}
Security Level: {manifest.security_level}
Trusted: {'Yes' if plugin_info.is_trusted else 'No'}
Capabilities: {', '.join(manifest.filesystem + manifest.network)}
    """
    
    console.print(Panel(info_text.strip(), title="Plugin Information"))

if __name__ == '__main__':
    cli()
