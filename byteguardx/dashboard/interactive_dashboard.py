"""
ByteGuardX Interactive Dashboard
Real-time security analytics with glassmorphism UI and comprehensive visualizations
"""

import logging
import json
import asyncio
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
from pathlib import Path
import pandas as pd
import numpy as np
from flask import Flask, render_template, jsonify, request, send_from_directory
from flask_socketio import Socket<PERSON>, emit
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.utils

logger = logging.getLogger(__name__)

class InteractiveDashboard:
    """
    Interactive security dashboard with real-time updates and glassmorphism design
    """
    
    def __init__(self, data_dir: str = "data/dashboard", port: int = 8080):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.port = port
        
        # Flask app setup
        self.app = Flask(__name__, 
                        template_folder='templates',
                        static_folder='static')
        self.app.config['SECRET_KEY'] = 'byteguardx-dashboard-secret'
        
        # SocketIO for real-time updates
        self.socketio = SocketIO(self.app, cors_allowed_origins="*")
        
        # Dashboard data
        self.scan_data = []
        self.vulnerability_trends = []
        self.performance_metrics = []
        self.security_events = []
        
        # Configuration
        self.config = {
            'refresh_interval': 30000,  # 30 seconds
            'max_data_points': 1000,
            'color_scheme': {
                'primary': '#00bcd4',
                'secondary': '#00ffff',
                'background': '#000000',
                'surface': '#1a1a1a',
                'text': '#ffffff',
                'success': '#4caf50',
                'warning': '#ff9800',
                'error': '#f44336'
            }
        }
        
        self._setup_routes()
        self._setup_socketio_events()
        
        logger.info("Interactive Dashboard initialized")
    
    def _setup_routes(self):
        """Setup Flask routes"""
        
        @self.app.route('/')
        def dashboard():
            """Main dashboard page"""
            return render_template('dashboard.html', config=self.config)
        
        @self.app.route('/api/scan-data')
        def get_scan_data():
            """Get scan data for dashboard"""
            return jsonify({
                'scan_data': self.scan_data[-100:],  # Last 100 scans
                'total_scans': len(self.scan_data),
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/vulnerability-trends')
        def get_vulnerability_trends():
            """Get vulnerability trends data"""
            return jsonify({
                'trends': self.vulnerability_trends[-50:],  # Last 50 data points
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/performance-metrics')
        def get_performance_metrics():
            """Get performance metrics"""
            return jsonify({
                'metrics': self.performance_metrics[-100:],
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/security-events')
        def get_security_events():
            """Get security events"""
            return jsonify({
                'events': self.security_events[-50:],
                'timestamp': datetime.now().isoformat()
            })
        
        @self.app.route('/api/dashboard-summary')
        def get_dashboard_summary():
            """Get dashboard summary statistics"""
            summary = self._calculate_dashboard_summary()
            return jsonify(summary)
        
        @self.app.route('/api/charts/vulnerability-trends')
        def get_vulnerability_trends_chart():
            """Get vulnerability trends chart"""
            chart = self._create_vulnerability_trends_chart()
            return jsonify(chart)
        
        @self.app.route('/api/charts/severity-distribution')
        def get_severity_distribution_chart():
            """Get severity distribution chart"""
            chart = self._create_severity_distribution_chart()
            return jsonify(chart)
        
        @self.app.route('/api/charts/scan-performance')
        def get_scan_performance_chart():
            """Get scan performance chart"""
            chart = self._create_scan_performance_chart()
            return jsonify(chart)
        
        @self.app.route('/static/<path:filename>')
        def static_files(filename):
            """Serve static files"""
            return send_from_directory('static', filename)
    
    def _setup_socketio_events(self):
        """Setup SocketIO events for real-time updates"""
        
        @self.socketio.on('connect')
        def handle_connect():
            """Handle client connection"""
            logger.info("Dashboard client connected")
            emit('connected', {'status': 'connected'})
        
        @self.socketio.on('disconnect')
        def handle_disconnect():
            """Handle client disconnection"""
            logger.info("Dashboard client disconnected")
        
        @self.socketio.on('request_update')
        def handle_update_request():
            """Handle update request from client"""
            self._broadcast_updates()
    
    def add_scan_data(self, scan_result: Dict[str, Any]):
        """Add new scan data to dashboard"""
        try:
            scan_data = {
                'timestamp': datetime.now().isoformat(),
                'scan_id': scan_result.get('scan_id'),
                'file_path': scan_result.get('file_path'),
                'findings_count': len(scan_result.get('findings', [])),
                'severity_breakdown': self._calculate_severity_breakdown(scan_result.get('findings', [])),
                'execution_time_ms': scan_result.get('execution_time_ms', 0),
                'scanner_type': scan_result.get('scanner_type', 'unknown')
            }
            
            self.scan_data.append(scan_data)
            
            # Keep only recent data
            if len(self.scan_data) > self.config['max_data_points']:
                self.scan_data = self.scan_data[-self.config['max_data_points']:]
            
            # Update trends
            self._update_vulnerability_trends(scan_data)
            
            # Broadcast update to connected clients
            self._broadcast_scan_update(scan_data)
            
            logger.debug(f"Added scan data: {scan_result.get('scan_id')}")
            
        except Exception as e:
            logger.error(f"Failed to add scan data: {e}")
    
    def add_performance_metric(self, metric_name: str, value: float, unit: str = ""):
        """Add performance metric"""
        try:
            metric_data = {
                'timestamp': datetime.now().isoformat(),
                'metric_name': metric_name,
                'value': value,
                'unit': unit
            }
            
            self.performance_metrics.append(metric_data)
            
            # Keep only recent data
            if len(self.performance_metrics) > self.config['max_data_points']:
                self.performance_metrics = self.performance_metrics[-self.config['max_data_points']:]
            
            # Broadcast update
            self.socketio.emit('performance_update', metric_data)
            
        except Exception as e:
            logger.error(f"Failed to add performance metric: {e}")
    
    def add_security_event(self, event_type: str, description: str, severity: str = "info"):
        """Add security event"""
        try:
            event_data = {
                'timestamp': datetime.now().isoformat(),
                'event_type': event_type,
                'description': description,
                'severity': severity,
                'id': f"event_{datetime.now().timestamp()}"
            }
            
            self.security_events.append(event_data)
            
            # Keep only recent events
            if len(self.security_events) > 200:
                self.security_events = self.security_events[-200:]
            
            # Broadcast update
            self.socketio.emit('security_event', event_data)
            
        except Exception as e:
            logger.error(f"Failed to add security event: {e}")
    
    def _calculate_severity_breakdown(self, findings: List[Dict[str, Any]]) -> Dict[str, int]:
        """Calculate severity breakdown from findings"""
        breakdown = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0, 'info': 0}
        
        for finding in findings:
            severity = finding.get('severity', 'info').lower()
            if severity in breakdown:
                breakdown[severity] += 1
        
        return breakdown
    
    def _update_vulnerability_trends(self, scan_data: Dict[str, Any]):
        """Update vulnerability trends data"""
        trend_data = {
            'timestamp': scan_data['timestamp'],
            'total_vulnerabilities': scan_data['findings_count'],
            'critical_count': scan_data['severity_breakdown']['critical'],
            'high_count': scan_data['severity_breakdown']['high'],
            'medium_count': scan_data['severity_breakdown']['medium'],
            'low_count': scan_data['severity_breakdown']['low']
        }
        
        self.vulnerability_trends.append(trend_data)
        
        # Keep only recent trends
        if len(self.vulnerability_trends) > 100:
            self.vulnerability_trends = self.vulnerability_trends[-100:]
    
    def _calculate_dashboard_summary(self) -> Dict[str, Any]:
        """Calculate dashboard summary statistics"""
        if not self.scan_data:
            return {
                'total_scans': 0,
                'total_vulnerabilities': 0,
                'critical_issues': 0,
                'average_scan_time': 0,
                'scan_efficiency': 0,
                'risk_score': 0
            }
        
        recent_scans = self.scan_data[-50:]  # Last 50 scans
        
        total_scans = len(self.scan_data)
        total_vulnerabilities = sum(scan['findings_count'] for scan in recent_scans)
        critical_issues = sum(scan['severity_breakdown']['critical'] for scan in recent_scans)
        average_scan_time = np.mean([scan['execution_time_ms'] for scan in recent_scans])
        
        # Calculate scan efficiency (scans per minute)
        if len(recent_scans) >= 2:
            time_span = (datetime.fromisoformat(recent_scans[-1]['timestamp']) - 
                        datetime.fromisoformat(recent_scans[0]['timestamp'])).total_seconds() / 60
            scan_efficiency = len(recent_scans) / max(time_span, 1)
        else:
            scan_efficiency = 0
        
        # Calculate risk score (0-10 scale)
        if total_vulnerabilities > 0:
            risk_score = min(10, (critical_issues * 3 + 
                                sum(scan['severity_breakdown']['high'] for scan in recent_scans) * 2 +
                                sum(scan['severity_breakdown']['medium'] for scan in recent_scans)) / 10)
        else:
            risk_score = 0
        
        return {
            'total_scans': total_scans,
            'total_vulnerabilities': total_vulnerabilities,
            'critical_issues': critical_issues,
            'average_scan_time': round(average_scan_time, 1),
            'scan_efficiency': round(scan_efficiency, 2),
            'risk_score': round(risk_score, 1)
        }
    
    def _create_vulnerability_trends_chart(self) -> Dict[str, Any]:
        """Create vulnerability trends chart"""
        if not self.vulnerability_trends:
            return {'data': [], 'layout': {}}
        
        df = pd.DataFrame(self.vulnerability_trends)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        fig = go.Figure()
        
        # Add traces for different severity levels
        fig.add_trace(go.Scatter(
            x=df['timestamp'],
            y=df['critical_count'],
            mode='lines+markers',
            name='Critical',
            line=dict(color='#f44336', width=3),
            marker=dict(size=6)
        ))
        
        fig.add_trace(go.Scatter(
            x=df['timestamp'],
            y=df['high_count'],
            mode='lines+markers',
            name='High',
            line=dict(color='#ff9800', width=2),
            marker=dict(size=4)
        ))
        
        fig.add_trace(go.Scatter(
            x=df['timestamp'],
            y=df['medium_count'],
            mode='lines+markers',
            name='Medium',
            line=dict(color='#ffeb3b', width=2),
            marker=dict(size=4)
        ))
        
        fig.add_trace(go.Scatter(
            x=df['timestamp'],
            y=df['low_count'],
            mode='lines+markers',
            name='Low',
            line=dict(color='#4caf50', width=2),
            marker=dict(size=4)
        ))
        
        fig.update_layout(
            title='Vulnerability Trends Over Time',
            xaxis_title='Time',
            yaxis_title='Number of Vulnerabilities',
            paper_bgcolor='rgba(0,0,0,0)',
            plot_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            showlegend=True,
            legend=dict(
                orientation="h",
                yanchor="bottom",
                y=1.02,
                xanchor="right",
                x=1
            )
        )
        
        return json.loads(plotly.utils.PlotlyJSONEncoder().encode(fig))
    
    def _create_severity_distribution_chart(self) -> Dict[str, Any]:
        """Create severity distribution pie chart"""
        if not self.scan_data:
            return {'data': [], 'layout': {}}
        
        # Aggregate severity data from recent scans
        recent_scans = self.scan_data[-20:]  # Last 20 scans
        severity_totals = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        
        for scan in recent_scans:
            for severity, count in scan['severity_breakdown'].items():
                if severity in severity_totals:
                    severity_totals[severity] += count
        
        if sum(severity_totals.values()) == 0:
            return {'data': [], 'layout': {}}
        
        fig = go.Figure(data=[go.Pie(
            labels=list(severity_totals.keys()),
            values=list(severity_totals.values()),
            hole=0.3,
            marker_colors=['#f44336', '#ff9800', '#ffeb3b', '#4caf50']
        )])
        
        fig.update_layout(
            title='Vulnerability Severity Distribution',
            paper_bgcolor='rgba(0,0,0,0)',
            plot_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white'),
            showlegend=True
        )
        
        return json.loads(plotly.utils.PlotlyJSONEncoder().encode(fig))
    
    def _create_scan_performance_chart(self) -> Dict[str, Any]:
        """Create scan performance chart"""
        if not self.scan_data:
            return {'data': [], 'layout': {}}
        
        recent_scans = self.scan_data[-30:]  # Last 30 scans
        
        fig = go.Figure()
        
        fig.add_trace(go.Scatter(
            x=[scan['timestamp'] for scan in recent_scans],
            y=[scan['execution_time_ms'] for scan in recent_scans],
            mode='lines+markers',
            name='Scan Time (ms)',
            line=dict(color='#00bcd4', width=2),
            marker=dict(size=6)
        ))
        
        fig.update_layout(
            title='Scan Performance Over Time',
            xaxis_title='Time',
            yaxis_title='Execution Time (ms)',
            paper_bgcolor='rgba(0,0,0,0)',
            plot_bgcolor='rgba(0,0,0,0)',
            font=dict(color='white')
        )
        
        return json.loads(plotly.utils.PlotlyJSONEncoder().encode(fig))
    
    def _broadcast_updates(self):
        """Broadcast updates to all connected clients"""
        summary = self._calculate_dashboard_summary()
        self.socketio.emit('dashboard_update', {
            'summary': summary,
            'timestamp': datetime.now().isoformat()
        })
    
    def _broadcast_scan_update(self, scan_data: Dict[str, Any]):
        """Broadcast scan update to connected clients"""
        self.socketio.emit('scan_update', scan_data)
    
    def run(self, host: str = '127.0.0.1', debug: bool = False):
        """Run the interactive dashboard"""
        try:
            logger.info(f"Starting ByteGuardX Interactive Dashboard on {host}:{self.port}")
            self.socketio.run(self.app, host=host, port=self.port, debug=debug)
        except Exception as e:
            logger.error(f"Failed to start dashboard: {e}")
            raise
    
    def stop(self):
        """Stop the dashboard"""
        logger.info("Stopping Interactive Dashboard")

# Global dashboard instance
dashboard = InteractiveDashboard()
