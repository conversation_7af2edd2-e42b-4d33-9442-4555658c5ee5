"""
ByteGuardX AI Inference Engine
Implements multi-model ensemble with quantized models for vulnerability detection
"""

import logging
import numpy as np
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import json
import hashlib
import time
# Optional ML dependencies - graceful fallback if not available
try:
    import onnxruntime as ort
    HAS_ONNX = True
except ImportError:
    HAS_ONNX = False
    ort = None

try:
    import tensorflow as tf
    HAS_TENSORFLOW = True
except ImportError:
    HAS_TENSORFLOW = False
    tf = None

try:
    from transformers import AutoTokenizer, AutoModel
    HAS_TRANSFORMERS = True
except ImportError:
    HAS_TRANSFORMERS = False
    AutoTokenizer = None
    AutoModel = None

try:
    import torch
    import torch.nn as nn
    HAS_TORCH = True
except ImportError:
    HAS_TORCH = False
    torch = None
    nn = None

try:
    from sklearn.preprocessing import StandardScaler
    HAS_SKLEARN = True
except ImportError:
    HAS_SKLEARN = False
    StandardScaler = None
import ast
import re

logger = logging.getLogger(__name__)

@dataclass
class ModelInfo:
    """Model information and metadata"""
    name: str
    path: str
    model_type: str  # 'onnx', 'tflite', 'pytorch', 'transformer'
    input_shape: Tuple[int, ...]
    output_shape: Tuple[int, ...]
    quantization: str  # 'int8', 'fp16', 'fp32'
    accuracy: float
    model_hash: str
    version: str

@dataclass
class InferenceResult:
    """AI inference result"""
    prediction: str
    confidence: float
    model_predictions: Dict[str, Any]
    feature_importance: Dict[str, float]
    execution_time_ms: int
    model_versions: Dict[str, str]

class AIInferenceEngine:
    """
    Multi-model AI inference engine with quantized models
    Supports ONNX, TensorFlow Lite, PyTorch, and Transformer models
    """
    
    def __init__(self, models_dir: str = "models"):
        self.models_dir = Path(models_dir)
        self.models_dir.mkdir(parents=True, exist_ok=True)
        
        # Model registry
        self.models: Dict[str, ModelInfo] = {}
        self.loaded_models: Dict[str, Any] = {}
        
        # Feature extractors
        self.tokenizer = None
        self.transformer_model = None
        self.scaler = StandardScaler() if HAS_SKLEARN else None
        
        # Model ensemble weights
        self.ensemble_weights = {
            'transformer': 0.35,
            'gnn': 0.25,
            'cnn': 0.20,
            'traditional_ml': 0.20
        }
        
        self.is_initialized = False
        
        logger.info("AI Inference Engine initialized")
    
    async def initialize(self):
        """Initialize AI inference engine"""
        try:
            # Check available ML libraries
            available_libs = []
            if HAS_TENSORFLOW:
                available_libs.append("TensorFlow")
            if HAS_TORCH:
                available_libs.append("PyTorch")
            if HAS_TRANSFORMERS:
                available_libs.append("Transformers")
            if HAS_ONNX:
                available_libs.append("ONNX Runtime")
            if HAS_SKLEARN:
                available_libs.append("scikit-learn")

            if not available_libs:
                logger.warning("No ML libraries available - using rule-based fallback")
                self.is_initialized = True
                return

            logger.info(f"Available ML libraries: {', '.join(available_libs)}")

            # Discover available models
            await self._discover_models()

            # Load quantized models
            await self._load_models()

            # Initialize transformer for code understanding (if available)
            if HAS_TRANSFORMERS:
                await self._initialize_transformer()

            # Verify model integrity
            await self._verify_model_integrity()

            self.is_initialized = True
            logger.info(f"AI Inference Engine ready with {len(self.loaded_models)} models")

        except Exception as e:
            logger.error(f"AI inference engine initialization failed: {e}")
            # Fallback to rule-based mode
            self.is_initialized = True
            logger.info("Falling back to rule-based inference")
    
    async def _discover_models(self):
        """Discover available models in models directory"""
        model_config_file = self.models_dir / "models.json"
        
        if model_config_file.exists():
            with open(model_config_file, 'r') as f:
                model_configs = json.load(f)
            
            for model_config in model_configs.get('models', []):
                model_info = ModelInfo(**model_config)
                self.models[model_info.name] = model_info
                logger.info(f"Discovered model: {model_info.name} ({model_info.model_type})")
        else:
            # Create default model configuration
            await self._create_default_model_config()
    
    async def _create_default_model_config(self):
        """Create default model configuration"""
        default_models = [
            {
                "name": "codebert_vulnerability_detector",
                "path": "codebert-vuln-detection-int8.onnx",
                "model_type": "onnx",
                "input_shape": [1, 512],
                "output_shape": [1, 10],
                "quantization": "int8",
                "accuracy": 0.94,
                "model_hash": "sha256:placeholder",
                "version": "1.0.0"
            },
            {
                "name": "code_gnn_analyzer",
                "path": "code-gnn-vuln-fp16.tflite",
                "model_type": "tflite",
                "input_shape": [1, 256, 256],
                "output_shape": [1, 5],
                "quantization": "fp16",
                "accuracy": 0.91,
                "model_hash": "sha256:placeholder",
                "version": "1.0.0"
            },
            {
                "name": "pattern_cnn_detector",
                "path": "pattern-cnn-int8.onnx",
                "model_type": "onnx",
                "input_shape": [1, 1000],
                "output_shape": [1, 8],
                "quantization": "int8",
                "accuracy": 0.89,
                "model_hash": "sha256:placeholder",
                "version": "1.0.0"
            }
        ]
        
        model_config = {
            "version": "1.0",
            "models": default_models
        }
        
        model_config_file = self.models_dir / "models.json"
        with open(model_config_file, 'w') as f:
            json.dump(model_config, f, indent=2)
        
        # Register models
        for model_data in default_models:
            model_info = ModelInfo(**model_data)
            self.models[model_info.name] = model_info
    
    async def _load_models(self):
        """Load quantized models for inference"""
        for model_name, model_info in self.models.items():
            try:
                model_path = self.models_dir / model_info.path
                
                if not model_path.exists():
                    logger.warning(f"Model file not found: {model_path}")
                    continue
                
                if model_info.model_type == 'onnx':
                    # Load ONNX model with CPU provider for quantized inference
                    session = ort.InferenceSession(
                        str(model_path),
                        providers=['CPUExecutionProvider']
                    )
                    self.loaded_models[model_name] = session
                    
                elif model_info.model_type == 'tflite':
                    # Load TensorFlow Lite model
                    interpreter = tf.lite.Interpreter(model_path=str(model_path))
                    interpreter.allocate_tensors()
                    self.loaded_models[model_name] = interpreter
                    
                elif model_info.model_type == 'pytorch':
                    # Load PyTorch model
                    model = torch.jit.load(str(model_path))
                    model.eval()
                    self.loaded_models[model_name] = model
                
                logger.info(f"Loaded model: {model_name} ({model_info.quantization})")
                
            except Exception as e:
                logger.error(f"Failed to load model {model_name}: {e}")
    
    async def _initialize_transformer(self):
        """Initialize transformer model for code understanding"""
        try:
            # Use CodeBERT for code understanding
            model_name = "microsoft/codebert-base"
            
            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
            self.transformer_model = AutoModel.from_pretrained(model_name)
            
            # Set to evaluation mode
            self.transformer_model.eval()
            
            logger.info("Transformer model initialized")
            
        except Exception as e:
            logger.warning(f"Failed to initialize transformer model: {e}")
            self.tokenizer = None
            self.transformer_model = None
    
    async def _verify_model_integrity(self):
        """Verify integrity of loaded models"""
        for model_name, model_info in self.models.items():
            model_path = self.models_dir / model_info.path
            
            if model_path.exists():
                # Calculate actual hash
                actual_hash = self._calculate_file_hash(model_path)
                expected_hash = model_info.model_hash.split(':')[1] if ':' in model_info.model_hash else model_info.model_hash
                
                if actual_hash != expected_hash and expected_hash != "placeholder":
                    logger.warning(f"Model hash mismatch for {model_name}")
                else:
                    logger.info(f"Model integrity verified: {model_name}")
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of file"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    async def extract_features(self, code_content: str) -> Dict[str, np.ndarray]:
        """Extract comprehensive features from code"""
        features = {}
        
        try:
            # Basic code metrics
            features['basic'] = self._extract_basic_features(code_content)
            
            # AST-based features
            features['ast'] = self._extract_ast_features(code_content)
            
            # Pattern-based features
            features['patterns'] = self._extract_pattern_features(code_content)
            
            # Semantic features using transformer
            if self.tokenizer and self.transformer_model:
                features['semantic'] = await self._extract_semantic_features(code_content)
            
            # Graph features (simplified)
            features['graph'] = self._extract_graph_features(code_content)
            
            logger.debug(f"Extracted {len(features)} feature types")
            return features
            
        except Exception as e:
            logger.error(f"Feature extraction failed: {e}")
            return {}
    
    def _extract_basic_features(self, code_content: str) -> np.ndarray:
        """Extract basic code metrics"""
        lines = code_content.split('\n')
        
        features = [
            len(code_content),  # Total characters
            len(lines),  # Total lines
            len([line for line in lines if line.strip()]),  # Non-empty lines
            len([line for line in lines if line.strip().startswith('#')]),  # Comments
            code_content.count('import '),  # Import statements
            code_content.count('def '),  # Function definitions
            code_content.count('class '),  # Class definitions
            code_content.count('if '),  # Conditional statements
            code_content.count('for '),  # Loop statements
            code_content.count('while '),  # While loops
            code_content.count('try:'),  # Exception handling
            code_content.count('except'),  # Exception catching
            len(code_content.split()),  # Word count
            code_content.count('='),  # Assignment operations
            code_content.count('=='),  # Comparison operations
            # Security-related patterns
            len(re.findall(r'(password|secret|key|token)\s*=', code_content, re.IGNORECASE)),
            len(re.findall(r'(exec|eval|system)\s*\(', code_content, re.IGNORECASE)),
            len(re.findall(r'(SELECT|INSERT|UPDATE|DELETE)', code_content, re.IGNORECASE)),
            len(re.findall(r'(http://|https://)', code_content, re.IGNORECASE)),
            len(re.findall(r'(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})', code_content))
        ]
        
        return np.array(features, dtype=np.float32)
    
    def _extract_ast_features(self, code_content: str) -> np.ndarray:
        """Extract AST-based features"""
        features = np.zeros(50, dtype=np.float32)  # Fixed size feature vector
        
        try:
            tree = ast.parse(code_content)
            
            # Count different node types
            node_counts = {}
            for node in ast.walk(tree):
                node_type = type(node).__name__
                node_counts[node_type] = node_counts.get(node_type, 0) + 1
            
            # Map common node types to feature indices
            node_mapping = {
                'FunctionDef': 0, 'ClassDef': 1, 'If': 2, 'For': 3, 'While': 4,
                'Try': 5, 'Call': 6, 'Assign': 7, 'Compare': 8, 'BinOp': 9,
                'Import': 10, 'ImportFrom': 11, 'Return': 12, 'Yield': 13,
                'Lambda': 14, 'ListComp': 15, 'DictComp': 16, 'SetComp': 17,
                'GeneratorExp': 18, 'With': 19
            }
            
            for node_type, index in node_mapping.items():
                if index < len(features):
                    features[index] = node_counts.get(node_type, 0)
            
        except Exception as e:
            logger.debug(f"AST parsing failed: {e}")
        
        return features
    
    def _extract_pattern_features(self, code_content: str) -> np.ndarray:
        """Extract security pattern features"""
        patterns = {
            'sql_injection': r'(SELECT|INSERT|UPDATE|DELETE).*(\+|%|\|)',
            'xss_patterns': r'(innerHTML|document\.write|eval\()',
            'command_injection': r'(exec|system|popen|subprocess)',
            'path_traversal': r'(\.\./|\.\.\\)',
            'hardcoded_secrets': r'(password|secret|key|token)\s*=\s*["\'][^"\']+["\']',
            'crypto_issues': r'(md5|sha1|des|rc4)',
            'unsafe_functions': r'(gets|strcpy|strcat|sprintf)',
            'buffer_overflow': r'(malloc|free|memcpy|strcpy)',
            'race_conditions': r'(pthread|thread|lock|mutex)',
            'integer_overflow': r'(int\s+\w+\s*=.*\+|size_t.*\*)',
            'format_string': r'(printf|sprintf|fprintf).*%[^s]',
            'null_pointer': r'(NULL|nullptr|\*\s*\w+\s*=\s*0)',
            'memory_leak': r'(malloc|calloc|new)(?!.*free|delete)',
            'weak_random': r'(rand\(\)|random\(\)|Math\.random)',
            'insecure_hash': r'(MD5|SHA1)(?!.*HMAC)'
        }
        
        features = []
        for pattern_name, pattern in patterns.items():
            matches = len(re.findall(pattern, code_content, re.IGNORECASE))
            features.append(float(matches))
        
        return np.array(features, dtype=np.float32)
    
    async def _extract_semantic_features(self, code_content: str) -> np.ndarray:
        """Extract semantic features using transformer"""
        try:
            # Tokenize code
            inputs = self.tokenizer(
                code_content,
                return_tensors="pt",
                max_length=512,
                truncation=True,
                padding=True
            )
            
            # Get embeddings
            with torch.no_grad():
                outputs = self.transformer_model(**inputs)
                embeddings = outputs.last_hidden_state.mean(dim=1).squeeze()
            
            # Convert to numpy and take first 100 dimensions
            semantic_features = embeddings.numpy()[:100]
            
            # Pad if necessary
            if len(semantic_features) < 100:
                semantic_features = np.pad(semantic_features, (0, 100 - len(semantic_features)))
            
            return semantic_features.astype(np.float32)
            
        except Exception as e:
            logger.debug(f"Semantic feature extraction failed: {e}")
            return np.zeros(100, dtype=np.float32)
    
    def _extract_graph_features(self, code_content: str) -> np.ndarray:
        """Extract graph-based features (simplified)"""
        # This is a simplified implementation
        # In production, you would build a proper code graph
        
        features = np.zeros(20, dtype=np.float32)
        
        try:
            # Count function calls
            function_calls = len(re.findall(r'\w+\s*\(', code_content))
            features[0] = function_calls
            
            # Count variable assignments
            assignments = len(re.findall(r'\w+\s*=', code_content))
            features[1] = assignments
            
            # Count control flow statements
            control_flow = len(re.findall(r'(if|for|while|try|except)', code_content))
            features[2] = control_flow
            
            # Add more graph-based features as needed
            
        except Exception as e:
            logger.debug(f"Graph feature extraction failed: {e}")
        
        return features
    
    async def predict_vulnerabilities(self, features: Dict[str, np.ndarray]) -> List[Dict[str, Any]]:
        """Predict vulnerabilities using ensemble of models"""
        start_time = time.time()
        predictions = []
        
        try:
            # Get predictions from each model
            model_predictions = {}
            
            # ONNX models
            for model_name, model in self.loaded_models.items():
                if model_name in self.models and self.models[model_name].model_type == 'onnx':
                    try:
                        # Prepare input based on model requirements
                        model_input = self._prepare_model_input(features, self.models[model_name])
                        
                        # Run inference
                        input_name = model.get_inputs()[0].name
                        output = model.run(None, {input_name: model_input})
                        
                        model_predictions[model_name] = output[0]
                        
                    except Exception as e:
                        logger.warning(f"ONNX model {model_name} inference failed: {e}")
            
            # TensorFlow Lite models
            for model_name, interpreter in self.loaded_models.items():
                if model_name in self.models and self.models[model_name].model_type == 'tflite':
                    try:
                        # Prepare input
                        model_input = self._prepare_model_input(features, self.models[model_name])
                        
                        # Set input tensor
                        input_details = interpreter.get_input_details()
                        interpreter.set_tensor(input_details[0]['index'], model_input)
                        
                        # Run inference
                        interpreter.invoke()
                        
                        # Get output
                        output_details = interpreter.get_output_details()
                        output = interpreter.get_tensor(output_details[0]['index'])
                        
                        model_predictions[model_name] = output
                        
                    except Exception as e:
                        logger.warning(f"TFLite model {model_name} inference failed: {e}")
            
            # Ensemble predictions
            if model_predictions:
                ensemble_result = self._ensemble_predictions(model_predictions)
                predictions.append(ensemble_result)
            
            execution_time = int((time.time() - start_time) * 1000)
            
            # Add execution metadata
            for prediction in predictions:
                prediction['execution_time_ms'] = execution_time
                prediction['model_versions'] = {name: info.version for name, info in self.models.items()}
            
            return predictions
            
        except Exception as e:
            logger.error(f"Vulnerability prediction failed: {e}")
            return []
    
    def _prepare_model_input(self, features: Dict[str, np.ndarray], model_info: ModelInfo) -> np.ndarray:
        """Prepare input for specific model"""
        # Combine features based on model requirements
        if model_info.name == 'codebert_vulnerability_detector':
            # Use semantic features
            return features.get('semantic', np.zeros(512, dtype=np.float32)).reshape(1, -1)
        
        elif model_info.name == 'pattern_cnn_detector':
            # Combine all features
            combined = np.concatenate([
                features.get('basic', np.zeros(20, dtype=np.float32)),
                features.get('ast', np.zeros(50, dtype=np.float32)),
                features.get('patterns', np.zeros(15, dtype=np.float32)),
                features.get('graph', np.zeros(20, dtype=np.float32))
            ])
            
            # Pad or truncate to expected size
            target_size = model_info.input_shape[-1]
            if len(combined) < target_size:
                combined = np.pad(combined, (0, target_size - len(combined)))
            elif len(combined) > target_size:
                combined = combined[:target_size]
            
            return combined.reshape(1, -1)
        
        else:
            # Default: use basic features
            basic_features = features.get('basic', np.zeros(20, dtype=np.float32))
            return basic_features.reshape(1, -1)
    
    def _ensemble_predictions(self, model_predictions: Dict[str, np.ndarray]) -> Dict[str, Any]:
        """Combine predictions from multiple models"""
        # Vulnerability type mapping
        vuln_types = ['sql_injection', 'xss', 'command_injection', 'path_traversal', 
                     'hardcoded_secret', 'crypto_weakness', 'buffer_overflow', 'race_condition']
        
        # Weighted ensemble
        ensemble_scores = np.zeros(len(vuln_types))
        total_weight = 0
        
        for model_name, prediction in model_predictions.items():
            if model_name in self.ensemble_weights:
                weight = self.ensemble_weights.get(model_name, 0.25)
                
                # Normalize prediction to vulnerability types
                if len(prediction.flatten()) >= len(vuln_types):
                    scores = prediction.flatten()[:len(vuln_types)]
                else:
                    scores = np.pad(prediction.flatten(), (0, len(vuln_types) - len(prediction.flatten())))
                
                ensemble_scores += weight * scores
                total_weight += weight
        
        if total_weight > 0:
            ensemble_scores /= total_weight
        
        # Find highest scoring vulnerability
        max_idx = np.argmax(ensemble_scores)
        max_score = ensemble_scores[max_idx]
        
        # Determine severity based on score
        if max_score > 0.8:
            severity = 'critical'
        elif max_score > 0.6:
            severity = 'high'
        elif max_score > 0.4:
            severity = 'medium'
        elif max_score > 0.2:
            severity = 'low'
        else:
            severity = 'info'
        
        return {
            'type': vuln_types[max_idx],
            'severity': severity,
            'confidence': float(max_score),
            'title': f'AI-detected {vuln_types[max_idx].replace("_", " ").title()}',
            'description': f'Machine learning models detected potential {vuln_types[max_idx].replace("_", " ")} vulnerability',
            'line_start': 1,  # Would be determined by more sophisticated analysis
            'line_end': 1,
            'model_predictions': {k: v.tolist() for k, v in model_predictions.items()},
            'ensemble_scores': ensemble_scores.tolist()
        }
