"""
ByteGuardX Explainability Engine
Implements GNN explainability, attention visualization, and LIME explanations
"""

import logging
import numpy as np
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
import json
import re
import ast
from pathlib import Path
import torch
import torch.nn.functional as F
from lime.lime_text import LimeTextExplainer
import shap

logger = logging.getLogger(__name__)

@dataclass
class AttentionWeight:
    """Attention weight for token-level explanation"""
    token: str
    line: int
    column: int
    attention_weight: float
    importance_level: str  # 'critical', 'high', 'medium', 'low'

@dataclass
class FeatureImportance:
    """Feature importance explanation"""
    feature_name: str
    importance_score: float
    feature_type: str  # 'pattern', 'ast', 'semantic', 'graph'
    description: str

@dataclass
class ExplanationResult:
    """Complete explanation result"""
    finding_id: str
    explanation_type: str
    attention_heatmap: List[AttentionWeight]
    feature_importance: List[FeatureImportance]
    similar_patterns: List[Dict[str, Any]]
    confidence_breakdown: Dict[str, float]
    local_explanation: str
    global_explanation: str
    remediation_suggestions: List[str]

class ExplainabilityEngine:
    """
    Advanced explainability engine for AI vulnerability predictions
    Provides multiple explanation techniques for transparency
    """
    
    def __init__(self):
        self.lime_explainer = None
        self.shap_explainer = None
        self.pattern_database = {}
        self.is_initialized = False
        
        # Explanation templates
        self.explanation_templates = {
            'sql_injection': {
                'description': 'SQL injection vulnerability detected through pattern analysis',
                'indicators': ['SQL keywords', 'String concatenation', 'User input handling'],
                'remediation': ['Use parameterized queries', 'Input validation', 'Escape special characters']
            },
            'xss': {
                'description': 'Cross-site scripting vulnerability identified',
                'indicators': ['DOM manipulation', 'User input rendering', 'Unsafe HTML generation'],
                'remediation': ['HTML encoding', 'Content Security Policy', 'Input sanitization']
            },
            'command_injection': {
                'description': 'Command injection risk detected',
                'indicators': ['System command execution', 'User input in commands', 'Shell operations'],
                'remediation': ['Avoid system calls', 'Input validation', 'Use safe APIs']
            },
            'hardcoded_secret': {
                'description': 'Hardcoded secret or credential found',
                'indicators': ['String literals with secrets', 'API keys', 'Passwords in code'],
                'remediation': ['Use environment variables', 'Secret management systems', 'Configuration files']
            }
        }
        
        logger.info("Explainability Engine initialized")
    
    async def initialize(self):
        """Initialize explainability engine"""
        try:
            # Initialize LIME explainer
            self.lime_explainer = LimeTextExplainer(
                class_names=['safe', 'vulnerable'],
                feature_selection='auto',
                verbose=False
            )
            
            # Load pattern database
            await self._load_pattern_database()
            
            # Initialize SHAP explainer (placeholder)
            # In production, this would be properly configured
            
            self.is_initialized = True
            logger.info("Explainability Engine initialized")
            
        except Exception as e:
            logger.error(f"Explainability engine initialization failed: {e}")
            raise
    
    async def _load_pattern_database(self):
        """Load database of known vulnerability patterns"""
        try:
            pattern_file = Path("data/vulnerability_patterns.json")
            
            if pattern_file.exists():
                with open(pattern_file, 'r') as f:
                    self.pattern_database = json.load(f)
            else:
                # Create default pattern database
                self.pattern_database = {
                    'sql_injection': [
                        {'pattern': r'SELECT.*FROM.*WHERE.*\+', 'description': 'String concatenation in SQL query'},
                        {'pattern': r'INSERT.*VALUES.*\+', 'description': 'Dynamic INSERT statement'},
                        {'pattern': r'UPDATE.*SET.*\+', 'description': 'Dynamic UPDATE statement'}
                    ],
                    'xss': [
                        {'pattern': r'innerHTML\s*=', 'description': 'Direct HTML injection'},
                        {'pattern': r'document\.write\s*\(', 'description': 'Dynamic document writing'},
                        {'pattern': r'eval\s*\(', 'description': 'Dynamic code evaluation'}
                    ],
                    'command_injection': [
                        {'pattern': r'exec\s*\(', 'description': 'Code execution function'},
                        {'pattern': r'system\s*\(', 'description': 'System command execution'},
                        {'pattern': r'subprocess\.(call|run|Popen)', 'description': 'Subprocess execution'}
                    ]
                }
                
                # Save default patterns
                pattern_file.parent.mkdir(parents=True, exist_ok=True)
                with open(pattern_file, 'w') as f:
                    json.dump(self.pattern_database, f, indent=2)
            
            logger.info(f"Loaded {len(self.pattern_database)} pattern categories")
            
        except Exception as e:
            logger.warning(f"Failed to load pattern database: {e}")
            self.pattern_database = {}
    
    async def explain_prediction(self, finding: Any, code_content: str) -> ExplanationResult:
        """Generate comprehensive explanation for AI prediction"""
        try:
            # Generate different types of explanations
            attention_heatmap = await self._generate_attention_explanation(finding, code_content)
            feature_importance = await self._generate_feature_importance(finding, code_content)
            similar_patterns = await self._find_similar_patterns(finding, code_content)
            confidence_breakdown = await self._analyze_confidence(finding)
            local_explanation = await self._generate_local_explanation(finding, code_content)
            global_explanation = await self._generate_global_explanation(finding)
            remediation_suggestions = await self._generate_remediation_suggestions(finding)
            
            return ExplanationResult(
                finding_id=finding.id,
                explanation_type='comprehensive',
                attention_heatmap=attention_heatmap,
                feature_importance=feature_importance,
                similar_patterns=similar_patterns,
                confidence_breakdown=confidence_breakdown,
                local_explanation=local_explanation,
                global_explanation=global_explanation,
                remediation_suggestions=remediation_suggestions
            )
            
        except Exception as e:
            logger.error(f"Explanation generation failed: {e}")
            return self._create_fallback_explanation(finding)
    
    async def _generate_attention_explanation(self, finding: Any, code_content: str) -> List[AttentionWeight]:
        """Generate attention-based explanation with token highlighting"""
        attention_weights = []
        
        try:
            # Tokenize code content
            lines = code_content.split('\n')
            
            # Simulate attention weights based on vulnerability patterns
            vuln_type = getattr(finding, 'type', 'unknown')
            patterns = self.pattern_database.get(vuln_type, [])
            
            for line_idx, line in enumerate(lines):
                tokens = self._tokenize_line(line)
                
                for token_idx, token in enumerate(tokens):
                    # Calculate attention weight based on pattern matching
                    attention_weight = self._calculate_token_attention(token, line, patterns)
                    
                    if attention_weight > 0.1:  # Only include significant weights
                        importance_level = self._categorize_importance(attention_weight)
                        
                        attention_weights.append(AttentionWeight(
                            token=token,
                            line=line_idx + 1,
                            column=token_idx,
                            attention_weight=attention_weight,
                            importance_level=importance_level
                        ))
            
            # Sort by attention weight
            attention_weights.sort(key=lambda x: x.attention_weight, reverse=True)
            
            return attention_weights[:50]  # Return top 50 tokens
            
        except Exception as e:
            logger.error(f"Attention explanation generation failed: {e}")
            return []
    
    def _tokenize_line(self, line: str) -> List[str]:
        """Tokenize code line into meaningful tokens"""
        # Simple tokenization - in production, use proper code tokenizer
        tokens = []
        
        # Split by common delimiters
        import re
        token_pattern = r'\w+|[^\w\s]'
        tokens = re.findall(token_pattern, line)
        
        return [token for token in tokens if token.strip()]
    
    def _calculate_token_attention(self, token: str, line: str, patterns: List[Dict[str, Any]]) -> float:
        """Calculate attention weight for token based on vulnerability patterns"""
        attention_weight = 0.0
        
        # Check if token matches vulnerability patterns
        for pattern_info in patterns:
            pattern = pattern_info['pattern']
            
            if re.search(pattern, line, re.IGNORECASE):
                # Token is part of a vulnerable pattern
                if token.lower() in ['select', 'insert', 'update', 'delete', 'exec', 'eval', 'system']:
                    attention_weight = max(attention_weight, 0.9)
                elif token.lower() in ['password', 'secret', 'key', 'token', 'api_key']:
                    attention_weight = max(attention_weight, 0.8)
                elif token in ['=', '+', '(', ')']:
                    attention_weight = max(attention_weight, 0.3)
                else:
                    attention_weight = max(attention_weight, 0.2)
        
        # Additional heuristics
        if token.lower() in ['input', 'request', 'user', 'param']:
            attention_weight = max(attention_weight, 0.4)
        
        return attention_weight
    
    def _categorize_importance(self, attention_weight: float) -> str:
        """Categorize attention weight into importance levels"""
        if attention_weight >= 0.8:
            return 'critical'
        elif attention_weight >= 0.6:
            return 'high'
        elif attention_weight >= 0.3:
            return 'medium'
        else:
            return 'low'
    
    async def _generate_feature_importance(self, finding: Any, code_content: str) -> List[FeatureImportance]:
        """Generate feature importance explanation"""
        feature_importance = []
        
        try:
            # Analyze different feature types
            vuln_type = getattr(finding, 'type', 'unknown')
            
            # Pattern-based features
            pattern_features = self._analyze_pattern_features(code_content, vuln_type)
            feature_importance.extend(pattern_features)
            
            # AST-based features
            ast_features = self._analyze_ast_features(code_content, vuln_type)
            feature_importance.extend(ast_features)
            
            # Semantic features (simplified)
            semantic_features = self._analyze_semantic_features(code_content, vuln_type)
            feature_importance.extend(semantic_features)
            
            # Sort by importance score
            feature_importance.sort(key=lambda x: x.importance_score, reverse=True)
            
            return feature_importance[:20]  # Return top 20 features
            
        except Exception as e:
            logger.error(f"Feature importance generation failed: {e}")
            return []
    
    def _analyze_pattern_features(self, code_content: str, vuln_type: str) -> List[FeatureImportance]:
        """Analyze pattern-based features"""
        features = []
        
        patterns = self.pattern_database.get(vuln_type, [])
        
        for pattern_info in patterns:
            pattern = pattern_info['pattern']
            description = pattern_info['description']
            
            matches = len(re.findall(pattern, code_content, re.IGNORECASE))
            
            if matches > 0:
                importance_score = min(matches * 0.3, 1.0)  # Cap at 1.0
                
                features.append(FeatureImportance(
                    feature_name=f"Pattern: {pattern}",
                    importance_score=importance_score,
                    feature_type='pattern',
                    description=description
                ))
        
        return features
    
    def _analyze_ast_features(self, code_content: str, vuln_type: str) -> List[FeatureImportance]:
        """Analyze AST-based features"""
        features = []
        
        try:
            tree = ast.parse(code_content)
            
            # Count different node types
            node_counts = {}
            for node in ast.walk(tree):
                node_type = type(node).__name__
                node_counts[node_type] = node_counts.get(node_type, 0) + 1
            
            # Analyze relevant AST features for vulnerability type
            if vuln_type == 'sql_injection':
                if node_counts.get('Call', 0) > 0:
                    features.append(FeatureImportance(
                        feature_name='Function calls',
                        importance_score=0.6,
                        feature_type='ast',
                        description='Presence of function calls that might execute SQL'
                    ))
                
                if node_counts.get('BinOp', 0) > 0:
                    features.append(FeatureImportance(
                        feature_name='String concatenation',
                        importance_score=0.8,
                        feature_type='ast',
                        description='Binary operations that might concatenate SQL strings'
                    ))
            
            elif vuln_type == 'xss':
                if node_counts.get('Assign', 0) > 0:
                    features.append(FeatureImportance(
                        feature_name='Variable assignments',
                        importance_score=0.5,
                        feature_type='ast',
                        description='Assignments that might involve HTML content'
                    ))
            
        except Exception as e:
            logger.debug(f"AST analysis failed: {e}")
        
        return features
    
    def _analyze_semantic_features(self, code_content: str, vuln_type: str) -> List[FeatureImportance]:
        """Analyze semantic features (simplified)"""
        features = []
        
        # Simple semantic analysis based on keywords
        security_keywords = {
            'sql_injection': ['query', 'database', 'sql', 'select', 'insert', 'update', 'delete'],
            'xss': ['html', 'dom', 'innerHTML', 'document', 'script', 'render'],
            'command_injection': ['command', 'exec', 'system', 'shell', 'subprocess'],
            'hardcoded_secret': ['password', 'secret', 'key', 'token', 'credential']
        }
        
        keywords = security_keywords.get(vuln_type, [])
        
        for keyword in keywords:
            count = len(re.findall(rf'\b{keyword}\b', code_content, re.IGNORECASE))
            
            if count > 0:
                importance_score = min(count * 0.2, 0.8)
                
                features.append(FeatureImportance(
                    feature_name=f"Keyword: {keyword}",
                    importance_score=importance_score,
                    feature_type='semantic',
                    description=f'Presence of security-relevant keyword: {keyword}'
                ))
        
        return features
    
    async def _find_similar_patterns(self, finding: Any, code_content: str) -> List[Dict[str, Any]]:
        """Find similar vulnerability patterns"""
        similar_patterns = []
        
        try:
            vuln_type = getattr(finding, 'type', 'unknown')
            
            # Get patterns for this vulnerability type
            patterns = self.pattern_database.get(vuln_type, [])
            
            for pattern_info in patterns:
                pattern = pattern_info['pattern']
                
                matches = list(re.finditer(pattern, code_content, re.IGNORECASE))
                
                for match in matches:
                    similar_patterns.append({
                        'pattern': pattern,
                        'description': pattern_info['description'],
                        'match_text': match.group(),
                        'start_pos': match.start(),
                        'end_pos': match.end(),
                        'similarity_score': 0.8  # Simplified similarity score
                    })
            
            return similar_patterns[:10]  # Return top 10 similar patterns
            
        except Exception as e:
            logger.error(f"Similar pattern analysis failed: {e}")
            return []
    
    async def _analyze_confidence(self, finding: Any) -> Dict[str, float]:
        """Analyze confidence breakdown"""
        confidence_breakdown = {}
        
        try:
            # Get model predictions if available
            model_predictions = getattr(finding, 'model_predictions', {})
            
            if model_predictions:
                for model_name, prediction in model_predictions.items():
                    if isinstance(prediction, (list, np.ndarray)):
                        confidence_breakdown[model_name] = float(np.max(prediction))
                    else:
                        confidence_breakdown[model_name] = float(prediction)
            else:
                # Fallback confidence analysis
                base_confidence = getattr(finding, 'confidence', 0.5)
                confidence_breakdown = {
                    'pattern_matching': base_confidence * 0.4,
                    'ml_prediction': base_confidence * 0.6
                }
            
        except Exception as e:
            logger.error(f"Confidence analysis failed: {e}")
            confidence_breakdown = {'unknown': 0.5}
        
        return confidence_breakdown
    
    async def _generate_local_explanation(self, finding: Any, code_content: str) -> str:
        """Generate local explanation for specific finding"""
        try:
            vuln_type = getattr(finding, 'type', 'unknown')
            confidence = getattr(finding, 'confidence', 0.5)
            
            template = self.explanation_templates.get(vuln_type, {})
            description = template.get('description', f'Vulnerability of type {vuln_type} detected')
            
            explanation = f"{description} with {confidence:.1%} confidence. "
            
            # Add specific indicators found
            indicators = template.get('indicators', [])
            if indicators:
                explanation += f"Key indicators include: {', '.join(indicators[:3])}. "
            
            # Add context about the detection
            explanation += f"This was identified through analysis of code patterns, "
            explanation += f"abstract syntax tree structure, and semantic content analysis."
            
            return explanation
            
        except Exception as e:
            logger.error(f"Local explanation generation failed: {e}")
            return "AI models detected a potential security vulnerability in this code."
    
    async def _generate_global_explanation(self, finding: Any) -> str:
        """Generate global explanation about the vulnerability type"""
        try:
            vuln_type = getattr(finding, 'type', 'unknown')
            template = self.explanation_templates.get(vuln_type, {})
            
            if template:
                description = template.get('description', '')
                return f"Global context: {description}. This type of vulnerability is commonly found in applications that handle user input without proper validation or sanitization."
            else:
                return f"This vulnerability type ({vuln_type}) represents a security risk that should be addressed to maintain application security."
                
        except Exception as e:
            logger.error(f"Global explanation generation failed: {e}")
            return "Security vulnerabilities can lead to data breaches and system compromise."
    
    async def _generate_remediation_suggestions(self, finding: Any) -> List[str]:
        """Generate remediation suggestions"""
        try:
            vuln_type = getattr(finding, 'type', 'unknown')
            template = self.explanation_templates.get(vuln_type, {})
            
            suggestions = template.get('remediation', [
                'Review the flagged code for security issues',
                'Apply security best practices',
                'Consider input validation and sanitization'
            ])
            
            return suggestions
            
        except Exception as e:
            logger.error(f"Remediation suggestion generation failed: {e}")
            return ['Review and address the security issue']
    
    def _create_fallback_explanation(self, finding: Any) -> ExplanationResult:
        """Create fallback explanation when detailed analysis fails"""
        return ExplanationResult(
            finding_id=getattr(finding, 'id', 'unknown'),
            explanation_type='fallback',
            attention_heatmap=[],
            feature_importance=[],
            similar_patterns=[],
            confidence_breakdown={'unknown': 0.5},
            local_explanation="AI models detected a potential security vulnerability.",
            global_explanation="Security analysis identified patterns that may indicate a vulnerability.",
            remediation_suggestions=["Review the flagged code for security issues"]
        )
