"""
Cache manager for ByteGuardX scan results and file metadata
Provides intelligent caching to avoid re-scanning unchanged files
"""

import os
import json
import hashlib
import logging
from pathlib import Path
from typing import Dict, Any, Optional, List, Set
from datetime import datetime, timedelta
import threading
from dataclasses import dataclass, asdict
import hmac
import gzip
import secrets

logger = logging.getLogger(__name__)

@dataclass
class FileMetadata:
    """File metadata for cache validation"""
    file_path: str
    size: int
    mtime: float  # modification time
    checksum: str
    last_scanned: datetime
    
    def is_valid(self) -> bool:
        """Check if cached metadata is still valid"""
        try:
            stat = os.stat(self.file_path)
            return (
                stat.st_size == self.size and
                stat.st_mtime == self.mtime
            )
        except (OSError, FileNotFoundError):
            return False
    
    @classmethod
    def from_file(cls, file_path: str) -> 'FileMetadata':
        """Create metadata from file"""
        stat = os.stat(file_path)
        
        # Calculate file checksum for content validation
        checksum = cls._calculate_checksum(file_path)
        
        return cls(
            file_path=file_path,
            size=stat.st_size,
            mtime=stat.st_mtime,
            checksum=checksum,
            last_scanned=datetime.now()
        )
    
    @staticmethod
    def _calculate_checksum(file_path: str) -> str:
        """Calculate SHA-256 checksum of file content"""
        hash_sha256 = hashlib.sha256()
        try:
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            logger.error(f"Failed to calculate checksum for {file_path}: {e}")
            return ""

@dataclass
class ScanCache:
    """Cached scan results for a file"""
    file_metadata: FileMetadata
    findings: List[Dict[str, Any]]
    scan_duration: float
    scanner_versions: Dict[str, str]  # Track scanner versions
    cached_at: datetime
    
    def is_valid(self, max_age: timedelta = timedelta(days=7)) -> bool:
        """Check if cache entry is still valid"""
        # Check file metadata
        if not self.file_metadata.is_valid():
            return False
        
        # Check cache age
        if datetime.now() - self.cached_at > max_age:
            return False
        
        # TODO: Check scanner version compatibility
        return True

class CacheManager:
    """
    Intelligent cache manager for scan results
    Supports file-based and memory caching with automatic invalidation
    """
    
    def __init__(self, cache_dir: str = "data/cache", max_memory_entries: int = 1000):
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(parents=True, exist_ok=True)

        # Advanced LRU cache with memory leak detection
        self._memory_cache: Dict[str, ScanCache] = {}
        self._memory_access_times: Dict[str, datetime] = {}
        self._memory_usage_bytes = 0
        self._memory_access_count: Dict[str, int] = {}
        self.max_memory_entries = max_memory_entries
        self.max_memory_bytes = 100 * 1024 * 1024  # 100MB memory limit

        # Advanced memory monitoring
        self._memory_check_interval = 50  # Check every 50 operations
        self._operation_count = 0
        self._memory_leak_threshold = 10 * 1024 * 1024  # 10MB leak threshold
        self._baseline_memory = self._get_process_memory()
        self._memory_growth_history = deque(maxlen=100)
        self._gc_forced_count = 0

        # Memory profiling
        self._memory_profiler_enabled = True
        self._large_object_threshold = 1024 * 1024  # 1MB
        self._large_objects_tracked = {}

        # Start memory monitoring thread
        self._start_memory_monitor()

        # Initialize secure cache key for HMAC signatures
        self.cache_secret = self._get_or_create_cache_secret()
        
        # File cache paths
        self.metadata_file = self.cache_dir / "file_metadata.json"
        self.cache_index_file = self.cache_dir / "cache_index.json"
        
        # Thread safety
        self._lock = threading.RLock()
        
        # Cache statistics
        self._stats = {
            'hits': 0,
            'misses': 0,
            'invalidations': 0,
            'memory_hits': 0,
            'disk_hits': 0
        }
        
        # Load existing cache index
        self._load_cache_index()

        # Cleanup settings
        self._last_cleanup = datetime.now()

        # Initialize secure cache secret
        self.cache_secret = self._get_or_create_cache_secret()

    def _get_or_create_cache_secret(self) -> bytes:
        """Get or create secure cache secret for HMAC signatures"""
        secret_file = self.cache_dir / ".cache_secret"

        try:
            if secret_file.exists():
                with open(secret_file, 'rb') as f:
                    return f.read()
            else:
                # Generate new secret
                secret = secrets.token_bytes(32)
                with open(secret_file, 'wb') as f:
                    f.write(secret)
                # Set secure permissions (owner read/write only)
                secret_file.chmod(0o600)
                return secret
        except Exception as e:
            logger.error(f"Failed to manage cache secret: {e}")
            # Fallback to in-memory secret (less secure but functional)
            return secrets.token_bytes(32)
        self._cleanup_interval = timedelta(hours=6)
    
    def get_cached_results(self, file_path: str) -> Optional[ScanCache]:
        """Get cached scan results for a file"""
        with self._lock:
            cache_key = self._get_cache_key(file_path)
            
            # Check memory cache first
            if cache_key in self._memory_cache:
                cache_entry = self._memory_cache[cache_key]
                if cache_entry.is_valid():
                    self._memory_access_times[cache_key] = datetime.now()
                    self._stats['hits'] += 1
                    self._stats['memory_hits'] += 1
                    return cache_entry
                else:
                    # Remove invalid entry
                    del self._memory_cache[cache_key]
                    del self._memory_access_times[cache_key]
            
            # Check disk cache
            cache_entry = self._load_from_disk(cache_key)
            if cache_entry and cache_entry.is_valid():
                # Add to memory cache
                self._add_to_memory_cache(cache_key, cache_entry)
                self._stats['hits'] += 1
                self._stats['disk_hits'] += 1
                return cache_entry
            
            self._stats['misses'] += 1
            return None
    
    def cache_results(self, file_path: str, findings: List[Dict[str, Any]], 
                     scan_duration: float, scanner_versions: Dict[str, str]):
        """Cache scan results for a file"""
        with self._lock:
            try:
                # Create file metadata
                file_metadata = FileMetadata.from_file(file_path)
                
                # Create cache entry
                cache_entry = ScanCache(
                    file_metadata=file_metadata,
                    findings=findings,
                    scan_duration=scan_duration,
                    scanner_versions=scanner_versions,
                    cached_at=datetime.now()
                )
                
                cache_key = self._get_cache_key(file_path)
                
                # Save to disk
                self._save_to_disk(cache_key, cache_entry)
                
                # Add to memory cache with memory management
                self._add_to_memory_cache(cache_key, cache_entry)

                # Periodic memory cleanup
                self._operation_count += 1
                if self._operation_count % self._memory_check_interval == 0:
                    self._cleanup_memory_cache()

                logger.debug(f"Cached results for {file_path}")
                
            except Exception as e:
                logger.error(f"Failed to cache results for {file_path}: {e}")
    
    def invalidate_file(self, file_path: str):
        """Invalidate cache for a specific file"""
        with self._lock:
            cache_key = self._get_cache_key(file_path)
            
            # Remove from memory cache
            self._memory_cache.pop(cache_key, None)
            self._memory_access_times.pop(cache_key, None)
            
            # Remove from disk cache
            cache_file = self.cache_dir / f"{cache_key}.cache"
            if cache_file.exists():
                cache_file.unlink()
            
            self._stats['invalidations'] += 1
            logger.debug(f"Invalidated cache for {file_path}")
    
    def invalidate_directory(self, directory_path: str):
        """Invalidate cache for all files in a directory"""
        with self._lock:
            directory_path = str(Path(directory_path).resolve())
            keys_to_remove = []
            
            # Find keys to remove from memory cache
            for cache_key, cache_entry in self._memory_cache.items():
                if cache_entry.file_metadata.file_path.startswith(directory_path):
                    keys_to_remove.append(cache_key)
            
            # Remove from memory cache
            for key in keys_to_remove:
                del self._memory_cache[key]
                self._memory_access_times.pop(key, None)
            
            # Remove from disk cache
            for cache_file in self.cache_dir.glob("*.cache"):
                try:
                    cache_entry = self._load_from_disk(cache_file.stem)
                    if (cache_entry and 
                        cache_entry.file_metadata.file_path.startswith(directory_path)):
                        cache_file.unlink()
                        self._stats['invalidations'] += 1
                except Exception as e:
                    logger.error(f"Error invalidating cache file {cache_file}: {e}")
            
            logger.info(f"Invalidated cache for directory {directory_path}")
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache performance statistics"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = (self._stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                **self._stats,
                'hit_rate_percent': round(hit_rate, 2),
                'memory_cache_size': len(self._memory_cache),
                'disk_cache_files': len(list(self.cache_dir.glob("*.cache"))),
                'cache_dir_size_mb': self._get_cache_dir_size() / (1024 * 1024)
            }
    
    def cleanup_cache(self, max_age: timedelta = timedelta(days=30)):
        """Clean up old cache entries"""
        with self._lock:
            current_time = datetime.now()
            
            # Skip if recently cleaned
            if current_time - self._last_cleanup < self._cleanup_interval:
                return
            
            removed_count = 0
            
            # Clean memory cache
            expired_keys = []
            for key, cache_entry in self._memory_cache.items():
                if not cache_entry.is_valid(max_age):
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self._memory_cache[key]
                self._memory_access_times.pop(key, None)
                removed_count += 1
            
            # Clean disk cache
            for cache_file in self.cache_dir.glob("*.cache"):
                try:
                    cache_entry = self._load_from_disk(cache_file.stem)
                    if not cache_entry or not cache_entry.is_valid(max_age):
                        cache_file.unlink()
                        removed_count += 1
                except Exception as e:
                    logger.error(f"Error cleaning cache file {cache_file}: {e}")
            
            self._last_cleanup = current_time
            
            if removed_count > 0:
                logger.info(f"Cleaned up {removed_count} expired cache entries")
    
    def _get_cache_key(self, file_path: str) -> str:
        """Generate cache key for file path"""
        # Use hash of absolute path for consistent keys
        abs_path = str(Path(file_path).resolve())
        return hashlib.md5(abs_path.encode()).hexdigest()
    
    def _add_to_memory_cache(self, cache_key: str, cache_entry: ScanCache):
        """Add entry to memory cache with LRU eviction and memory management"""
        # Calculate entry size
        entry_size = self._calculate_cache_entry_size(cache_entry)

        # Check memory limits before adding
        while (len(self._memory_cache) >= self.max_memory_entries or
               self._memory_usage_bytes + entry_size > self.max_memory_bytes):
            if not self._evict_lru_entry():
                break  # No more entries to evict

        # Add to cache
        self._memory_cache[cache_key] = cache_entry
        self._memory_access_times[cache_key] = datetime.now()
        self._memory_usage_bytes += entry_size

    def _calculate_cache_entry_size(self, cache_entry: ScanCache) -> int:
        """Calculate approximate memory size of cache entry"""
        try:
            import sys
            size = sys.getsizeof(cache_entry)
            size += sys.getsizeof(cache_entry.findings)
            size += sum(sys.getsizeof(finding) for finding in cache_entry.findings)
            size += sys.getsizeof(cache_entry.file_metadata)
            return size
        except Exception:
            return 1024  # Default 1KB estimate

    def _evict_lru_entry(self) -> bool:
        """Evict least recently used entry"""
        if not self._memory_cache:
            return False

        # Find oldest entry
        oldest_key = min(self._memory_access_times.keys(),
                        key=lambda k: self._memory_access_times[k])

        # Calculate size before removal
        if oldest_key in self._memory_cache:
            entry_size = self._calculate_cache_entry_size(self._memory_cache[oldest_key])
            self._memory_usage_bytes -= entry_size

        # Remove entry
        del self._memory_cache[oldest_key]
        del self._memory_access_times[oldest_key]

        return True

    def _cleanup_memory_cache(self):
        """Periodic memory cache cleanup"""
        try:
            # Remove expired entries
            current_time = datetime.now()
            expired_keys = []

            for key, cache_entry in self._memory_cache.items():
                if not cache_entry.is_valid():
                    expired_keys.append(key)

            for key in expired_keys:
                if key in self._memory_cache:
                    entry_size = self._calculate_cache_entry_size(self._memory_cache[key])
                    self._memory_usage_bytes -= entry_size
                    del self._memory_cache[key]
                    del self._memory_access_times[key]

            # Force garbage collection if memory usage is high
            if self._memory_usage_bytes > self.max_memory_bytes * 0.8:
                import gc
                gc.collect()

            logger.debug(f"Memory cache cleanup: {len(expired_keys)} expired entries removed, "
                        f"memory usage: {self._memory_usage_bytes / 1024 / 1024:.1f}MB")

        except Exception as e:
            logger.error(f"Memory cache cleanup failed: {e}")

    def _start_memory_monitor(self):
        """Start advanced memory monitoring thread"""
        import threading

        def memory_monitor_loop():
            while True:
                try:
                    self._advanced_memory_check()
                    time.sleep(30)  # Check every 30 seconds
                except Exception as e:
                    logger.error(f"Memory monitor error: {e}")
                    time.sleep(30)

        monitor_thread = threading.Thread(target=memory_monitor_loop, daemon=True)
        monitor_thread.start()
        logger.info("Advanced memory monitor started")

    def _get_process_memory(self) -> int:
        """Get current process memory usage"""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss
        except Exception:
            return 0

    def _advanced_memory_check(self):
        """Advanced memory leak detection and optimization"""
        try:
            current_memory = self._get_process_memory()
            memory_growth = current_memory - self._baseline_memory

            # Track memory growth history
            self._memory_growth_history.append({
                'timestamp': datetime.now(),
                'memory_usage': current_memory,
                'memory_growth': memory_growth,
                'cache_size': len(self._memory_cache),
                'cache_memory': self._memory_usage_bytes
            })

            # Detect memory leaks
            if memory_growth > self._memory_leak_threshold:
                logger.warning(f"Potential memory leak detected: {memory_growth / 1024 / 1024:.1f}MB growth")
                self._handle_memory_leak()

            # Optimize cache if memory usage is high
            if current_memory > 500 * 1024 * 1024:  # 500MB threshold
                self._aggressive_cache_cleanup()

            # Profile large objects
            if self._memory_profiler_enabled:
                self._profile_large_objects()

        except Exception as e:
            logger.error(f"Advanced memory check failed: {e}")

    def _handle_memory_leak(self):
        """Handle detected memory leak"""
        try:
            # Force garbage collection
            import gc
            collected = gc.collect()
            self._gc_forced_count += 1

            # Aggressive cache cleanup
            self._aggressive_cache_cleanup()

            # Log memory leak details
            logger.warning(f"Memory leak mitigation: GC collected {collected} objects, "
                          f"forced GC count: {self._gc_forced_count}")

            # Update baseline if leak was resolved
            new_memory = self._get_process_memory()
            if new_memory < self._baseline_memory + self._memory_leak_threshold:
                self._baseline_memory = new_memory
                logger.info("Memory leak resolved, baseline updated")

        except Exception as e:
            logger.error(f"Memory leak handling failed: {e}")

    def _aggressive_cache_cleanup(self):
        """Aggressive cache cleanup for memory optimization"""
        try:
            original_size = len(self._memory_cache)

            # Remove 50% of least recently used items
            if self._memory_cache:
                sorted_keys = sorted(
                    self._memory_access_times.keys(),
                    key=lambda k: self._memory_access_times[k]
                )

                keys_to_remove = sorted_keys[:len(sorted_keys) // 2]

                for key in keys_to_remove:
                    if key in self._memory_cache:
                        entry_size = self._calculate_cache_entry_size(self._memory_cache[key])
                        self._memory_usage_bytes -= entry_size
                        del self._memory_cache[key]
                        del self._memory_access_times[key]
                        self._memory_access_count.pop(key, None)

            # Force garbage collection
            import gc
            gc.collect()

            logger.info(f"Aggressive cache cleanup: removed {original_size - len(self._memory_cache)} entries")

        except Exception as e:
            logger.error(f"Aggressive cache cleanup failed: {e}")

    def _profile_large_objects(self):
        """Profile and track large objects in cache"""
        try:
            large_objects = {}

            for key, cache_entry in self._memory_cache.items():
                entry_size = self._calculate_cache_entry_size(cache_entry)

                if entry_size > self._large_object_threshold:
                    large_objects[key] = {
                        'size': entry_size,
                        'type': type(cache_entry).__name__,
                        'access_count': self._memory_access_count.get(key, 0),
                        'last_access': self._memory_access_times.get(key)
                    }

            # Update tracked large objects
            self._large_objects_tracked = large_objects

            # Log if too many large objects
            if len(large_objects) > 10:
                total_large_size = sum(obj['size'] for obj in large_objects.values())
                logger.warning(f"High number of large objects in cache: {len(large_objects)} "
                              f"objects using {total_large_size / 1024 / 1024:.1f}MB")

        except Exception as e:
            logger.error(f"Large object profiling failed: {e}")
    
    def _evict_lru_entries(self):
        """Evict least recently used entries from memory cache"""
        # Remove 10% of entries
        evict_count = max(1, self.max_memory_entries // 10)
        
        # Sort by access time
        sorted_keys = sorted(
            self._memory_access_times.keys(),
            key=lambda k: self._memory_access_times[k]
        )
        
        # Remove oldest entries
        for key in sorted_keys[:evict_count]:
            del self._memory_cache[key]
            del self._memory_access_times[key]
    
    def _save_to_disk(self, cache_key: str, cache_entry: ScanCache):
        """Save cache entry to disk with secure serialization"""
        cache_file = self.cache_dir / f"{cache_key}.cache"

        try:
            # Convert to dictionary for JSON serialization
            cache_data = {
                'file_metadata': {
                    'file_path': cache_entry.file_metadata.file_path,
                    'file_size': cache_entry.file_metadata.file_size,
                    'modified_time': cache_entry.file_metadata.modified_time.isoformat(),
                    'checksum': cache_entry.file_metadata.checksum,
                    'file_type': cache_entry.file_metadata.file_type
                },
                'findings': cache_entry.findings,
                'scan_duration': cache_entry.scan_duration,
                'scanner_versions': cache_entry.scanner_versions,
                'cached_at': cache_entry.cached_at.isoformat()
            }

            # Serialize to JSON
            json_data = json.dumps(cache_data, separators=(',', ':'))

            # Create HMAC signature
            signature = hmac.new(
                self.cache_secret,
                json_data.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            # Save with signature
            signed_data = {
                'data': cache_data,
                'signature': signature
            }

            with gzip.open(cache_file, 'wt', encoding='utf-8') as f:
                json.dump(signed_data, f, separators=(',', ':'))

        except Exception as e:
            logger.error(f"Failed to save cache to disk: {e}")
    
    def _load_from_disk(self, cache_key: str) -> Optional[ScanCache]:
        """Load cache entry from disk with signature verification"""
        cache_file = self.cache_dir / f"{cache_key}.cache"

        if not cache_file.exists():
            return None

        try:
            with gzip.open(cache_file, 'rt', encoding='utf-8') as f:
                signed_data = json.load(f)

            # Verify signature
            if 'data' not in signed_data or 'signature' not in signed_data:
                logger.warning(f"Invalid cache file format: {cache_file}")
                cache_file.unlink()
                return None

            # Verify HMAC signature
            cache_data = signed_data['data']
            expected_signature = signed_data['signature']

            json_data = json.dumps(cache_data, separators=(',', ':'))
            actual_signature = hmac.new(
                self.cache_secret,
                json_data.encode('utf-8'),
                hashlib.sha256
            ).hexdigest()

            if not hmac.compare_digest(expected_signature, actual_signature):
                logger.warning(f"Cache signature verification failed: {cache_file}")
                cache_file.unlink()
                return None

            # Reconstruct ScanCache object
            file_metadata = FileMetadata(
                file_path=cache_data['file_metadata']['file_path'],
                file_size=cache_data['file_metadata']['file_size'],
                modified_time=datetime.fromisoformat(cache_data['file_metadata']['modified_time']),
                checksum=cache_data['file_metadata']['checksum'],
                file_type=cache_data['file_metadata']['file_type']
            )

            return ScanCache(
                file_metadata=file_metadata,
                findings=cache_data['findings'],
                scan_duration=cache_data['scan_duration'],
                scanner_versions=cache_data['scanner_versions'],
                cached_at=datetime.fromisoformat(cache_data['cached_at'])
            )

        except Exception as e:
            logger.error(f"Failed to load cache from disk: {e}")
            # Remove corrupted cache file
            try:
                cache_file.unlink()
            except:
                pass
            return None
    
    def _load_cache_index(self):
        """Load cache index for faster lookups"""
        # TODO: Implement cache index for faster file lookups
        pass
    
    def _get_cache_dir_size(self) -> int:
        """Get total size of cache directory in bytes"""
        total_size = 0
        try:
            for cache_file in self.cache_dir.rglob("*"):
                if cache_file.is_file():
                    total_size += cache_file.stat().st_size
        except Exception as e:
            logger.error(f"Error calculating cache directory size: {e}")
        return total_size

# Global cache manager instance
cache_manager = CacheManager()
