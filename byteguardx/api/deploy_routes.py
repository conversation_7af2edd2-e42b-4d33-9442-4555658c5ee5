"""
Self-Hosted Deployment Wizard API Routes
Provides endpoints for guided deployment setup
"""

import os
import json
import logging
import subprocess
import tempfile
from typing import Dict, List, Optional
from flask import Blueprint, request, jsonify, Response
from pathlib import Path
import secrets
import string

logger = logging.getLogger(__name__)

# Create blueprint
deploy_bp = Blueprint('deploy', __name__, url_prefix='/api/deploy')

# Deployment configuration
DOCKER_COMPOSE_TEMPLATE = """version: '3.8'

services:
  byteguardx-backend:
    image: byteguardx/backend:latest
    container_name: byteguardx-backend
    ports:
      - "{backend_port}:5000"
    environment:
      - DATABASE_URL={database_url}
      - JWT_SECRET_KEY={jwt_secret}
      - FLASK_ENV=production
      - SMTP_HOST={smtp_host}
      - SMTP_PORT={smtp_port}
      - SMTP_USERNAME={smtp_username}
      - SMTP_PASSWORD={smtp_password}
      - EMAIL_FROM={email_from}
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./reports:/app/reports
    restart: unless-stopped
    networks:
      - byteguardx-network

  byteguardx-frontend:
    image: byteguardx/frontend:latest
    container_name: byteguardx-frontend
    ports:
      - "{frontend_port}:80"
    environment:
      - REACT_APP_API_URL=http://localhost:{backend_port}
    depends_on:
      - byteguardx-backend
    restart: unless-stopped
    networks:
      - byteguardx-network

  byteguardx-db:
    image: postgres:13
    container_name: byteguardx-db
    environment:
      - POSTGRES_DB={db_name}
      - POSTGRES_USER={db_user}
      - POSTGRES_PASSWORD={db_password}
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped
    networks:
      - byteguardx-network

  redis:
    image: redis:7-alpine
    container_name: byteguardx-redis
    restart: unless-stopped
    networks:
      - byteguardx-network

volumes:
  postgres_data:

networks:
  byteguardx-network:
    driver: bridge
"""

ENV_TEMPLATE = """# ByteGuardX Production Configuration
# Generated by ByteGuardX Deployment Wizard

# Database Configuration
DATABASE_URL={database_url}
DB_HOST={db_host}
DB_PORT={db_port}
DB_NAME={db_name}
DB_USER={db_user}
DB_PASSWORD={db_password}

# Security Configuration
JWT_SECRET_KEY={jwt_secret}
ENCRYPTION_KEY={encryption_key}
SESSION_SECRET={session_secret}

# Email Configuration
SMTP_HOST={smtp_host}
SMTP_PORT={smtp_port}
SMTP_USERNAME={smtp_username}
SMTP_PASSWORD={smtp_password}
SMTP_USE_TLS={smtp_use_tls}
EMAIL_FROM={email_from}

# Application Configuration
FLASK_ENV=production
DEBUG=false
ALLOWED_ORIGINS={allowed_origins}
FRONTEND_URL={frontend_url}

# Monitoring Configuration
ENABLE_HEALTH_MONITORING=true
HEALTH_CHECK_INTERVAL=60
HEALTH_RETENTION_HOURS=24

# Rate Limiting
RATE_LIMIT_ENABLED=true
RATE_LIMIT_PER_MINUTE=60
RATE_LIMIT_BURST=10

# File Upload Configuration
MAX_UPLOAD_SIZE_MB=100
ALLOWED_EXTENSIONS=.zip,.tar,.gz,.py,.js,.json,.yaml,.yml

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE_PATH=logs/byteguardx.log
LOG_MAX_SIZE_MB=10
LOG_BACKUP_COUNT=5
"""


def is_local_request():
    """Check if request is from localhost (security measure)"""
    remote_addr = request.environ.get('REMOTE_ADDR', '')
    return remote_addr in ['127.0.0.1', '::1', 'localhost']


@deploy_bp.before_request
def check_local_access():
    """Ensure deployment endpoints are only accessible locally"""
    if not is_local_request():
        return jsonify({
            'error': 'Deployment wizard is only accessible from localhost for security reasons'
        }), 403


@deploy_bp.route('/check-requirements', methods=['GET'])
def check_requirements():
    """Check system requirements for deployment"""
    try:
        requirements = {
            'docker': check_docker(),
            'docker_compose': check_docker_compose(),
            'disk_space': check_disk_space(),
            'memory': check_memory(),
            'ports': check_ports([5000, 3000, 5432, 6379])
        }
        
        all_met = all(req['status'] for req in requirements.values())
        
        return jsonify({
            'requirements': requirements,
            'all_requirements_met': all_met,
            'recommendations': get_recommendations(requirements)
        })
        
    except Exception as e:
        logger.error(f"Error checking requirements: {e}")
        return jsonify({'error': 'Failed to check requirements'}), 500


@deploy_bp.route('/generate-config', methods=['POST'])
def generate_config():
    """Generate deployment configuration"""
    try:
        data = request.get_json()
        
        # Validate required fields
        required_fields = ['db_name', 'db_user', 'admin_email', 'domain']
        for field in required_fields:
            if field not in data:
                return jsonify({'error': f'Missing required field: {field}'}), 400
        
        # Generate secure secrets
        config = {
            'db_name': data['db_name'],
            'db_user': data['db_user'],
            'db_password': data.get('db_password') or generate_password(16),
            'db_host': data.get('db_host', 'byteguardx-db'),
            'db_port': data.get('db_port', '5432'),
            'jwt_secret': generate_password(32),
            'encryption_key': generate_password(32),
            'session_secret': generate_password(24),
            'admin_email': data['admin_email'],
            'domain': data['domain'],
            'frontend_port': data.get('frontend_port', '3000'),
            'backend_port': data.get('backend_port', '5000'),
            'smtp_host': data.get('smtp_host', ''),
            'smtp_port': data.get('smtp_port', '587'),
            'smtp_username': data.get('smtp_username', ''),
            'smtp_password': data.get('smtp_password', ''),
            'smtp_use_tls': data.get('smtp_use_tls', 'true'),
            'email_from': data.get('email_from', f"noreply@{data['domain']}")
        }
        
        # Build URLs
        if data['domain'] == 'localhost':
            config['frontend_url'] = f"http://localhost:{config['frontend_port']}"
            config['backend_url'] = f"http://localhost:{config['backend_port']}"
            config['allowed_origins'] = f"http://localhost:{config['frontend_port']}"
        else:
            protocol = 'https' if data.get('ssl_enabled', True) else 'http'
            config['frontend_url'] = f"{protocol}://{data['domain']}"
            config['backend_url'] = f"{protocol}://api.{data['domain']}"
            config['allowed_origins'] = config['frontend_url']
        
        # Build database URL
        config['database_url'] = (
            f"postgresql://{config['db_user']}:{config['db_password']}"
            f"@{config['db_host']}:{config['db_port']}/{config['db_name']}"
        )
        
        return jsonify({
            'config': config,
            'message': 'Configuration generated successfully'
        })
        
    except Exception as e:
        logger.error(f"Error generating config: {e}")
        return jsonify({'error': 'Failed to generate configuration'}), 500


@deploy_bp.route('/create-files', methods=['POST'])
def create_deployment_files():
    """Create deployment files (docker-compose.yml, .env)"""
    try:
        data = request.get_json()
        config = data.get('config', {})
        
        if not config:
            return jsonify({'error': 'Configuration required'}), 400
        
        # Create deployment directory
        deploy_dir = Path('deployment')
        deploy_dir.mkdir(exist_ok=True)
        
        # Create docker-compose.yml
        docker_compose_content = DOCKER_COMPOSE_TEMPLATE.format(**config)
        docker_compose_path = deploy_dir / 'docker-compose.yml'
        
        with open(docker_compose_path, 'w') as f:
            f.write(docker_compose_content)
        
        # Create .env file
        env_content = ENV_TEMPLATE.format(**config)
        env_path = deploy_dir / '.env'
        
        with open(env_path, 'w') as f:
            f.write(env_content)
        
        # Create data directories
        for directory in ['data', 'logs', 'reports']:
            (deploy_dir / directory).mkdir(exist_ok=True)
        
        # Create nginx configuration if needed
        if config.get('domain') != 'localhost':
            nginx_config = create_nginx_config(config)
            nginx_path = deploy_dir / 'nginx.conf'
            
            with open(nginx_path, 'w') as f:
                f.write(nginx_config)
        
        return jsonify({
            'message': 'Deployment files created successfully',
            'files': [
                'docker-compose.yml',
                '.env',
                'nginx.conf' if config.get('domain') != 'localhost' else None
            ],
            'deployment_dir': str(deploy_dir.absolute())
        })
        
    except Exception as e:
        logger.error(f"Error creating deployment files: {e}")
        return jsonify({'error': 'Failed to create deployment files'}), 500


@deploy_bp.route('/deploy', methods=['POST'])
def start_deployment():
    """Start the deployment process"""
    try:
        data = request.get_json()
        deployment_dir = data.get('deployment_dir', 'deployment')
        
        if not Path(deployment_dir).exists():
            return jsonify({'error': 'Deployment directory not found'}), 400
        
        # Change to deployment directory
        original_dir = os.getcwd()
        os.chdir(deployment_dir)
        
        try:
            # Start deployment process
            deployment_id = generate_password(8)
            
            # This would start the actual deployment in a background process
            # For now, we'll simulate it
            
            return jsonify({
                'deployment_id': deployment_id,
                'message': 'Deployment started successfully',
                'status': 'starting'
            })
            
        finally:
            os.chdir(original_dir)
            
    except Exception as e:
        logger.error(f"Error starting deployment: {e}")
        return jsonify({'error': 'Failed to start deployment'}), 500


@deploy_bp.route('/status/<deployment_id>', methods=['GET'])
def get_deployment_status(deployment_id):
    """Get deployment status"""
    try:
        # Mock deployment status - in production this would track real deployment
        status = {
            'deployment_id': deployment_id,
            'status': 'completed',
            'progress': 100,
            'steps': [
                {'name': 'Creating containers', 'status': 'completed', 'timestamp': '2024-01-15T10:30:00Z'},
                {'name': 'Starting database', 'status': 'completed', 'timestamp': '2024-01-15T10:31:00Z'},
                {'name': 'Starting backend', 'status': 'completed', 'timestamp': '2024-01-15T10:32:00Z'},
                {'name': 'Starting frontend', 'status': 'completed', 'timestamp': '2024-01-15T10:33:00Z'},
                {'name': 'Running health checks', 'status': 'completed', 'timestamp': '2024-01-15T10:34:00Z'}
            ],
            'services': {
                'backend': {'status': 'running', 'health': 'healthy'},
                'frontend': {'status': 'running', 'health': 'healthy'},
                'database': {'status': 'running', 'health': 'healthy'},
                'redis': {'status': 'running', 'health': 'healthy'}
            },
            'urls': {
                'frontend': 'http://localhost:3000',
                'backend': 'http://localhost:5000',
                'admin': 'http://localhost:3000/admin'
            }
        }
        
        return jsonify(status)
        
    except Exception as e:
        logger.error(f"Error getting deployment status: {e}")
        return jsonify({'error': 'Failed to get deployment status'}), 500


@deploy_bp.route('/logs/<deployment_id>', methods=['GET'])
def get_deployment_logs(deployment_id):
    """Stream deployment logs"""
    try:
        def generate_logs():
            # Mock log streaming - in production this would stream real logs
            logs = [
                "Starting ByteGuardX deployment...",
                "Pulling Docker images...",
                "Creating network 'byteguardx-network'...",
                "Creating volume 'postgres_data'...",
                "Starting database container...",
                "Database is ready!",
                "Starting Redis container...",
                "Redis is ready!",
                "Starting backend container...",
                "Backend is ready!",
                "Starting frontend container...",
                "Frontend is ready!",
                "Running health checks...",
                "All services are healthy!",
                "Deployment completed successfully!"
            ]
            
            for log in logs:
                yield f"data: {json.dumps({'message': log, 'timestamp': '2024-01-15T10:30:00Z'})}\n\n"
        
        return Response(generate_logs(), mimetype='text/plain')
        
    except Exception as e:
        logger.error(f"Error streaming logs: {e}")
        return jsonify({'error': 'Failed to stream logs'}), 500


# Helper functions

def check_docker():
    """Check if Docker is installed and running"""
    try:
        result = subprocess.run(['docker', '--version'], capture_output=True, text=True)
        return {
            'status': result.returncode == 0,
            'version': result.stdout.strip() if result.returncode == 0 else None,
            'message': 'Docker is installed and accessible' if result.returncode == 0 else 'Docker not found'
        }
    except FileNotFoundError:
        return {
            'status': False,
            'version': None,
            'message': 'Docker not installed'
        }


def check_docker_compose():
    """Check if Docker Compose is installed"""
    try:
        result = subprocess.run(['docker-compose', '--version'], capture_output=True, text=True)
        return {
            'status': result.returncode == 0,
            'version': result.stdout.strip() if result.returncode == 0 else None,
            'message': 'Docker Compose is installed' if result.returncode == 0 else 'Docker Compose not found'
        }
    except FileNotFoundError:
        return {
            'status': False,
            'version': None,
            'message': 'Docker Compose not installed'
        }


def check_disk_space():
    """Check available disk space"""
    try:
        import shutil
        total, used, free = shutil.disk_usage('/')
        free_gb = free // (1024**3)
        
        return {
            'status': free_gb >= 5,  # Require at least 5GB
            'free_gb': free_gb,
            'message': f'{free_gb}GB available' if free_gb >= 5 else f'Only {free_gb}GB available, need at least 5GB'
        }
    except Exception:
        return {
            'status': False,
            'free_gb': 0,
            'message': 'Could not check disk space'
        }


def check_memory():
    """Check available memory"""
    try:
        import psutil
        memory = psutil.virtual_memory()
        available_gb = memory.available // (1024**3)
        
        return {
            'status': available_gb >= 2,  # Require at least 2GB
            'available_gb': available_gb,
            'message': f'{available_gb}GB available' if available_gb >= 2 else f'Only {available_gb}GB available, need at least 2GB'
        }
    except ImportError:
        return {
            'status': True,  # Assume OK if psutil not available
            'available_gb': 'unknown',
            'message': 'Memory check skipped (psutil not available)'
        }


def check_ports(ports):
    """Check if required ports are available"""
    import socket
    
    results = {}
    for port in ports:
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(('localhost', port))
                results[port] = {
                    'status': True,
                    'message': f'Port {port} is available'
                }
        except OSError:
            results[port] = {
                'status': False,
                'message': f'Port {port} is in use'
            }
    
    return results


def get_recommendations(requirements):
    """Get recommendations based on requirement check results"""
    recommendations = []
    
    if not requirements['docker']['status']:
        recommendations.append('Install Docker from https://docs.docker.com/get-docker/')
    
    if not requirements['docker_compose']['status']:
        recommendations.append('Install Docker Compose from https://docs.docker.com/compose/install/')
    
    if not requirements['disk_space']['status']:
        recommendations.append('Free up disk space - at least 5GB required')
    
    if not requirements['memory']['status']:
        recommendations.append('Ensure at least 2GB of available memory')
    
    for port, result in requirements['ports'].items():
        if not result['status']:
            recommendations.append(f'Free up port {port} or configure alternative port')
    
    return recommendations


def generate_password(length=16):
    """Generate a secure random password"""
    alphabet = string.ascii_letters + string.digits + "!@#$%^&*"
    return ''.join(secrets.choice(alphabet) for _ in range(length))


def create_nginx_config(config):
    """Create nginx configuration for production deployment"""
    return f"""
server {{
    listen 80;
    server_name {config['domain']};
    return 301 https://$server_name$request_uri;
}}

server {{
    listen 443 ssl http2;
    server_name {config['domain']};

    ssl_certificate /etc/ssl/certs/byteguardx.crt;
    ssl_certificate_key /etc/ssl/private/byteguardx.key;

    location / {{
        proxy_pass http://byteguardx-frontend:80;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}

    location /api/ {{
        proxy_pass http://byteguardx-backend:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}
}}
"""
