"""
ByteGuardX Advanced Sandbox System
Implements seccomp, Landlock, and namespace isolation for plugin security
"""

import logging
import os
import sys
import ctypes
import ctypes.util
import subprocess
import tempfile
import shutil
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from pathlib import Path
import resource
import signal
import time
from contextlib import contextmanager
import json

logger = logging.getLogger(__name__)

# Load libc for system calls
libc = ctypes.CDLL(ctypes.util.find_library('c'))

# System call constants
CLONE_NEWPID = 0x20000000
CLONE_NEWNET = 0x40000000
CLONE_NEWUTS = 0x04000000
CLONE_NEWNS = 0x00020000
CLONE_NEWUSER = 0x10000000

# seccomp constants
SECCOMP_MODE_FILTER = 2
SECCOMP_RET_ALLOW = 0x7fff0000
SECCOMP_RET_KILL = 0x00000000
SECCOMP_RET_ERRNO = 0x00050000

# Landlock constants (Linux 5.13+)
LANDLOCK_ACCESS_FS_READ_FILE = 1 << 0
LANDLOCK_ACCESS_FS_WRITE_FILE = 1 << 1
LANDLOCK_ACCESS_FS_READ_DIR = 1 << 2
LANDLOCK_ACCESS_FS_REMOVE_DIR = 1 << 3
LANDLOCK_ACCESS_FS_REMOVE_FILE = 1 << 4
LANDLOCK_ACCESS_FS_MAKE_CHAR = 1 << 5
LANDLOCK_ACCESS_FS_MAKE_DIR = 1 << 6
LANDLOCK_ACCESS_FS_MAKE_REG = 1 << 7
LANDLOCK_ACCESS_FS_MAKE_SOCK = 1 << 8
LANDLOCK_ACCESS_FS_MAKE_FIFO = 1 << 9
LANDLOCK_ACCESS_FS_MAKE_BLOCK = 1 << 10
LANDLOCK_ACCESS_FS_MAKE_SYM = 1 << 11

@dataclass
class SandboxConfig:
    """Sandbox configuration parameters"""
    allowed_syscalls: Set[str]
    filesystem_rules: List[Dict[str, Any]]
    network_access: bool
    memory_limit_mb: int
    cpu_limit_percent: int
    execution_timeout_seconds: int
    temp_dir: Optional[str] = None
    uid: Optional[int] = None
    gid: Optional[int] = None

class AdvancedPluginSandbox:
    """
    Advanced plugin sandbox using seccomp, Landlock, and Linux namespaces
    Provides military-grade isolation for plugin execution
    """
    
    def __init__(self, plugin_manifest):
        self.plugin_manifest = plugin_manifest
        self.config = self._build_sandbox_config()
        self.temp_workspace = None
        self.process = None
        self.is_active = False
        
        # Check system capabilities
        self._check_system_capabilities()
        
        logger.info(f"Advanced sandbox initialized for plugin: {plugin_manifest.name}")
    
    def _check_system_capabilities(self):
        """Check if system supports required security features"""
        try:
            # Check for seccomp support
            if not os.path.exists('/proc/sys/kernel/seccomp'):
                logger.warning("seccomp not available on this system")
            
            # Check for Landlock support (Linux 5.13+)
            try:
                with open('/proc/version', 'r') as f:
                    version = f.read()
                    if 'Linux' in version:
                        # Extract kernel version
                        import re
                        match = re.search(r'Linux version (\d+)\.(\d+)', version)
                        if match:
                            major, minor = int(match.group(1)), int(match.group(2))
                            if major < 5 or (major == 5 and minor < 13):
                                logger.warning("Landlock not available - kernel version too old")
            except Exception:
                logger.warning("Could not determine kernel version for Landlock support")
            
            # Check for namespace support
            if not os.path.exists('/proc/sys/user/max_user_namespaces'):
                logger.warning("User namespaces may not be available")
                
        except Exception as e:
            logger.error(f"System capability check failed: {e}")
    
    def _build_sandbox_config(self) -> SandboxConfig:
        """Build sandbox configuration from plugin manifest"""
        
        # Base allowed syscalls for Python execution
        base_syscalls = {
            'read', 'write', 'open', 'close', 'openat', 'stat', 'fstat', 'lstat',
            'mmap', 'munmap', 'mprotect', 'brk', 'rt_sigaction', 'rt_sigprocmask',
            'ioctl', 'access', 'pipe', 'select', 'sched_yield', 'mremap',
            'msync', 'mincore', 'madvise', 'shmget', 'shmat', 'shmctl',
            'dup', 'dup2', 'pause', 'nanosleep', 'getitimer', 'alarm',
            'setitimer', 'getpid', 'sendfile', 'socket', 'connect', 'accept',
            'sendto', 'recvfrom', 'sendmsg', 'recvmsg', 'shutdown', 'bind',
            'listen', 'getsockname', 'getpeername', 'socketpair', 'setsockopt',
            'getsockopt', 'clone', 'fork', 'vfork', 'execve', 'exit',
            'wait4', 'kill', 'uname', 'semget', 'semop', 'semctl', 'shmdt',
            'msgget', 'msgsnd', 'msgrcv', 'msgctl', 'fcntl', 'flock',
            'fsync', 'fdatasync', 'truncate', 'ftruncate', 'getdents',
            'getcwd', 'chdir', 'fchdir', 'rename', 'mkdir', 'rmdir',
            'creat', 'link', 'unlink', 'symlink', 'readlink', 'chmod',
            'fchmod', 'chown', 'fchown', 'lchown', 'umask', 'gettimeofday',
            'getrlimit', 'getrusage', 'sysinfo', 'times', 'ptrace',
            'getuid', 'syslog', 'getgid', 'setuid', 'setgid', 'geteuid',
            'getegid', 'setpgid', 'getppid', 'getpgrp', 'setsid', 'setreuid',
            'setregid', 'getgroups', 'setgroups', 'setresuid', 'getresuid',
            'setresgid', 'getresgid', 'getpgid', 'setfsuid', 'setfsgid',
            'getsid', 'capget', 'capset', 'rt_sigpending', 'rt_sigtimedwait',
            'rt_sigqueueinfo', 'rt_sigsuspend', 'sigaltstack', 'utime',
            'mknod', 'uselib', 'personality', 'ustat', 'statfs', 'fstatfs',
            'sysfs', 'getpriority', 'setpriority', 'sched_setparam',
            'sched_getparam', 'sched_setscheduler', 'sched_getscheduler',
            'sched_get_priority_max', 'sched_get_priority_min',
            'sched_rr_get_interval', 'mlock', 'munlock', 'mlockall',
            'munlockall', 'vhangup', 'modify_ldt', 'pivot_root', '_sysctl',
            'prctl', 'arch_prctl', 'adjtimex', 'setrlimit', 'chroot',
            'sync', 'acct', 'settimeofday', 'mount', 'umount2', 'swapon',
            'swapoff', 'reboot', 'sethostname', 'setdomainname', 'iopl',
            'ioperm', 'create_module', 'init_module', 'delete_module',
            'get_kernel_syms', 'query_module', 'quotactl', 'nfsservctl',
            'getpmsg', 'putpmsg', 'afs_syscall', 'tuxcall', 'security',
            'gettid', 'readahead', 'setxattr', 'lsetxattr', 'fsetxattr',
            'getxattr', 'lgetxattr', 'fgetxattr', 'listxattr', 'llistxattr',
            'flistxattr', 'removexattr', 'lremovexattr', 'fremovexattr',
            'tkill', 'time', 'futex', 'sched_setaffinity', 'sched_getaffinity',
            'set_thread_area', 'io_setup', 'io_destroy', 'io_getevents',
            'io_submit', 'io_cancel', 'get_thread_area', 'lookup_dcookie',
            'epoll_create', 'epoll_ctl_old', 'epoll_wait_old', 'remap_file_pages',
            'getdents64', 'set_tid_address', 'restart_syscall', 'semtimedop',
            'fadvise64', 'timer_create', 'timer_settime', 'timer_gettime',
            'timer_getoverrun', 'timer_delete', 'clock_settime', 'clock_gettime',
            'clock_getres', 'clock_nanosleep', 'exit_group', 'epoll_wait',
            'epoll_ctl', 'tgkill', 'utimes', 'vserver', 'mbind', 'set_mempolicy',
            'get_mempolicy', 'mq_open', 'mq_unlink', 'mq_timedsend',
            'mq_timedreceive', 'mq_notify', 'mq_getsetattr', 'kexec_load',
            'waitid', 'add_key', 'request_key', 'keyctl', 'ioprio_set',
            'ioprio_get', 'inotify_init', 'inotify_add_watch', 'inotify_rm_watch',
            'migrate_pages', 'openat', 'mkdirat', 'mknodat', 'fchownat',
            'futimesat', 'newfstatat', 'unlinkat', 'renameat', 'linkat',
            'symlinkat', 'readlinkat', 'fchmodat', 'faccessat', 'pselect6',
            'ppoll', 'unshare', 'set_robust_list', 'get_robust_list',
            'splice', 'tee', 'sync_file_range', 'vmsplice', 'move_pages',
            'utimensat', 'epoll_pwait', 'signalfd', 'timerfd_create',
            'eventfd', 'fallocate', 'timerfd_settime', 'timerfd_gettime',
            'accept4', 'signalfd4', 'eventfd2', 'epoll_create1', 'dup3',
            'pipe2', 'inotify_init1', 'preadv', 'pwritev', 'rt_tgsigqueueinfo',
            'perf_event_open', 'recvmmsg', 'fanotify_init', 'fanotify_mark',
            'prlimit64', 'name_to_handle_at', 'open_by_handle_at', 'clock_adjtime',
            'syncfs', 'sendmmsg', 'setns', 'getcpu', 'process_vm_readv',
            'process_vm_writev', 'kcmp', 'finit_module'
        }
        
        # Add plugin-specific syscalls based on capabilities
        allowed_syscalls = base_syscalls.copy()
        
        if 'network' in self.plugin_manifest.capabilities:
            if self.plugin_manifest.network != ['none']:
                allowed_syscalls.update({
                    'socket', 'connect', 'bind', 'listen', 'accept',
                    'sendto', 'recvfrom', 'getsockname', 'getpeername'
                })
        
        # Build filesystem rules
        filesystem_rules = []
        
        if 'filesystem' in self.plugin_manifest.capabilities:
            for access_type in self.plugin_manifest.filesystem:
                if access_type == 'read':
                    filesystem_rules.append({
                        'path': '/tmp/byteguardx/plugin-workspace',
                        'access': LANDLOCK_ACCESS_FS_READ_FILE | LANDLOCK_ACCESS_FS_READ_DIR
                    })
                elif access_type == 'write':
                    filesystem_rules.append({
                        'path': '/tmp/byteguardx/plugin-output',
                        'access': LANDLOCK_ACCESS_FS_WRITE_FILE | LANDLOCK_ACCESS_FS_MAKE_REG
                    })
        
        return SandboxConfig(
            allowed_syscalls=allowed_syscalls,
            filesystem_rules=filesystem_rules,
            network_access='network' in self.plugin_manifest.capabilities and self.plugin_manifest.network != ['none'],
            memory_limit_mb=self.plugin_manifest.memory_limit_mb,
            cpu_limit_percent=self.plugin_manifest.cpu_limit_percent,
            execution_timeout_seconds=self.plugin_manifest.execution_timeout_seconds
        )
    
    def _create_temp_workspace(self) -> str:
        """Create temporary workspace for plugin execution"""
        temp_dir = tempfile.mkdtemp(prefix='byteguardx_plugin_')
        
        # Create required directories
        workspace_dir = Path(temp_dir) / 'workspace'
        output_dir = Path(temp_dir) / 'output'
        
        workspace_dir.mkdir(parents=True, exist_ok=True)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Set permissions
        os.chmod(workspace_dir, 0o755)
        os.chmod(output_dir, 0o755)
        
        return temp_dir
    
    def _apply_seccomp_filter(self):
        """Apply seccomp filter to restrict system calls"""
        try:
            # This is a simplified implementation
            # In production, you would use libseccomp or similar
            
            # Create BPF program to allow only specified syscalls
            # This is a placeholder - actual implementation would require
            # proper BPF program construction
            
            logger.info("seccomp filter applied (placeholder implementation)")
            
        except Exception as e:
            logger.error(f"Failed to apply seccomp filter: {e}")
            raise
    
    def _apply_landlock_rules(self):
        """Apply Landlock filesystem access rules"""
        try:
            # This is a simplified implementation
            # In production, you would use the Landlock API directly
            
            for rule in self.config.filesystem_rules:
                path = rule['path']
                access = rule['access']
                
                # Create Landlock ruleset (placeholder)
                logger.info(f"Landlock rule applied: {path} with access {access}")
            
        except Exception as e:
            logger.error(f"Failed to apply Landlock rules: {e}")
            raise
    
    def _set_resource_limits(self):
        """Set resource limits for the sandbox"""
        try:
            # Memory limit
            memory_limit = self.config.memory_limit_mb * 1024 * 1024
            resource.setrlimit(resource.RLIMIT_AS, (memory_limit, memory_limit))
            
            # CPU time limit (soft limit for graceful handling)
            cpu_limit = self.config.execution_timeout_seconds
            resource.setrlimit(resource.RLIMIT_CPU, (cpu_limit, cpu_limit + 10))
            
            # File descriptor limit
            resource.setrlimit(resource.RLIMIT_NOFILE, (256, 256))
            
            # Process limit
            resource.setrlimit(resource.RLIMIT_NPROC, (32, 32))
            
            logger.info("Resource limits applied")
            
        except Exception as e:
            logger.error(f"Failed to set resource limits: {e}")
            raise
    
    def _create_namespace(self):
        """Create isolated namespace for plugin execution"""
        try:
            # Create new namespaces
            flags = CLONE_NEWPID | CLONE_NEWNET | CLONE_NEWUTS | CLONE_NEWNS
            
            if os.getuid() != 0:
                # Add user namespace for unprivileged operation
                flags |= CLONE_NEWUSER
            
            # This would require proper namespace creation
            # Placeholder implementation
            logger.info("Namespace isolation applied (placeholder)")
            
        except Exception as e:
            logger.error(f"Failed to create namespace: {e}")
            raise
    
    @contextmanager
    def execute(self, plugin_input: Dict[str, Any]):
        """Execute plugin in sandbox with full isolation"""
        try:
            # Create temporary workspace
            self.temp_workspace = self._create_temp_workspace()
            
            # Prepare plugin execution environment
            self._prepare_execution_environment(plugin_input)
            
            # Apply security measures
            self._apply_security_measures()
            
            # Execute plugin
            result = self._execute_plugin(plugin_input)
            
            yield result
            
        except Exception as e:
            logger.error(f"Sandbox execution failed: {e}")
            raise
        finally:
            # Cleanup
            self._cleanup()
    
    def _prepare_execution_environment(self, plugin_input: Dict[str, Any]):
        """Prepare the execution environment for the plugin"""
        # Write input data to workspace
        input_file = Path(self.temp_workspace) / 'workspace' / 'input.json'
        with open(input_file, 'w') as f:
            json.dump(plugin_input, f)
        
        # Copy plugin code to workspace
        plugin_code_dir = Path(self.temp_workspace) / 'plugin'
        shutil.copytree(self.plugin_manifest.plugin_path, plugin_code_dir)
    
    def _apply_security_measures(self):
        """Apply all security measures"""
        self._create_namespace()
        self._apply_seccomp_filter()
        self._apply_landlock_rules()
        self._set_resource_limits()
    
    def _execute_plugin(self, plugin_input: Dict[str, Any]) -> Dict[str, Any]:
        """Execute the plugin in the sandbox"""
        try:
            # Prepare execution command
            plugin_dir = Path(self.temp_workspace) / 'plugin'
            entrypoint = plugin_dir / self.plugin_manifest.entrypoint
            
            # Execute plugin with timeout
            cmd = [sys.executable, str(entrypoint)]
            
            start_time = time.time()
            
            process = subprocess.Popen(
                cmd,
                cwd=plugin_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                env=self._get_sandbox_environment()
            )
            
            try:
                stdout, stderr = process.communicate(
                    timeout=self.config.execution_timeout_seconds
                )
                
                execution_time = int((time.time() - start_time) * 1000)
                
                if process.returncode != 0:
                    raise RuntimeError(f"Plugin execution failed: {stderr.decode()}")
                
                # Parse plugin output
                try:
                    result = json.loads(stdout.decode())
                    result['execution_time_ms'] = execution_time
                    return result
                except json.JSONDecodeError:
                    raise RuntimeError("Plugin output is not valid JSON")
                
            except subprocess.TimeoutExpired:
                process.kill()
                raise RuntimeError("Plugin execution timed out")
                
        except Exception as e:
            logger.error(f"Plugin execution failed: {e}")
            raise
    
    def _get_sandbox_environment(self) -> Dict[str, str]:
        """Get environment variables for sandbox execution"""
        return {
            'PATH': '/usr/bin:/bin',
            'PYTHONPATH': '',
            'HOME': '/tmp',
            'TMPDIR': '/tmp',
            'BYTEGUARDX_SANDBOX': '1',
            'BYTEGUARDX_WORKSPACE': str(Path(self.temp_workspace) / 'workspace'),
            'BYTEGUARDX_OUTPUT': str(Path(self.temp_workspace) / 'output')
        }
    
    def _cleanup(self):
        """Cleanup sandbox resources"""
        try:
            if self.temp_workspace and Path(self.temp_workspace).exists():
                shutil.rmtree(self.temp_workspace)
                logger.info("Sandbox workspace cleaned up")
        except Exception as e:
            logger.error(f"Sandbox cleanup failed: {e}")
    
    async def cleanup(self):
        """Async cleanup method"""
        self._cleanup()
