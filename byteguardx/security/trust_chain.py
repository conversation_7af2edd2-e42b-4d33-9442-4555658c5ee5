"""
ByteGuardX Trust Chain Management
Implements Ed25519 signing, certificate validation, and trust root management
"""

import logging
import json
import hashlib
import base64
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from pathlib import Path
from datetime import datetime, timedelta
import requests
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric.ed25519 import <PERSON>2551<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ed25519<PERSON><PERSON><PERSON><PERSON>ey
from cryptography.hazmat.primitives.asymmetric import utils
from cryptography.exceptions import InvalidSignature
import cryptography.x509 as x509

logger = logging.getLogger(__name__)

@dataclass
class TrustRoot:
    """Trust root certificate information"""
    fingerprint: str
    public_key: Ed25519Public<PERSON>ey
    name: str
    valid_from: datetime
    valid_until: datetime
    is_active: bool = True

@dataclass
class PluginCertificate:
    """Plugin certificate for trust chain"""
    fingerprint: str
    public_key: Ed25519PublicKey
    subject: str
    issuer: str
    valid_from: datetime
    valid_until: datetime
    signature: bytes
    is_revoked: bool = False

class TrustChainManager:
    """
    Manages plugin trust chains with Ed25519 signatures and certificate validation
    """
    
    def __init__(self, trust_store_path: str = "config/trust_store"):
        self.trust_store_path = Path(trust_store_path)
        self.trust_store_path.mkdir(parents=True, exist_ok=True)
        
        # Trust roots and certificates
        self.trust_roots: Dict[str, TrustRoot] = {}
        self.certificates: Dict[str, PluginCertificate] = {}
        self.revocation_list: Set[str] = set()
        
        # Signing key for ByteGuardX (if we're a CA)
        self.signing_key: Optional[Ed25519PrivateKey] = None
        
        self.is_initialized = False
        
        logger.info("Trust Chain Manager initialized")
    
    async def initialize(self):
        """Initialize trust chain manager"""
        try:
            # Load trust roots
            await self._load_trust_roots()
            
            # Load certificates
            await self._load_certificates()
            
            # Load revocation list
            await self._load_revocation_list()
            
            # Load signing key if available
            await self._load_signing_key()
            
            self.is_initialized = True
            logger.info(f"Trust Chain Manager initialized with {len(self.trust_roots)} trust roots")
            
        except Exception as e:
            logger.error(f"Trust chain initialization failed: {e}")
            raise
    
    async def _load_trust_roots(self):
        """Load trust root certificates"""
        trust_roots_file = self.trust_store_path / "trust_roots.json"
        
        if trust_roots_file.exists():
            try:
                with open(trust_roots_file, 'r') as f:
                    trust_data = json.load(f)
                
                for root_data in trust_data.get('roots', []):
                    trust_root = self._parse_trust_root(root_data)
                    self.trust_roots[trust_root.fingerprint] = trust_root
                    
                logger.info(f"Loaded {len(self.trust_roots)} trust roots")
                
            except Exception as e:
                logger.error(f"Failed to load trust roots: {e}")
        else:
            # Create default trust roots
            await self._create_default_trust_roots()
    
    def _parse_trust_root(self, root_data: Dict[str, Any]) -> TrustRoot:
        """Parse trust root from JSON data"""
        # Decode public key
        public_key_bytes = base64.b64decode(root_data['public_key'])
        public_key = Ed25519PublicKey.from_public_bytes(public_key_bytes)
        
        return TrustRoot(
            fingerprint=root_data['fingerprint'],
            public_key=public_key,
            name=root_data['name'],
            valid_from=datetime.fromisoformat(root_data['valid_from']),
            valid_until=datetime.fromisoformat(root_data['valid_until']),
            is_active=root_data.get('is_active', True)
        )
    
    async def _create_default_trust_roots(self):
        """Create default trust roots for ByteGuardX"""
        try:
            # Generate root key pair
            root_private_key = Ed25519PrivateKey.generate()
            root_public_key = root_private_key.public_key()
            
            # Create trust root
            fingerprint = self._calculate_key_fingerprint(root_public_key)
            
            trust_root = TrustRoot(
                fingerprint=fingerprint,
                public_key=root_public_key,
                name="ByteGuardX Root CA",
                valid_from=datetime.now(),
                valid_until=datetime.now() + timedelta(days=3650),  # 10 years
                is_active=True
            )
            
            self.trust_roots[fingerprint] = trust_root
            
            # Save trust root
            await self._save_trust_roots()
            
            # Save private key securely
            await self._save_root_private_key(root_private_key)
            
            logger.info("Created default trust root")
            
        except Exception as e:
            logger.error(f"Failed to create default trust roots: {e}")
            raise
    
    def _calculate_key_fingerprint(self, public_key: Ed25519PublicKey) -> str:
        """Calculate SHA-256 fingerprint of public key"""
        public_bytes = public_key.public_bytes(
            encoding=serialization.Encoding.Raw,
            format=serialization.PublicFormat.Raw
        )
        return hashlib.sha256(public_bytes).hexdigest()
    
    async def _save_trust_roots(self):
        """Save trust roots to file"""
        trust_roots_file = self.trust_store_path / "trust_roots.json"
        
        trust_data = {
            'version': '1.0',
            'updated': datetime.now().isoformat(),
            'roots': []
        }
        
        for trust_root in self.trust_roots.values():
            public_key_bytes = trust_root.public_key.public_bytes(
                encoding=serialization.Encoding.Raw,
                format=serialization.PublicFormat.Raw
            )
            
            root_data = {
                'fingerprint': trust_root.fingerprint,
                'public_key': base64.b64encode(public_key_bytes).decode(),
                'name': trust_root.name,
                'valid_from': trust_root.valid_from.isoformat(),
                'valid_until': trust_root.valid_until.isoformat(),
                'is_active': trust_root.is_active
            }
            
            trust_data['roots'].append(root_data)
        
        with open(trust_roots_file, 'w') as f:
            json.dump(trust_data, f, indent=2)
    
    async def _save_root_private_key(self, private_key: Ed25519PrivateKey):
        """Save root private key securely"""
        key_file = self.trust_store_path / "root_private_key.pem"
        
        # Serialize private key
        private_bytes = private_key.private_bytes(
            encoding=serialization.Encoding.PEM,
            format=serialization.PrivateFormat.PKCS8,
            encryption_algorithm=serialization.NoEncryption()  # In production, use password
        )
        
        # Save with restricted permissions
        with open(key_file, 'wb') as f:
            f.write(private_bytes)
        
        # Set restrictive permissions
        key_file.chmod(0o600)
        
        self.signing_key = private_key
    
    async def _load_signing_key(self):
        """Load signing key if available"""
        key_file = self.trust_store_path / "root_private_key.pem"
        
        if key_file.exists():
            try:
                with open(key_file, 'rb') as f:
                    private_bytes = f.read()
                
                self.signing_key = serialization.load_pem_private_key(
                    private_bytes,
                    password=None  # In production, use password
                )
                
                logger.info("Loaded signing key")
                
            except Exception as e:
                logger.warning(f"Failed to load signing key: {e}")
    
    async def _load_certificates(self):
        """Load plugin certificates"""
        certs_file = self.trust_store_path / "certificates.json"
        
        if certs_file.exists():
            try:
                with open(certs_file, 'r') as f:
                    cert_data = json.load(f)
                
                for cert_info in cert_data.get('certificates', []):
                    certificate = self._parse_certificate(cert_info)
                    self.certificates[certificate.fingerprint] = certificate
                    
                logger.info(f"Loaded {len(self.certificates)} certificates")
                
            except Exception as e:
                logger.error(f"Failed to load certificates: {e}")
    
    def _parse_certificate(self, cert_data: Dict[str, Any]) -> PluginCertificate:
        """Parse certificate from JSON data"""
        # Decode public key
        public_key_bytes = base64.b64decode(cert_data['public_key'])
        public_key = Ed25519PublicKey.from_public_bytes(public_key_bytes)
        
        # Decode signature
        signature = base64.b64decode(cert_data['signature'])
        
        return PluginCertificate(
            fingerprint=cert_data['fingerprint'],
            public_key=public_key,
            subject=cert_data['subject'],
            issuer=cert_data['issuer'],
            valid_from=datetime.fromisoformat(cert_data['valid_from']),
            valid_until=datetime.fromisoformat(cert_data['valid_until']),
            signature=signature,
            is_revoked=cert_data.get('is_revoked', False)
        )
    
    async def _load_revocation_list(self):
        """Load certificate revocation list"""
        crl_file = self.trust_store_path / "revocation_list.json"
        
        if crl_file.exists():
            try:
                with open(crl_file, 'r') as f:
                    crl_data = json.load(f)
                
                self.revocation_list = set(crl_data.get('revoked_certificates', []))
                logger.info(f"Loaded {len(self.revocation_list)} revoked certificates")
                
            except Exception as e:
                logger.error(f"Failed to load revocation list: {e}")
    
    async def verify_plugin_signature(self, plugin_path: Path, plugin_manifest) -> bool:
        """Verify plugin signature using Ed25519"""
        try:
            # Extract signing key ID from manifest
            signing_key_id = plugin_manifest.signing_key_id
            if not signing_key_id.startswith('ed25519:'):
                logger.error("Invalid signing key format")
                return False
            
            key_fingerprint = signing_key_id.split(':')[1]
            
            # Check if key is in trust chain
            public_key = self._get_public_key_by_fingerprint(key_fingerprint)
            if not public_key:
                logger.error(f"Unknown signing key: {key_fingerprint}")
                return False
            
            # Check revocation list
            if key_fingerprint in self.revocation_list:
                logger.error(f"Revoked signing key: {key_fingerprint}")
                return False
            
            # Calculate plugin content hash
            plugin_content = self._calculate_plugin_hash(plugin_path)
            
            # Verify signature
            signature_data = plugin_manifest.signature
            if not signature_data.startswith('ed25519:'):
                logger.error("Invalid signature format")
                return False
            
            signature_bytes = base64.b64decode(signature_data.split(':')[1])
            
            try:
                public_key.verify(signature_bytes, plugin_content.encode())
                logger.info(f"Plugin signature verified for {plugin_manifest.name}")
                return True
                
            except InvalidSignature:
                logger.error(f"Invalid plugin signature for {plugin_manifest.name}")
                return False
                
        except Exception as e:
            logger.error(f"Plugin signature verification failed: {e}")
            return False
    
    def _get_public_key_by_fingerprint(self, fingerprint: str) -> Optional[Ed25519PublicKey]:
        """Get public key by fingerprint from trust roots or certificates"""
        # Check trust roots first
        if fingerprint in self.trust_roots:
            return self.trust_roots[fingerprint].public_key
        
        # Check certificates
        if fingerprint in self.certificates:
            return self.certificates[fingerprint].public_key
        
        return None
    
    def _calculate_plugin_hash(self, plugin_path: Path) -> str:
        """Calculate hash of plugin content for signature verification"""
        hasher = hashlib.sha256()
        
        # Hash all files in plugin directory
        for file_path in sorted(plugin_path.rglob('*')):
            if file_path.is_file():
                with open(file_path, 'rb') as f:
                    hasher.update(f.read())
        
        return hasher.hexdigest()
    
    async def verify_trust_chain(self, plugin_manifest) -> bool:
        """Verify certificate chain to trust root"""
        try:
            # For now, we'll implement a simple trust verification
            # In production, this would walk the full certificate chain
            
            signing_key_id = plugin_manifest.signing_key_id
            if not signing_key_id.startswith('ed25519:'):
                return False
            
            key_fingerprint = signing_key_id.split(':')[1]
            
            # Check if directly trusted
            if key_fingerprint in self.trust_roots:
                trust_root = self.trust_roots[key_fingerprint]
                
                # Check validity period
                now = datetime.now()
                if now < trust_root.valid_from or now > trust_root.valid_until:
                    logger.error("Trust root expired or not yet valid")
                    return False
                
                # Check if active
                if not trust_root.is_active:
                    logger.error("Trust root is inactive")
                    return False
                
                return True
            
            # Check certificate chain (simplified)
            if key_fingerprint in self.certificates:
                certificate = self.certificates[key_fingerprint]
                
                # Check if revoked
                if certificate.is_revoked:
                    logger.error("Certificate is revoked")
                    return False
                
                # Check validity period
                now = datetime.now()
                if now < certificate.valid_from or now > certificate.valid_until:
                    logger.error("Certificate expired or not yet valid")
                    return False
                
                # Verify certificate signature against issuer
                # This is simplified - in production, you'd verify the full chain
                return True
            
            logger.error(f"No trust path found for key: {key_fingerprint}")
            return False
            
        except Exception as e:
            logger.error(f"Trust chain verification failed: {e}")
            return False
    
    async def sign_plugin(self, plugin_path: Path) -> str:
        """Sign plugin with our signing key"""
        if not self.signing_key:
            raise ValueError("No signing key available")
        
        try:
            # Calculate plugin hash
            plugin_hash = self._calculate_plugin_hash(plugin_path)
            
            # Sign hash
            signature = self.signing_key.sign(plugin_hash.encode())
            
            # Return base64-encoded signature
            return f"ed25519:{base64.b64encode(signature).decode()}"
            
        except Exception as e:
            logger.error(f"Plugin signing failed: {e}")
            raise
    
    async def add_trust_root(self, public_key: Ed25519PublicKey, name: str, validity_days: int = 3650):
        """Add new trust root"""
        fingerprint = self._calculate_key_fingerprint(public_key)
        
        trust_root = TrustRoot(
            fingerprint=fingerprint,
            public_key=public_key,
            name=name,
            valid_from=datetime.now(),
            valid_until=datetime.now() + timedelta(days=validity_days),
            is_active=True
        )
        
        self.trust_roots[fingerprint] = trust_root
        await self._save_trust_roots()
        
        logger.info(f"Added trust root: {name}")
    
    async def revoke_certificate(self, fingerprint: str):
        """Revoke certificate by fingerprint"""
        self.revocation_list.add(fingerprint)
        
        # Mark certificate as revoked if we have it
        if fingerprint in self.certificates:
            self.certificates[fingerprint].is_revoked = True
        
        # Save revocation list
        await self._save_revocation_list()
        
        logger.info(f"Revoked certificate: {fingerprint}")
    
    async def _save_revocation_list(self):
        """Save certificate revocation list"""
        crl_file = self.trust_store_path / "revocation_list.json"
        
        crl_data = {
            'version': '1.0',
            'updated': datetime.now().isoformat(),
            'revoked_certificates': list(self.revocation_list)
        }
        
        with open(crl_file, 'w') as f:
            json.dump(crl_data, f, indent=2)
    
    async def update_revocation_list(self):
        """Update certificate revocation list from trusted source"""
        try:
            # In production, this would fetch from a trusted CRL endpoint
            # For now, we'll just log the action
            logger.info("Revocation list update requested (placeholder)")
            
        except Exception as e:
            logger.error(f"Failed to update revocation list: {e}")
    
    def get_trust_status(self, plugin_manifest) -> Dict[str, Any]:
        """Get detailed trust status for plugin"""
        signing_key_id = plugin_manifest.signing_key_id
        key_fingerprint = signing_key_id.split(':')[1] if ':' in signing_key_id else signing_key_id
        
        return {
            'signing_key_id': signing_key_id,
            'key_fingerprint': key_fingerprint,
            'is_trusted': key_fingerprint in self.trust_roots or key_fingerprint in self.certificates,
            'is_revoked': key_fingerprint in self.revocation_list,
            'trust_level': plugin_manifest.security_level,
            'verification_time': datetime.now().isoformat()
        }
