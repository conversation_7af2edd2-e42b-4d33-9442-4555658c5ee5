"""
ByteGuardX Plugin IPC System
Implements secure inter-process communication between host and plugins
"""

import logging
import json
import asyncio
import uuid
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from pathlib import Path
import jsonschema
from datetime import datetime
import socket
import ssl
import struct

logger = logging.getLogger(__name__)

@dataclass
class IPCMessage:
    """Standard IPC message structure"""
    message_id: str
    message_type: str
    payload: Dict[str, Any]
    timestamp: str
    sender: str
    recipient: str
    
    def to_json(self) -> str:
        return json.dumps(asdict(self))
    
    @classmethod
    def from_json(cls, json_str: str) -> 'IPCMessage':
        data = json.loads(json_str)
        return cls(**data)

@dataclass
class ScanInput:
    """Plugin scan input schema"""
    scan_id: str
    content: str
    file_path: str
    content_hash: str
    metadata: Dict[str, Any]
    scan_options: Dict[str, Any]

@dataclass
class Finding:
    """Plugin finding output schema"""
    id: str
    type: str  # 'secret', 'vulnerability', 'code_smell', 'dependency'
    severity: str  # 'critical', 'high', 'medium', 'low', 'info'
    title: str
    description: str
    file_path: str
    line_start: int
    line_end: int
    column_start: int
    column_end: int
    confidence: float  # 0.0 to 1.0
    evidence: str
    remediation: str
    references: List[str]
    metadata: Dict[str, Any]

@dataclass
class ScanOutput:
    """Plugin scan output schema"""
    scan_id: str
    plugin_name: str
    plugin_version: str
    execution_time_ms: int
    findings: List[Finding]
    errors: List[Dict[str, Any]]
    metrics: Dict[str, Any]

class PluginIPCHandler:
    """
    Handles IPC communication with plugins using JSON schema validation
    """
    
    def __init__(self, plugin_manifest):
        self.plugin_manifest = plugin_manifest
        self.input_schema = self._load_schema(plugin_manifest.input_schema)
        self.output_schema = self._load_schema(plugin_manifest.output_schema)
        self.connection = None
        self.is_connected = False
        
        logger.info(f"IPC Handler initialized for plugin: {plugin_manifest.name}")
    
    def _load_schema(self, schema_path: str) -> Dict[str, Any]:
        """Load JSON schema for validation"""
        try:
            schema_file = Path(schema_path)
            if schema_file.exists():
                with open(schema_file, 'r') as f:
                    return json.load(f)
            else:
                return self._get_default_schema(schema_path)
        except Exception as e:
            logger.warning(f"Failed to load schema {schema_path}: {e}")
            return {}
    
    def _get_default_schema(self, schema_type: str) -> Dict[str, Any]:
        """Get default schema based on type"""
        if 'input' in schema_type:
            return {
                "$schema": "https://json-schema.org/draft/2020-12/schema",
                "type": "object",
                "properties": {
                    "scan_id": {"type": "string", "format": "uuid"},
                    "content": {"type": "string"},
                    "file_path": {"type": "string"},
                    "content_hash": {"type": "string", "pattern": "^sha256:[a-f0-9]{64}$"},
                    "metadata": {"type": "object"},
                    "scan_options": {"type": "object"}
                },
                "required": ["scan_id", "content", "file_path", "content_hash"]
            }
        else:  # output schema
            return {
                "$schema": "https://json-schema.org/draft/2020-12/schema",
                "type": "object",
                "properties": {
                    "scan_id": {"type": "string", "format": "uuid"},
                    "plugin_name": {"type": "string"},
                    "plugin_version": {"type": "string"},
                    "execution_time_ms": {"type": "integer"},
                    "findings": {
                        "type": "array",
                        "items": {
                            "type": "object",
                            "properties": {
                                "id": {"type": "string", "format": "uuid"},
                                "type": {"type": "string", "enum": ["secret", "vulnerability", "code_smell", "dependency"]},
                                "severity": {"type": "string", "enum": ["critical", "high", "medium", "low", "info"]},
                                "title": {"type": "string"},
                                "description": {"type": "string"},
                                "file_path": {"type": "string"},
                                "line_start": {"type": "integer", "minimum": 1},
                                "line_end": {"type": "integer", "minimum": 1},
                                "column_start": {"type": "integer", "minimum": 1},
                                "column_end": {"type": "integer", "minimum": 1},
                                "confidence": {"type": "number", "minimum": 0, "maximum": 1},
                                "evidence": {"type": "string"},
                                "remediation": {"type": "string"},
                                "references": {"type": "array", "items": {"type": "string", "format": "uri"}},
                                "metadata": {"type": "object"}
                            },
                            "required": ["id", "type", "severity", "title", "description", "file_path", "line_start", "confidence"]
                        }
                    },
                    "errors": {"type": "array"},
                    "metrics": {"type": "object"}
                },
                "required": ["scan_id", "plugin_name", "plugin_version", "findings"]
            }
    
    async def connect(self, socket_path: str = None, port: int = None):
        """Establish IPC connection with plugin"""
        try:
            if socket_path:
                # Unix domain socket
                self.connection = socket.socket(socket.AF_UNIX, socket.SOCK_STREAM)
                await asyncio.get_event_loop().run_in_executor(
                    None, self.connection.connect, socket_path
                )
            elif port:
                # TCP socket with TLS
                context = ssl.create_default_context(ssl.Purpose.SERVER_AUTH)
                context.check_hostname = False
                context.verify_mode = ssl.CERT_NONE
                
                self.connection = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                self.connection = context.wrap_socket(self.connection)
                await asyncio.get_event_loop().run_in_executor(
                    None, self.connection.connect, ('localhost', port)
                )
            else:
                raise ValueError("Either socket_path or port must be provided")
            
            self.is_connected = True
            logger.info(f"IPC connection established for plugin: {self.plugin_manifest.name}")
            
        except Exception as e:
            logger.error(f"Failed to establish IPC connection: {e}")
            raise
    
    async def execute_scan(self, scan_input: ScanInput) -> ScanOutput:
        """Execute scan via IPC with schema validation"""
        try:
            # Validate input
            input_dict = asdict(scan_input)
            if not self.validate_input(input_dict):
                raise ValueError("Input validation failed")
            
            # Create IPC message
            message = IPCMessage(
                message_id=str(uuid.uuid4()),
                message_type="scan_request",
                payload=input_dict,
                timestamp=datetime.now().isoformat(),
                sender="byteguardx_host",
                recipient=self.plugin_manifest.name
            )
            
            # Send scan request
            await self.send_message(message)
            
            # Receive scan response
            response = await self.receive_message()
            
            if response.message_type != "scan_response":
                raise ValueError(f"Unexpected message type: {response.message_type}")
            
            # Validate output
            if not self.validate_output(response.payload):
                raise ValueError("Output validation failed")
            
            # Convert to ScanOutput
            findings = [Finding(**f) for f in response.payload.get('findings', [])]
            
            scan_output = ScanOutput(
                scan_id=response.payload['scan_id'],
                plugin_name=response.payload['plugin_name'],
                plugin_version=response.payload['plugin_version'],
                execution_time_ms=response.payload['execution_time_ms'],
                findings=findings,
                errors=response.payload.get('errors', []),
                metrics=response.payload.get('metrics', {})
            )
            
            return scan_output
            
        except Exception as e:
            logger.error(f"Scan execution failed: {e}")
            raise
    
    def validate_input(self, input_data: Dict[str, Any]) -> bool:
        """Validate input data against schema"""
        try:
            jsonschema.validate(input_data, self.input_schema)
            return True
        except jsonschema.ValidationError as e:
            logger.error(f"Input validation failed: {e}")
            return False
    
    def validate_output(self, output_data: Dict[str, Any]) -> bool:
        """Validate output data against schema"""
        try:
            jsonschema.validate(output_data, self.output_schema)
            return True
        except jsonschema.ValidationError as e:
            logger.error(f"Output validation failed: {e}")
            return False
    
    async def send_message(self, message: IPCMessage) -> None:
        """Send IPC message to plugin"""
        if not self.is_connected:
            raise RuntimeError("IPC connection not established")
        
        try:
            message_json = message.to_json()
            message_bytes = message_json.encode('utf-8')
            
            # Send message length first (4 bytes)
            length = struct.pack('!I', len(message_bytes))
            await asyncio.get_event_loop().run_in_executor(
                None, self.connection.send, length
            )
            
            # Send message data
            await asyncio.get_event_loop().run_in_executor(
                None, self.connection.send, message_bytes
            )
            
            logger.debug(f"Sent IPC message: {message.message_type}")
            
        except Exception as e:
            logger.error(f"Failed to send IPC message: {e}")
            raise
    
    async def receive_message(self) -> IPCMessage:
        """Receive IPC message from plugin"""
        if not self.is_connected:
            raise RuntimeError("IPC connection not established")
        
        try:
            # Receive message length (4 bytes)
            length_bytes = await asyncio.get_event_loop().run_in_executor(
                None, self.connection.recv, 4
            )
            
            if len(length_bytes) != 4:
                raise RuntimeError("Failed to receive message length")
            
            message_length = struct.unpack('!I', length_bytes)[0]
            
            # Receive message data
            message_bytes = b''
            while len(message_bytes) < message_length:
                chunk = await asyncio.get_event_loop().run_in_executor(
                    None, self.connection.recv, message_length - len(message_bytes)
                )
                if not chunk:
                    raise RuntimeError("Connection closed unexpectedly")
                message_bytes += chunk
            
            # Parse message
            message_json = message_bytes.decode('utf-8')
            message = IPCMessage.from_json(message_json)
            
            logger.debug(f"Received IPC message: {message.message_type}")
            return message
            
        except Exception as e:
            logger.error(f"Failed to receive IPC message: {e}")
            raise

class SecureIPCBridge:
    """Secure IPC bridge for managing multiple plugin connections"""
    
    def __init__(self):
        self.active_connections: Dict[str, PluginIPCHandler] = {}
        self.is_initialized = False
        
    async def initialize(self):
        """Initialize IPC bridge"""
        self.is_initialized = True
        logger.info("Secure IPC Bridge initialized")
    
    async def create_connection(self, plugin_manifest) -> PluginIPCHandler:
        """Create new IPC connection for plugin"""
        handler = PluginIPCHandler(plugin_manifest)
        self.active_connections[plugin_manifest.name] = handler
        return handler
