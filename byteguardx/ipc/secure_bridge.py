"""
ByteGuardX Secure IPC Bridge
Secure inter-process communication for ByteGuardX components
"""

import logging
import asyncio
import json
from typing import Dict, Any, Optional, Callable
from dataclasses import dataclass
from datetime import datetime
import uuid

logger = logging.getLogger(__name__)

@dataclass
class IPCMessage:
    """IPC message structure"""
    id: str
    sender: str
    recipient: str
    message_type: str
    payload: Dict[str, Any]
    timestamp: datetime
    priority: int = 0

class SecureIPCBridge:
    """
    Secure IPC bridge for communication between ByteGuardX components
    """
    
    def __init__(self):
        self.message_handlers: Dict[str, Callable] = {}
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.is_running = False
        self.component_id = str(uuid.uuid4())
        
        logger.info(f"SecureIPCBridge initialized with ID: {self.component_id}")
    
    async def initialize(self) -> bool:
        """Initialize the IPC bridge"""
        try:
            self.is_running = True
            
            # Start message processing loop
            asyncio.create_task(self._process_messages())
            
            logger.info("SecureIPCBridge initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize SecureIPCBridge: {e}")
            return False
    
    def register_handler(self, message_type: str, handler: Callable):
        """Register a message handler"""
        self.message_handlers[message_type] = handler
        logger.debug(f"Registered handler for message type: {message_type}")
    
    async def send_message(self, recipient: str, message_type: str, payload: Dict[str, Any]) -> str:
        """Send a message to another component"""
        try:
            message = IPCMessage(
                id=str(uuid.uuid4()),
                sender=self.component_id,
                recipient=recipient,
                message_type=message_type,
                payload=payload,
                timestamp=datetime.now()
            )
            
            await self.message_queue.put(message)
            logger.debug(f"Message sent: {message.id} -> {recipient}")
            
            return message.id
            
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            raise
    
    async def _process_messages(self):
        """Process messages from the queue"""
        while self.is_running:
            try:
                # Get message from queue with timeout
                message = await asyncio.wait_for(
                    self.message_queue.get(), 
                    timeout=1.0
                )
                
                # Process the message
                await self._handle_message(message)
                
            except asyncio.TimeoutError:
                # No message received, continue
                continue
            except Exception as e:
                logger.error(f"Error processing message: {e}")
    
    async def _handle_message(self, message: IPCMessage):
        """Handle a received message"""
        try:
            handler = self.message_handlers.get(message.message_type)
            
            if handler:
                await handler(message)
                logger.debug(f"Message handled: {message.id}")
            else:
                logger.warning(f"No handler for message type: {message.message_type}")
                
        except Exception as e:
            logger.error(f"Error handling message {message.id}: {e}")
    
    async def broadcast_message(self, message_type: str, payload: Dict[str, Any]):
        """Broadcast a message to all components"""
        try:
            message = IPCMessage(
                id=str(uuid.uuid4()),
                sender=self.component_id,
                recipient="*",  # Broadcast
                message_type=message_type,
                payload=payload,
                timestamp=datetime.now()
            )
            
            await self.message_queue.put(message)
            logger.debug(f"Broadcast message sent: {message.id}")
            
        except Exception as e:
            logger.error(f"Failed to broadcast message: {e}")
            raise
    
    async def shutdown(self):
        """Shutdown the IPC bridge"""
        try:
            self.is_running = False
            logger.info("SecureIPCBridge shutdown completed")
            
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get IPC bridge status"""
        return {
            'component_id': self.component_id,
            'is_running': self.is_running,
            'registered_handlers': list(self.message_handlers.keys()),
            'queue_size': self.message_queue.qsize()
        }

# Global IPC bridge instance
ipc_bridge = SecureIPCBridge()
