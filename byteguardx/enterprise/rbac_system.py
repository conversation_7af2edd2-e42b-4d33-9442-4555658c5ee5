"""
ByteGuardX Enterprise RBAC & Multi-tenant Architecture
Role-Based Access Control with multi-tenant isolation and enterprise features
"""

import logging
import asyncio
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import json
import uuid
from pathlib import Path
import hashlib
from cryptography.fernet import Fernet

logger = logging.getLogger(__name__)

class Permission(Enum):
    """System permissions"""
    # Scan permissions
    SCAN_CREATE = "scan:create"
    SCAN_READ = "scan:read"
    SCAN_UPDATE = "scan:update"
    SCAN_DELETE = "scan:delete"
    SCAN_EXECUTE = "scan:execute"
    
    # Plugin permissions
    PLUGIN_INSTALL = "plugin:install"
    PLUGIN_UNINSTALL = "plugin:uninstall"
    PLUGIN_CONFIGURE = "plugin:configure"
    PLUGIN_DEVELOP = "plugin:develop"
    
    # User management
    USER_CREATE = "user:create"
    USER_READ = "user:read"
    USER_UPDATE = "user:update"
    USER_DELETE = "user:delete"
    USER_IMPERSONATE = "user:impersonate"
    
    # Organization management
    ORG_CREATE = "org:create"
    ORG_READ = "org:read"
    ORG_UPDATE = "org:update"
    ORG_DELETE = "org:delete"
    ORG_MANAGE_BILLING = "org:billing"
    
    # System administration
    SYSTEM_ADMIN = "system:admin"
    SYSTEM_CONFIG = "system:config"
    SYSTEM_MONITOR = "system:monitor"
    SYSTEM_AUDIT = "system:audit"
    
    # Reporting
    REPORT_CREATE = "report:create"
    REPORT_READ = "report:read"
    REPORT_EXPORT = "report:export"
    REPORT_SCHEDULE = "report:schedule"
    
    # API access
    API_READ = "api:read"
    API_WRITE = "api:write"
    API_ADMIN = "api:admin"

@dataclass
class Role:
    """Role definition with permissions"""
    id: str
    name: str
    description: str
    permissions: Set[Permission]
    is_system_role: bool = False
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    tenant_id: Optional[str] = None

@dataclass
class User:
    """User with roles and tenant association"""
    id: str
    username: str
    email: str
    full_name: str
    is_active: bool
    is_verified: bool
    roles: List[str]  # Role IDs
    tenant_id: str
    last_login: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    password_hash: Optional[str] = None
    mfa_enabled: bool = False
    mfa_secret: Optional[str] = None
    session_timeout: int = 3600  # seconds
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None

@dataclass
class Tenant:
    """Multi-tenant organization"""
    id: str
    name: str
    domain: str
    subscription_plan: str  # 'free', 'pro', 'enterprise'
    is_active: bool
    settings: Dict[str, Any]
    resource_limits: Dict[str, int]
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    billing_email: Optional[str] = None
    admin_user_id: Optional[str] = None

@dataclass
class Session:
    """User session management"""
    id: str
    user_id: str
    tenant_id: str
    created_at: datetime
    expires_at: datetime
    ip_address: str
    user_agent: str
    is_active: bool = True
    permissions_cache: Optional[Set[Permission]] = None

class RBACSystem:
    """
    Enterprise Role-Based Access Control with Multi-tenant Architecture
    """
    
    def __init__(self, encryption_key: Optional[bytes] = None):
        self.encryption_key = encryption_key or Fernet.generate_key()
        self.cipher = Fernet(self.encryption_key)
        
        # In-memory storage (replace with database in production)
        self.users: Dict[str, User] = {}
        self.roles: Dict[str, Role] = {}
        self.tenants: Dict[str, Tenant] = {}
        self.sessions: Dict[str, Session] = {}
        
        # Permission cache
        self.permission_cache: Dict[str, Set[Permission]] = {}
        self.cache_ttl = 300  # 5 minutes
        
        # Initialize system roles
        self._initialize_system_roles()
        
        logger.info("RBAC System initialized")
    
    def _initialize_system_roles(self):
        """Initialize default system roles"""
        # Super Admin - all permissions
        super_admin = Role(
            id="super_admin",
            name="Super Administrator",
            description="Full system access across all tenants",
            permissions=set(Permission),
            is_system_role=True
        )
        self.roles[super_admin.id] = super_admin
        
        # Tenant Admin - tenant-level admin
        tenant_admin = Role(
            id="tenant_admin",
            name="Tenant Administrator",
            description="Full access within tenant",
            permissions={
                Permission.SCAN_CREATE, Permission.SCAN_READ, Permission.SCAN_UPDATE, 
                Permission.SCAN_DELETE, Permission.SCAN_EXECUTE,
                Permission.PLUGIN_INSTALL, Permission.PLUGIN_UNINSTALL, Permission.PLUGIN_CONFIGURE,
                Permission.USER_CREATE, Permission.USER_READ, Permission.USER_UPDATE, Permission.USER_DELETE,
                Permission.ORG_READ, Permission.ORG_UPDATE,
                Permission.REPORT_CREATE, Permission.REPORT_READ, Permission.REPORT_EXPORT, Permission.REPORT_SCHEDULE,
                Permission.API_READ, Permission.API_WRITE
            },
            is_system_role=True
        )
        self.roles[tenant_admin.id] = tenant_admin
        
        # Security Analyst - scanning and reporting
        security_analyst = Role(
            id="security_analyst",
            name="Security Analyst",
            description="Security scanning and analysis",
            permissions={
                Permission.SCAN_CREATE, Permission.SCAN_READ, Permission.SCAN_EXECUTE,
                Permission.PLUGIN_CONFIGURE,
                Permission.REPORT_CREATE, Permission.REPORT_READ, Permission.REPORT_EXPORT,
                Permission.API_READ
            },
            is_system_role=True
        )
        self.roles[security_analyst.id] = security_analyst
        
        # Developer - basic scanning
        developer = Role(
            id="developer",
            name="Developer",
            description="Basic scanning capabilities",
            permissions={
                Permission.SCAN_CREATE, Permission.SCAN_READ, Permission.SCAN_EXECUTE,
                Permission.REPORT_READ,
                Permission.API_READ
            },
            is_system_role=True
        )
        self.roles[developer.id] = developer
        
        # Viewer - read-only access
        viewer = Role(
            id="viewer",
            name="Viewer",
            description="Read-only access to scans and reports",
            permissions={
                Permission.SCAN_READ,
                Permission.REPORT_READ,
                Permission.API_READ
            },
            is_system_role=True
        )
        self.roles[viewer.id] = viewer
    
    async def create_tenant(self, name: str, domain: str, admin_email: str, 
                          subscription_plan: str = "free") -> Tenant:
        """Create new tenant organization"""
        try:
            tenant_id = str(uuid.uuid4())
            
            # Default resource limits based on plan
            resource_limits = {
                "free": {"max_users": 5, "max_scans_per_month": 100, "max_storage_gb": 1},
                "pro": {"max_users": 50, "max_scans_per_month": 1000, "max_storage_gb": 10},
                "enterprise": {"max_users": -1, "max_scans_per_month": -1, "max_storage_gb": -1}
            }
            
            tenant = Tenant(
                id=tenant_id,
                name=name,
                domain=domain,
                subscription_plan=subscription_plan,
                is_active=True,
                settings={
                    "require_mfa": subscription_plan == "enterprise",
                    "session_timeout": 3600,
                    "password_policy": {
                        "min_length": 8,
                        "require_uppercase": True,
                        "require_lowercase": True,
                        "require_numbers": True,
                        "require_symbols": subscription_plan != "free"
                    }
                },
                resource_limits=resource_limits.get(subscription_plan, resource_limits["free"]),
                billing_email=admin_email
            )
            
            self.tenants[tenant_id] = tenant
            
            # Create admin user for tenant
            admin_user = await self.create_user(
                username=f"admin@{domain}",
                email=admin_email,
                full_name="Tenant Administrator",
                tenant_id=tenant_id,
                roles=["tenant_admin"]
            )
            
            tenant.admin_user_id = admin_user.id
            
            logger.info(f"Created tenant: {name} ({tenant_id})")
            return tenant
            
        except Exception as e:
            logger.error(f"Failed to create tenant: {e}")
            raise
    
    async def create_user(self, username: str, email: str, full_name: str, 
                         tenant_id: str, roles: List[str], password: Optional[str] = None) -> User:
        """Create new user"""
        try:
            # Validate tenant exists
            if tenant_id not in self.tenants:
                raise ValueError(f"Tenant {tenant_id} not found")
            
            tenant = self.tenants[tenant_id]
            
            # Check resource limits
            tenant_users = [u for u in self.users.values() if u.tenant_id == tenant_id]
            if (tenant.resource_limits.get("max_users", 0) > 0 and 
                len(tenant_users) >= tenant.resource_limits["max_users"]):
                raise ValueError("Tenant user limit exceeded")
            
            # Validate roles exist and belong to tenant
            for role_id in roles:
                if role_id not in self.roles:
                    raise ValueError(f"Role {role_id} not found")
                role = self.roles[role_id]
                if role.tenant_id and role.tenant_id != tenant_id:
                    raise ValueError(f"Role {role_id} not available for tenant {tenant_id}")
            
            user_id = str(uuid.uuid4())
            
            user = User(
                id=user_id,
                username=username,
                email=email,
                full_name=full_name,
                is_active=True,
                is_verified=False,
                roles=roles,
                tenant_id=tenant_id,
                password_hash=self._hash_password(password) if password else None,
                mfa_enabled=tenant.settings.get("require_mfa", False),
                session_timeout=tenant.settings.get("session_timeout", 3600)
            )
            
            self.users[user_id] = user
            
            logger.info(f"Created user: {username} ({user_id}) for tenant {tenant_id}")
            return user
            
        except Exception as e:
            logger.error(f"Failed to create user: {e}")
            raise
    
    async def create_role(self, name: str, description: str, permissions: List[Permission], 
                         tenant_id: Optional[str] = None) -> Role:
        """Create custom role"""
        try:
            role_id = str(uuid.uuid4())
            
            role = Role(
                id=role_id,
                name=name,
                description=description,
                permissions=set(permissions),
                is_system_role=False,
                tenant_id=tenant_id
            )
            
            self.roles[role_id] = role
            
            logger.info(f"Created role: {name} ({role_id})")
            return role
            
        except Exception as e:
            logger.error(f"Failed to create role: {e}")
            raise
    
    async def authenticate_user(self, username: str, password: str, 
                              ip_address: str, user_agent: str) -> Optional[Session]:
        """Authenticate user and create session"""
        try:
            # Find user
            user = None
            for u in self.users.values():
                if u.username == username or u.email == username:
                    user = u
                    break
            
            if not user:
                logger.warning(f"Authentication failed: user not found - {username}")
                return None
            
            # Check if user is locked
            if user.locked_until and user.locked_until > datetime.now():
                logger.warning(f"Authentication failed: user locked - {username}")
                return None
            
            # Verify password
            if not self._verify_password(password, user.password_hash):
                user.failed_login_attempts += 1
                
                # Lock user after 5 failed attempts
                if user.failed_login_attempts >= 5:
                    user.locked_until = datetime.now() + timedelta(minutes=30)
                    logger.warning(f"User locked due to failed attempts: {username}")
                
                return None
            
            # Reset failed attempts on successful login
            user.failed_login_attempts = 0
            user.locked_until = None
            user.last_login = datetime.now()
            
            # Create session
            session = Session(
                id=str(uuid.uuid4()),
                user_id=user.id,
                tenant_id=user.tenant_id,
                created_at=datetime.now(),
                expires_at=datetime.now() + timedelta(seconds=user.session_timeout),
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            self.sessions[session.id] = session
            
            logger.info(f"User authenticated: {username} ({user.id})")
            return session
            
        except Exception as e:
            logger.error(f"Authentication failed: {e}")
            return None
    
    async def check_permission(self, session_id: str, permission: Permission, 
                             resource_tenant_id: Optional[str] = None) -> bool:
        """Check if user has specific permission"""
        try:
            session = self.sessions.get(session_id)
            if not session or not session.is_active:
                return False
            
            # Check session expiry
            if session.expires_at < datetime.now():
                session.is_active = False
                return False
            
            user = self.users.get(session.user_id)
            if not user or not user.is_active:
                return False
            
            # Multi-tenant isolation check
            if resource_tenant_id and resource_tenant_id != user.tenant_id:
                # Only super admins can access cross-tenant resources
                if not await self._is_super_admin(user):
                    return False
            
            # Get user permissions (with caching)
            user_permissions = await self._get_user_permissions(user.id)
            
            return permission in user_permissions
            
        except Exception as e:
            logger.error(f"Permission check failed: {e}")
            return False
    
    async def _get_user_permissions(self, user_id: str) -> Set[Permission]:
        """Get all permissions for user (cached)"""
        try:
            # Check cache
            cache_key = f"permissions_{user_id}"
            if cache_key in self.permission_cache:
                return self.permission_cache[cache_key]
            
            user = self.users.get(user_id)
            if not user:
                return set()
            
            permissions = set()
            
            # Collect permissions from all user roles
            for role_id in user.roles:
                role = self.roles.get(role_id)
                if role:
                    permissions.update(role.permissions)
            
            # Cache permissions
            self.permission_cache[cache_key] = permissions
            
            # Schedule cache cleanup
            asyncio.create_task(self._cleanup_permission_cache(cache_key))
            
            return permissions
            
        except Exception as e:
            logger.error(f"Failed to get user permissions: {e}")
            return set()
    
    async def _is_super_admin(self, user: User) -> bool:
        """Check if user is super admin"""
        return "super_admin" in user.roles
    
    async def assign_role(self, user_id: str, role_id: str, assigned_by: str) -> bool:
        """Assign role to user"""
        try:
            user = self.users.get(user_id)
            role = self.roles.get(role_id)
            
            if not user or not role:
                return False
            
            # Check tenant isolation
            if role.tenant_id and role.tenant_id != user.tenant_id:
                return False
            
            if role_id not in user.roles:
                user.roles.append(role_id)
                user.updated_at = datetime.now()
                
                # Clear permission cache
                cache_key = f"permissions_{user_id}"
                if cache_key in self.permission_cache:
                    del self.permission_cache[cache_key]
                
                logger.info(f"Role {role_id} assigned to user {user_id} by {assigned_by}")
                return True
            
            return True  # Already has role
            
        except Exception as e:
            logger.error(f"Failed to assign role: {e}")
            return False
    
    async def revoke_role(self, user_id: str, role_id: str, revoked_by: str) -> bool:
        """Revoke role from user"""
        try:
            user = self.users.get(user_id)
            
            if not user:
                return False
            
            if role_id in user.roles:
                user.roles.remove(role_id)
                user.updated_at = datetime.now()
                
                # Clear permission cache
                cache_key = f"permissions_{user_id}"
                if cache_key in self.permission_cache:
                    del self.permission_cache[cache_key]
                
                logger.info(f"Role {role_id} revoked from user {user_id} by {revoked_by}")
                return True
            
            return True  # Didn't have role
            
        except Exception as e:
            logger.error(f"Failed to revoke role: {e}")
            return False
    
    async def get_tenant_users(self, tenant_id: str) -> List[User]:
        """Get all users for a tenant"""
        return [user for user in self.users.values() if user.tenant_id == tenant_id]
    
    async def get_user_sessions(self, user_id: str) -> List[Session]:
        """Get all active sessions for user"""
        return [session for session in self.sessions.values() 
                if session.user_id == user_id and session.is_active]
    
    async def revoke_session(self, session_id: str) -> bool:
        """Revoke user session"""
        try:
            session = self.sessions.get(session_id)
            if session:
                session.is_active = False
                logger.info(f"Session revoked: {session_id}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to revoke session: {e}")
            return False
    
    async def cleanup_expired_sessions(self):
        """Clean up expired sessions"""
        try:
            now = datetime.now()
            expired_sessions = [
                session_id for session_id, session in self.sessions.items()
                if session.expires_at < now
            ]
            
            for session_id in expired_sessions:
                self.sessions[session_id].is_active = False
            
            logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
            
        except Exception as e:
            logger.error(f"Session cleanup failed: {e}")
    
    async def _cleanup_permission_cache(self, cache_key: str):
        """Clean up permission cache entry after TTL"""
        await asyncio.sleep(self.cache_ttl)
        if cache_key in self.permission_cache:
            del self.permission_cache[cache_key]
    
    def _hash_password(self, password: str) -> str:
        """Hash password securely"""
        import bcrypt
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def _verify_password(self, password: str, password_hash: Optional[str]) -> bool:
        """Verify password against hash"""
        if not password_hash:
            return False
        
        import bcrypt
        return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
    
    async def export_audit_log(self, tenant_id: str, start_date: datetime, 
                              end_date: datetime) -> Dict[str, Any]:
        """Export audit log for compliance"""
        try:
            # This would query actual audit logs in production
            audit_data = {
                "tenant_id": tenant_id,
                "export_date": datetime.now().isoformat(),
                "period": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat()
                },
                "events": [
                    # User authentication events
                    # Permission changes
                    # Role assignments
                    # System access events
                ],
                "summary": {
                    "total_events": 0,
                    "user_logins": 0,
                    "permission_changes": 0,
                    "failed_attempts": 0
                }
            }
            
            return audit_data
            
        except Exception as e:
            logger.error(f"Audit log export failed: {e}")
            raise

# Global RBAC system instance
rbac_system = RBACSystem()
