<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ByteGuardX Security Report</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8f9fa;
        }
        
        .header {
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header .subtitle {
            margin: 10px 0 0 0;
            font-size: 1.2em;
            opacity: 0.8;
        }
        
        .summary {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .summary h2 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #00d4aa;
            padding-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .stat-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #00d4aa;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .severity-critical { border-left-color: #e74c3c; }
        .severity-high { border-left-color: #f39c12; }
        .severity-medium { border-left-color: #f1c40f; }
        .severity-low { border-left-color: #3498db; }
        
        .findings-section {
            background: white;
            padding: 25px;
            border-radius: 10px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .findings-section h2 {
            margin-top: 0;
            color: #2c3e50;
            border-bottom: 2px solid #00d4aa;
            padding-bottom: 10px;
        }
        
        .finding {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .finding-header {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .finding-title {
            font-weight: bold;
            color: #2c3e50;
            margin: 0;
        }
        
        .severity-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.8em;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .severity-badge.critical {
            background: #e74c3c;
            color: white;
        }
        
        .severity-badge.high {
            background: #f39c12;
            color: white;
        }
        
        .severity-badge.medium {
            background: #f1c40f;
            color: #333;
        }
        
        .severity-badge.low {
            background: #3498db;
            color: white;
        }
        
        .finding-body {
            padding: 20px;
        }
        
        .finding-description {
            margin-bottom: 15px;
            color: #555;
        }
        
        .finding-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .detail-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        }
        
        .detail-label {
            font-weight: bold;
            color: #666;
            font-size: 0.9em;
        }
        
        .detail-value {
            color: #333;
            margin-top: 2px;
        }
        
        .code-snippet {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin-top: 15px;
        }
        
        .footer {
            text-align: center;
            margin-top: 40px;
            padding: 20px;
            color: #666;
            border-top: 1px solid #e0e0e0;
        }
        
        .no-findings {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .no-findings .icon {
            font-size: 3em;
            color: #00d4aa;
            margin-bottom: 20px;
        }
        
        @media print {
            body {
                background: white;
                padding: 0;
            }
            
            .header, .summary, .findings-section {
                box-shadow: none;
                border: 1px solid #ddd;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🛡️ ByteGuardX</h1>
        <div class="subtitle">Security Vulnerability Report</div>
    </div>

    <div class="summary">
        <h2>📊 Scan Summary</h2>
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number">{{ scan_data.total_files }}</div>
                <div class="stat-label">Files Scanned</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">{{ scan_data.total_findings }}</div>
                <div class="stat-label">Total Issues</div>
            </div>
            <div class="stat-card severity-critical">
                <div class="stat-number">{{ scan_data.critical_findings }}</div>
                <div class="stat-label">Critical</div>
            </div>
            <div class="stat-card severity-high">
                <div class="stat-number">{{ scan_data.high_findings }}</div>
                <div class="stat-label">High</div>
            </div>
            <div class="stat-card severity-medium">
                <div class="stat-number">{{ scan_data.medium_findings }}</div>
                <div class="stat-label">Medium</div>
            </div>
            <div class="stat-card severity-low">
                <div class="stat-number">{{ scan_data.low_findings }}</div>
                <div class="stat-label">Low</div>
            </div>
        </div>
        
        <div class="detail-item">
            <div class="detail-label">Scan Directory</div>
            <div class="detail-value">{{ scan_data.directory_path }}</div>
        </div>
        
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-top: 15px;">
            <div class="detail-item">
                <div class="detail-label">Scan Started</div>
                <div class="detail-value">{{ scan_data.started_at }}</div>
            </div>
            <div class="detail-item">
                <div class="detail-label">Scan Duration</div>
                <div class="detail-value">{{ "%.1f"|format(scan_data.scan_duration) }} seconds</div>
            </div>
        </div>
    </div>

    {% if findings %}
    <div class="findings-section">
        <h2>🔍 Security Findings</h2>
        
        {% for finding in findings %}
        <div class="finding">
            <div class="finding-header">
                <h3 class="finding-title">{{ finding.title }}</h3>
                <span class="severity-badge {{ finding.severity }}">{{ finding.severity }}</span>
            </div>
            <div class="finding-body">
                <div class="finding-description">
                    {{ finding.description }}
                </div>
                
                <div class="finding-details">
                    <div class="detail-item">
                        <div class="detail-label">File</div>
                        <div class="detail-value">{{ finding.file_path }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Line</div>
                        <div class="detail-value">{{ finding.line_number }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Type</div>
                        <div class="detail-value">{{ finding.vulnerability_type }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Scanner</div>
                        <div class="detail-value">{{ finding.scanner_type }}</div>
                    </div>
                    <div class="detail-item">
                        <div class="detail-label">Confidence</div>
                        <div class="detail-value">{{ "%.1f"|format(finding.confidence_score * 100) }}%</div>
                    </div>
                </div>
                
                {% if finding.code_snippet %}
                <div class="code-snippet">{{ finding.code_snippet }}</div>
                {% endif %}
            </div>
        </div>
        {% endfor %}
    </div>
    {% else %}
    <div class="findings-section">
        <div class="no-findings">
            <div class="icon">✅</div>
            <h3>No Security Issues Found</h3>
            <p>Great job! Your code appears to be free of common security vulnerabilities.</p>
        </div>
    </div>
    {% endif %}

    <div class="footer">
        <p>Generated by ByteGuardX on {{ report_date }}</p>
        <p>For more information, visit <strong>byteguardx.com</strong></p>
    </div>
</body>
</html>
