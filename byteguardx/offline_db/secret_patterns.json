{"api_keys": {"aws_access_key": {"pattern": "AKIA[0-9A-Z]{16}", "description": "AWS Access Key ID", "severity": "critical"}, "aws_secret_key": {"pattern": "[A-Za-z0-9/+=]{40}", "description": "AWS Secret Access Key", "severity": "critical", "context_required": ["aws", "secret", "key"]}, "github_token": {"pattern": "ghp_[A-Za-z0-9]{36}", "description": "GitHub Personal Access Token", "severity": "high"}, "github_oauth": {"pattern": "gho_[A-Za-z0-9]{36}", "description": "GitHub OAuth Access Token", "severity": "high"}, "slack_token": {"pattern": "xox[baprs]-[A-Za-z0-9-]+", "description": "<PERSON><PERSON><PERSON>", "severity": "high"}, "stripe_live_key": {"pattern": "sk_live_[A-Za-z0-9]{24}", "description": "Stripe Live Secret Key", "severity": "critical"}, "stripe_test_key": {"pattern": "sk_test_[A-Za-z0-9]{24}", "description": "Stripe Test Secret Key", "severity": "medium"}, "google_api": {"pattern": "AIza[0-9A-Za-z\\-_]{35}", "description": "Google API Key", "severity": "high"}, "firebase_key": {"pattern": "AAAA[A-Za-z0-9_-]{7}:[A-Za-z0-9_-]{140}", "description": "Firebase Server Key", "severity": "high"}, "twilio_sid": {"pattern": "AC[a-z0-9]{32}", "description": "<PERSON><PERSON><PERSON> Account SID", "severity": "medium"}, "twilio_token": {"pattern": "[a-z0-9]{32}", "description": "<PERSON><PERSON><PERSON>", "severity": "high", "context_required": ["twi<PERSON>", "auth", "token"]}, "mailgun_key": {"pattern": "key-[a-z0-9]{32}", "description": "Mailgun API Key", "severity": "medium"}, "sendgrid_key": {"pattern": "SG\\.[a-zA-Z0-9_-]{22}\\.[a-zA-Z0-9_-]{43}", "description": "SendGrid API Key", "severity": "medium"}, "discord_token": {"pattern": "[MN][A-Za-z\\d]{23}\\.[\\w-]{6}\\.[\\w-]{27}", "description": "Discord <PERSON><PERSON>", "severity": "medium"}, "discord_webhook": {"pattern": "https://discord(app)?\\.com/api/webhooks/[0-9]+/[A-Za-z0-9_-]+", "description": "Discord Webhook URL", "severity": "low"}}, "database": {"mongodb_connection": {"pattern": "mongodb://[^\\s]+", "description": "MongoDB Connection String", "severity": "high"}, "mysql_connection": {"pattern": "mysql://[^\\s]+", "description": "MySQL Connection String", "severity": "high"}, "postgresql_connection": {"pattern": "postgresql://[^\\s]+", "description": "PostgreSQL Connection String", "severity": "high"}, "redis_connection": {"pattern": "redis://[^\\s]+", "description": "Redis Connection String", "severity": "medium"}, "password_in_url": {"pattern": "://[^:]+:([^@]+)@", "description": "Password in URL", "severity": "medium"}}, "generic": {"private_key": {"pattern": "-----B<PERSON><PERSON> [A-Z ]+PRIVATE KEY-----", "description": "Private Key", "severity": "critical"}, "jwt_token": {"pattern": "eyJ[A-Za-z0-9-_=]+\\.[A-Za-z0-9-_=]+\\.?[A-Za-z0-9-_.+/=]*", "description": "JWT Token", "severity": "medium"}, "password_assignment": {"pattern": "(password|pwd|pass)\\s*[=:]\\s*['\"][^'\"]{8,}['\"]", "description": "Hardcoded Password", "severity": "medium"}, "api_key_assignment": {"pattern": "(api_key|apikey|api-key)\\s*[=:]\\s*['\"][^'\"]{16,}['\"]", "description": "Hardcoded API Key", "severity": "high"}, "secret_assignment": {"pattern": "(secret|secret_key)\\s*[=:]\\s*['\"][^'\"]{16,}['\"]", "description": "Hardcoded Secret", "severity": "high"}, "token_assignment": {"pattern": "(token|access_token)\\s*[=:]\\s*['\"][^'\"]{16,}['\"]", "description": "Hardcoded Token", "severity": "medium"}}, "cloud_services": {"azure_storage_key": {"pattern": "[A-Za-z0-9+/]{88}==", "description": "Azure Storage Account Key", "severity": "high", "context_required": ["azure", "storage"]}, "gcp_service_account": {"pattern": "\"type\":\\s*\"service_account\"", "description": "GCP Service Account Key", "severity": "critical"}, "heroku_api_key": {"pattern": "[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}", "description": "Heroku API Key", "severity": "medium", "context_required": ["<PERSON><PERSON>"]}, "digitalocean_token": {"pattern": "dop_v1_[a-f0-9]{64}", "description": "DigitalOcean Personal Access Token", "severity": "high"}}}