"""
ByteGuardX Core System Architecture
Implements the complete modular architecture with IPC bridge, plugin engine, and AI pipeline
"""

import logging
import asyncio
import threading
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from pathlib import Path
import json
import hashlib
from datetime import datetime
import uuid

from byteguardx.ipc.secure_bridge import SecureIPCBridge
from byteguardx.plugins.plugin_manager import PluginManager
from byteguardx.ai.inference_engine import AIInferenceEngine
from byteguardx.ai.explainability_engine import ExplainabilityEngine
from byteguardx.security.trust_chain import TrustChainManager
from byteguardx.security.audit_logger import TamperEvidentAuditLogger
from byteguardx.security.secrets_vault import SecretsVault
from byteguardx.database.connection_manager import DatabaseManager
from byteguardx.monitoring.observability import ObservabilityManager

logger = logging.getLogger(__name__)

@dataclass
class ScanContext:
    """Complete scan context with content hashing and traceability"""
    scan_id: str = field(default_factory=lambda: str(uuid.uuid4()))
    content_hash: str = ""
    file_path: str = ""
    git_blame: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: Dict[str, Any] = field(default_factory=dict)
    trust_level: str = "untrusted"

@dataclass
class Finding:
    """Enhanced finding with explainability and trust metrics"""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    type: str = ""
    severity: str = ""
    title: str = ""
    description: str = ""
    file_path: str = ""
    line_start: int = 0
    line_end: int = 0
    column_start: int = 0
    column_end: int = 0
    confidence: float = 0.0
    evidence: str = ""
    remediation: str = ""
    explanation: Dict[str, Any] = field(default_factory=dict)
    content_hash: str = ""
    git_blame: Dict[str, Any] = field(default_factory=dict)
    plugin_source: str = ""
    ai_prediction: bool = False
    references: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

class ByteGuardXCore:
    """
    Core ByteGuardX system orchestrator
    Manages all system components and coordinates the complete scan pipeline
    """
    
    def __init__(self, config_path: str = "config/byteguardx.toml"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        
        # Initialize core components
        self.ipc_bridge = SecureIPCBridge()
        self.plugin_manager = PluginManager()
        self.ai_inference = AIInferenceEngine()
        self.explainability = ExplainabilityEngine()
        self.trust_manager = TrustChainManager()
        self.audit_logger = TamperEvidentAuditLogger()
        self.secrets_vault = SecretsVault()
        self.database = DatabaseManager()
        self.observability = ObservabilityManager()
        
        # System state
        self.is_initialized = False
        self.active_scans = {}
        self.content_cache = {}
        self.findings_cache = {}
        
        logger.info("ByteGuardX Core initialized")
    
    async def initialize_system(self) -> bool:
        """Initialize all system components with proper dependency order"""
        try:
            # Step 1: Initialize security foundation
            await self.trust_manager.initialize()
            await self.secrets_vault.initialize()
            await self.audit_logger.initialize()
            
            # Step 2: Initialize database and observability
            await self.database.initialize()
            await self.observability.initialize()
            
            # Step 3: Initialize AI components
            await self.ai_inference.initialize()
            await self.explainability.initialize()
            
            # Step 4: Initialize plugin system
            await self.plugin_manager.initialize(self.trust_manager)
            
            # Step 5: Initialize IPC bridge
            await self.ipc_bridge.initialize()
            
            # Step 6: Setup system monitoring
            self._setup_system_monitoring()
            
            self.is_initialized = True
            
            # Audit system initialization
            await self.audit_logger.log_event(
                "system_initialization",
                {"status": "success", "components": self._get_component_status()},
                system_user=True
            )
            
            logger.info("ByteGuardX system fully initialized")
            return True
            
        except Exception as e:
            logger.error(f"System initialization failed: {e}")
            await self.audit_logger.log_event(
                "system_initialization_failed",
                {"error": str(e)},
                system_user=True
            )
            return False
    
    async def complete_scan_flow(self, code_input: Dict[str, Any]) -> List[Finding]:
        """
        Complete end-to-end scan flow with content hashing, sandboxing, and explainability
        This is the main data flow pipeline as specified in the requirements
        """
        if not self.is_initialized:
            raise RuntimeError("System not initialized")
        
        # Step 1: Content Ingestion & Hashing
        content_hash = self._calculate_content_hash(code_input)
        scan_context = ScanContext(
            content_hash=content_hash,
            file_path=code_input.get('file_path', ''),
            git_blame=await self._extract_git_blame(code_input.get('file_path', '')),
            metadata=code_input.get('metadata', {})
        )
        
        # Audit scan start
        await self.audit_logger.log_event(
            "scan_started",
            {
                "scan_id": scan_context.scan_id,
                "content_hash": content_hash,
                "file_path": scan_context.file_path
            }
        )
        
        # Step 2: Deduplication Check
        if content_hash in self.findings_cache:
            logger.info(f"Cache hit for content hash: {content_hash}")
            cached_findings = self.findings_cache[content_hash]
            await self.audit_logger.log_event(
                "scan_cache_hit",
                {"scan_id": scan_context.scan_id, "findings_count": len(cached_findings)}
            )
            return cached_findings
        
        # Step 3: Plugin Execution (Sandboxed)
        plugin_findings = await self._execute_plugins_sandboxed(code_input, scan_context)
        
        # Step 4: AI Inference Pipeline
        ai_predictions = await self._run_ai_inference(code_input, scan_context)
        
        # Step 5: Explainability Processing
        explanations = await self._generate_explanations(ai_predictions, code_input)
        
        # Step 6: Finding Enrichment & Deduplication
        all_findings = self._merge_findings(plugin_findings, ai_predictions)
        deduplicated_findings = self._deduplicate_findings(all_findings)
        enriched_findings = await self._enrich_findings(deduplicated_findings, scan_context, explanations)
        
        # Step 7: Persistence & Audit
        await self._persist_findings(content_hash, enriched_findings, scan_context)
        
        # Cache results
        self.findings_cache[content_hash] = enriched_findings
        
        # Final audit log
        await self.audit_logger.log_event(
            "scan_completed",
            {
                "scan_id": scan_context.scan_id,
                "findings_count": len(enriched_findings),
                "severity_breakdown": self._get_severity_breakdown(enriched_findings)
            }
        )
        
        return enriched_findings
    
    def _calculate_content_hash(self, code_input: Dict[str, Any]) -> str:
        """Calculate SHA-256 hash of content + metadata for deduplication"""
        content = code_input.get('content', '')
        metadata = json.dumps(code_input.get('metadata', {}), sort_keys=True)
        combined = f"{content}{metadata}"
        return hashlib.sha256(combined.encode()).hexdigest()
    
    async def _extract_git_blame(self, file_path: str) -> Dict[str, Any]:
        """Extract Git blame information for traceability"""
        try:
            if not file_path or not Path(file_path).exists():
                return {}
            
            # Use GitPython or similar to extract blame info
            # This is a placeholder implementation
            return {
                "last_commit": "abc123",
                "author": "<EMAIL>",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.warning(f"Failed to extract Git blame: {e}")
            return {}
    
    async def _execute_plugins_sandboxed(self, code_input: Dict[str, Any], scan_context: ScanContext) -> List[Finding]:
        """Execute plugins in sandboxed environment with trust enforcement"""
        plugin_findings = []
        active_plugins = await self.plugin_manager.get_active_plugins()
        
        for plugin in active_plugins:
            try:
                # Trust enforcement point
                if not await self.trust_manager.verify_plugin_trust(plugin):
                    logger.warning(f"Plugin {plugin.name} failed trust verification")
                    continue
                
                # Create sandbox context
                sandbox_context = await self.plugin_manager.create_sandbox(plugin)
                
                # Prepare plugin input
                plugin_input = {
                    'scan_id': scan_context.scan_id,
                    'content': code_input.get('content', ''),
                    'file_path': scan_context.file_path,
                    'content_hash': scan_context.content_hash,
                    'metadata': scan_context.metadata
                }
                
                # Execute in sandbox with resource limits
                with self.observability.trace_span(f"plugin_execution_{plugin.name}"):
                    result = await sandbox_context.execute(plugin_input)
                    
                    # Convert plugin results to Finding objects
                    for finding_data in result.get('findings', []):
                        finding = Finding(
                            **finding_data,
                            content_hash=scan_context.content_hash,
                            plugin_source=plugin.name,
                            ai_prediction=False
                        )
                        plugin_findings.append(finding)
                
                # Audit plugin execution
                await self.audit_logger.log_event(
                    "plugin_executed",
                    {
                        "plugin_name": plugin.name,
                        "scan_id": scan_context.scan_id,
                        "findings_count": len(result.get('findings', [])),
                        "execution_time_ms": result.get('execution_time_ms', 0)
                    }
                )
                
            except Exception as e:
                logger.error(f"Plugin {plugin.name} execution failed: {e}")
                await self.audit_logger.log_event(
                    "plugin_execution_failed",
                    {"plugin_name": plugin.name, "error": str(e)}
                )
        
        return plugin_findings
    
    async def _run_ai_inference(self, code_input: Dict[str, Any], scan_context: ScanContext) -> List[Finding]:
        """Run AI inference pipeline with model verification"""
        try:
            with self.observability.trace_span("ai_inference"):
                # Extract features
                features = await self.ai_inference.extract_features(code_input.get('content', ''))
                
                # Run inference
                predictions = await self.ai_inference.predict_vulnerabilities(features)
                
                # Convert predictions to Finding objects
                ai_findings = []
                for prediction in predictions:
                    finding = Finding(
                        type=prediction.get('type', 'vulnerability'),
                        severity=prediction.get('severity', 'medium'),
                        title=prediction.get('title', 'AI-detected vulnerability'),
                        description=prediction.get('description', ''),
                        file_path=scan_context.file_path,
                        line_start=prediction.get('line_start', 1),
                        line_end=prediction.get('line_end', 1),
                        confidence=prediction.get('confidence', 0.5),
                        content_hash=scan_context.content_hash,
                        ai_prediction=True
                    )
                    ai_findings.append(finding)
                
                return ai_findings
                
        except Exception as e:
            logger.error(f"AI inference failed: {e}")
            return []
    
    async def _generate_explanations(self, ai_predictions: List[Finding], code_input: Dict[str, Any]) -> Dict[str, Any]:
        """Generate explainability data for AI predictions"""
        explanations = {}
        
        for finding in ai_predictions:
            if finding.ai_prediction:
                try:
                    explanation = await self.explainability.explain_prediction(
                        finding, code_input.get('content', '')
                    )
                    explanations[finding.id] = explanation
                except Exception as e:
                    logger.warning(f"Failed to generate explanation for finding {finding.id}: {e}")
        
        return explanations
    
    def _merge_findings(self, plugin_findings: List[Finding], ai_predictions: List[Finding]) -> List[Finding]:
        """Merge findings from plugins and AI predictions"""
        return plugin_findings + ai_predictions
    
    def _deduplicate_findings(self, findings: List[Finding]) -> List[Finding]:
        """Deduplicate findings based on content hash and location"""
        seen_hashes = set()
        deduplicated = []
        
        for finding in findings:
            # Create deduplication key
            dedup_key = f"{finding.content_hash}:{finding.file_path}:{finding.line_start}:{finding.type}"
            
            if dedup_key not in seen_hashes:
                seen_hashes.add(dedup_key)
                deduplicated.append(finding)
        
        return deduplicated
    
    async def _enrich_findings(self, findings: List[Finding], scan_context: ScanContext, explanations: Dict[str, Any]) -> List[Finding]:
        """Enrich findings with explanations, blame info, and remediation"""
        enriched = []
        
        for finding in findings:
            # Add explanation if available
            if finding.id in explanations:
                finding.explanation = explanations[finding.id]
            
            # Add Git blame context
            finding.git_blame = scan_context.git_blame
            
            # Generate remediation suggestions
            finding.remediation = await self._generate_remediation(finding)
            
            enriched.append(finding)
        
        return enriched
    
    async def _generate_remediation(self, finding: Finding) -> str:
        """Generate remediation suggestions for findings"""
        # This would integrate with AI models for remediation generation
        # Placeholder implementation
        remediation_templates = {
            'secret': 'Remove hardcoded secret and use environment variables or secure vault',
            'vulnerability': 'Update dependency to latest secure version',
            'code_smell': 'Refactor code to follow security best practices'
        }
        
        return remediation_templates.get(finding.type, 'Review and address security issue')
    
    async def _persist_findings(self, content_hash: str, findings: List[Finding], scan_context: ScanContext):
        """Persist findings to database with audit trail"""
        try:
            await self.database.store_scan_results(content_hash, findings, scan_context)
            logger.info(f"Persisted {len(findings)} findings for content hash {content_hash}")
        except Exception as e:
            logger.error(f"Failed to persist findings: {e}")
    
    def _get_severity_breakdown(self, findings: List[Finding]) -> Dict[str, int]:
        """Get breakdown of findings by severity"""
        breakdown = {}
        for finding in findings:
            breakdown[finding.severity] = breakdown.get(finding.severity, 0) + 1
        return breakdown
    
    def _get_component_status(self) -> Dict[str, str]:
        """Get status of all system components"""
        return {
            "ipc_bridge": "initialized" if self.ipc_bridge.is_initialized else "failed",
            "plugin_manager": "initialized" if self.plugin_manager.is_initialized else "failed",
            "ai_inference": "initialized" if self.ai_inference.is_initialized else "failed",
            "trust_manager": "initialized" if self.trust_manager.is_initialized else "failed",
            "database": "initialized" if self.database.is_initialized else "failed"
        }
    
    def _setup_system_monitoring(self):
        """Setup comprehensive system monitoring"""
        # Setup OpenTelemetry spans and metrics
        self.observability.setup_metrics([
            "scan_duration_seconds",
            "plugin_execution_time_seconds", 
            "ai_inference_time_seconds",
            "findings_per_scan",
            "cache_hit_rate"
        ])
    
    def _load_config(self) -> Dict[str, Any]:
        """Load system configuration"""
        if self.config_path.exists():
            import toml
            return toml.load(self.config_path)
        else:
            return self._get_default_config()
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Get default system configuration"""
        return {
            "system": {
                "max_concurrent_scans": 10,
                "cache_size": 1000,
                "audit_retention_days": 90
            },
            "security": {
                "require_plugin_signatures": True,
                "sandbox_timeout_seconds": 300,
                "trust_level_required": "medium"
            },
            "ai": {
                "enable_explainability": True,
                "confidence_threshold": 0.7,
                "max_inference_time_seconds": 60
            }
        }

# Global system instance
byteguardx_core = ByteGuardXCore()
