"""
ByteGuardX Git Integration
Implements pre-commit hooks, staged diff scanning, and Git workflow integration
"""

import logging
import subprocess
import asyncio
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from pathlib import Path
import json
import tempfile
import shutil
import os
from datetime import datetime
import git
from git import Repo, InvalidGitRepositoryError

logger = logging.getLogger(__name__)

@dataclass
class GitFileChange:
    """Git file change information"""
    file_path: str
    change_type: str  # 'A', 'M', 'D', 'R', 'C'
    content: str
    old_content: Optional[str] = None
    line_changes: List[Tuple[int, str]] = None

@dataclass
class GitBlameInfo:
    """Git blame information for traceability"""
    line_number: int
    commit_hash: str
    author: str
    author_email: str
    commit_date: datetime
    commit_message: str

@dataclass
class ScanResult:
    """Scan result for Git integration"""
    file_path: str
    findings: List[Dict[str, Any]]
    severity_counts: Dict[str, int]
    scan_time: datetime

class GitIntegration:
    """
    Comprehensive Git integration for ByteGuardX
    Handles pre-commit hooks, staged scanning, and blame analysis
    """
    
    def __init__(self, repo_path: Optional[str] = None):
        self.repo_path = Path(repo_path) if repo_path else Path.cwd()
        self.repo: Optional[Repo] = None
        self.scanner = None  # Will be injected
        
        # Hook templates
        self.hook_templates = {
            'pre-commit': self._get_precommit_hook_template(),
            'pre-push': self._get_prepush_hook_template(),
            'commit-msg': self._get_commitmsg_hook_template()
        }
        
        logger.info(f"Git Integration initialized for: {self.repo_path}")
    
    async def initialize(self, scanner=None):
        """Initialize Git integration"""
        try:
            # Initialize Git repository
            self.repo = Repo(self.repo_path)
            self.scanner = scanner
            
            # Verify repository state
            if self.repo.bare:
                raise ValueError("Cannot work with bare repository")
            
            logger.info(f"Git repository initialized: {self.repo.working_dir}")
            return True
            
        except InvalidGitRepositoryError:
            logger.error(f"Not a Git repository: {self.repo_path}")
            return False
        except Exception as e:
            logger.error(f"Git initialization failed: {e}")
            return False
    
    def _get_precommit_hook_template(self) -> str:
        """Get pre-commit hook template"""
        return '''#!/bin/bash
# ByteGuardX Pre-commit Hook
set -e

echo "🛡️  ByteGuardX Security Scan Starting..."

# Get staged files
STAGED_FILES=$(git diff --cached --name-only --diff-filter=ACM)

if [ -z "$STAGED_FILES" ]; then
    echo "✅ No staged files to scan"
    exit 0
fi

# Create temporary file for results
RESULTS_FILE=$(mktemp)

# Run ByteGuardX scan on staged files
python -m byteguardx.cli scan --staged --format=json --output="$RESULTS_FILE" --severity=medium

# Check scan results
if [ $? -ne 0 ]; then
    echo "❌ ByteGuardX scan failed"
    rm -f "$RESULTS_FILE"
    exit 1
fi

# Parse results
CRITICAL_COUNT=$(jq '[.findings[] | select(.severity == "critical")] | length' "$RESULTS_FILE" 2>/dev/null || echo "0")
HIGH_COUNT=$(jq '[.findings[] | select(.severity == "high")] | length' "$RESULTS_FILE" 2>/dev/null || echo "0")
TOTAL_COUNT=$(jq '.findings | length' "$RESULTS_FILE" 2>/dev/null || echo "0")

# Clean up
rm -f "$RESULTS_FILE"

# Check for blocking issues
if [ "$CRITICAL_COUNT" -gt 0 ]; then
    echo "❌ Critical security issues found ($CRITICAL_COUNT). Commit blocked."
    echo "   Run 'byteguardx scan --staged --interactive' to review and fix issues."
    echo "   Use 'git commit --no-verify' to bypass (not recommended)."
    exit 1
fi

if [ "$HIGH_COUNT" -gt 0 ]; then
    echo "⚠️  High severity security issues found ($HIGH_COUNT)."
    echo "   Consider running 'byteguardx scan --staged --interactive' to review."
    
    # Interactive prompt for high severity issues
    if [ -t 0 ]; then  # Check if running in terminal
        read -p "Continue with commit? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            echo "Commit cancelled by user"
            exit 1
        fi
    fi
fi

if [ "$TOTAL_COUNT" -gt 0 ]; then
    echo "ℹ️  Found $TOTAL_COUNT security findings (Critical: $CRITICAL_COUNT, High: $HIGH_COUNT)"
fi

echo "✅ ByteGuardX security scan completed"
exit 0
'''
    
    def _get_prepush_hook_template(self) -> str:
        """Get pre-push hook template"""
        return '''#!/bin/bash
# ByteGuardX Pre-push Hook
set -e

echo "🛡️  ByteGuardX Pre-push Security Check..."

# Get commits being pushed
while read local_ref local_sha remote_ref remote_sha; do
    if [ "$local_sha" != "0000000000000000000000000000000000000000" ]; then
        # Get changed files in commits being pushed
        if [ "$remote_sha" != "0000000000000000000000000000000000000000" ]; then
            CHANGED_FILES=$(git diff --name-only "$remote_sha..$local_sha")
        else
            CHANGED_FILES=$(git diff --name-only "$local_sha")
        fi
        
        if [ -n "$CHANGED_FILES" ]; then
            echo "Scanning changed files in push..."
            
            # Run security scan on changed files
            echo "$CHANGED_FILES" | xargs python -m byteguardx.cli scan --format=json --severity=critical
            
            if [ $? -ne 0 ]; then
                echo "❌ Critical security issues found in push. Push rejected."
                exit 1
            fi
        fi
    fi
done

echo "✅ Pre-push security check passed"
exit 0
'''
    
    def _get_commitmsg_hook_template(self) -> str:
        """Get commit-msg hook template"""
        return '''#!/bin/bash
# ByteGuardX Commit Message Hook

COMMIT_MSG_FILE=$1
COMMIT_MSG=$(cat "$COMMIT_MSG_FILE")

# Check for security-related keywords in commit message
if echo "$COMMIT_MSG" | grep -qi "\\(security\\|vulnerability\\|cve\\|exploit\\|fix\\)"; then
    echo "🛡️  Security-related commit detected"
    
    # Add security scan summary to commit message
    SCAN_SUMMARY=$(python -m byteguardx.cli scan --staged --format=summary 2>/dev/null || echo "")
    
    if [ -n "$SCAN_SUMMARY" ]; then
        echo "" >> "$COMMIT_MSG_FILE"
        echo "ByteGuardX Security Scan Summary:" >> "$COMMIT_MSG_FILE"
        echo "$SCAN_SUMMARY" >> "$COMMIT_MSG_FILE"
    fi
fi

exit 0
'''
    
    async def install_hooks(self, hooks: List[str] = None) -> Dict[str, bool]:
        """Install Git hooks for ByteGuardX"""
        if not self.repo:
            raise ValueError("Git repository not initialized")
        
        hooks_to_install = hooks or ['pre-commit', 'pre-push', 'commit-msg']
        installation_results = {}
        
        hooks_dir = Path(self.repo.git_dir) / 'hooks'
        hooks_dir.mkdir(exist_ok=True)
        
        for hook_name in hooks_to_install:
            try:
                hook_path = hooks_dir / hook_name
                hook_template = self.hook_templates.get(hook_name)
                
                if not hook_template:
                    logger.warning(f"No template available for hook: {hook_name}")
                    installation_results[hook_name] = False
                    continue
                
                # Backup existing hook if it exists
                if hook_path.exists():
                    backup_path = hook_path.with_suffix('.backup')
                    shutil.copy2(hook_path, backup_path)
                    logger.info(f"Backed up existing hook: {hook_name}")
                
                # Write new hook
                with open(hook_path, 'w') as f:
                    f.write(hook_template)
                
                # Make executable
                hook_path.chmod(0o755)
                
                installation_results[hook_name] = True
                logger.info(f"Installed Git hook: {hook_name}")
                
            except Exception as e:
                logger.error(f"Failed to install hook {hook_name}: {e}")
                installation_results[hook_name] = False
        
        return installation_results
    
    async def scan_staged_changes(self) -> List[ScanResult]:
        """Scan only staged changes for efficiency"""
        if not self.repo:
            raise ValueError("Git repository not initialized")
        
        try:
            # Get staged files
            staged_files = self._get_staged_files()
            
            if not staged_files:
                logger.info("No staged files to scan")
                return []
            
            scan_results = []
            
            for file_change in staged_files:
                if self.scanner:
                    # Scan file content
                    findings = await self.scanner.scan_content(
                        file_change.content,
                        file_change.file_path
                    )
                    
                    # Count findings by severity
                    severity_counts = {}
                    for finding in findings:
                        severity = finding.get('severity', 'unknown')
                        severity_counts[severity] = severity_counts.get(severity, 0) + 1
                    
                    scan_result = ScanResult(
                        file_path=file_change.file_path,
                        findings=findings,
                        severity_counts=severity_counts,
                        scan_time=datetime.now()
                    )
                    
                    scan_results.append(scan_result)
            
            return scan_results
            
        except Exception as e:
            logger.error(f"Staged changes scan failed: {e}")
            return []
    
    def _get_staged_files(self) -> List[GitFileChange]:
        """Get staged files with their content"""
        staged_files = []
        
        try:
            # Get staged changes
            staged_items = self.repo.index.diff("HEAD")
            
            for item in staged_items:
                if item.change_type in ['A', 'M']:  # Added or Modified
                    try:
                        # Get staged content
                        content = item.a_blob.data_stream.read().decode('utf-8', errors='ignore')
                        
                        # Get old content for modified files
                        old_content = None
                        if item.change_type == 'M' and item.b_blob:
                            old_content = item.b_blob.data_stream.read().decode('utf-8', errors='ignore')
                        
                        file_change = GitFileChange(
                            file_path=item.a_path,
                            change_type=item.change_type,
                            content=content,
                            old_content=old_content
                        )
                        
                        staged_files.append(file_change)
                        
                    except Exception as e:
                        logger.warning(f"Failed to read staged file {item.a_path}: {e}")
            
            return staged_files
            
        except Exception as e:
            logger.error(f"Failed to get staged files: {e}")
            return []
    
    async def generate_blame_context(self, file_path: str, line_number: int) -> Optional[GitBlameInfo]:
        """Generate Git blame context for specific line"""
        if not self.repo:
            return None
        
        try:
            # Get blame information
            blame_info = list(self.repo.blame('HEAD', file_path))
            
            if line_number <= len(blame_info):
                commit, lines = blame_info[line_number - 1]
                
                return GitBlameInfo(
                    line_number=line_number,
                    commit_hash=commit.hexsha[:8],
                    author=commit.author.name,
                    author_email=commit.author.email,
                    commit_date=commit.committed_datetime,
                    commit_message=commit.message.strip()
                )
            
        except Exception as e:
            logger.warning(f"Failed to generate blame context for {file_path}:{line_number}: {e}")
        
        return None
    
    async def scan_commit_range(self, start_commit: str, end_commit: str) -> List[ScanResult]:
        """Scan files changed in commit range"""
        if not self.repo:
            raise ValueError("Git repository not initialized")
        
        try:
            # Get changed files in commit range
            changed_files = self.repo.git.diff(
                '--name-only',
                f"{start_commit}..{end_commit}"
            ).split('\n')
            
            changed_files = [f for f in changed_files if f.strip()]
            
            scan_results = []
            
            for file_path in changed_files:
                try:
                    # Get file content at end commit
                    file_content = self.repo.git.show(f"{end_commit}:{file_path}")
                    
                    if self.scanner:
                        findings = await self.scanner.scan_content(file_content, file_path)
                        
                        severity_counts = {}
                        for finding in findings:
                            severity = finding.get('severity', 'unknown')
                            severity_counts[severity] = severity_counts.get(severity, 0) + 1
                        
                        scan_result = ScanResult(
                            file_path=file_path,
                            findings=findings,
                            severity_counts=severity_counts,
                            scan_time=datetime.now()
                        )
                        
                        scan_results.append(scan_result)
                
                except Exception as e:
                    logger.warning(f"Failed to scan file {file_path}: {e}")
            
            return scan_results
            
        except Exception as e:
            logger.error(f"Commit range scan failed: {e}")
            return []
    
    async def create_security_branch(self, base_branch: str = "main") -> str:
        """Create security fix branch"""
        if not self.repo:
            raise ValueError("Git repository not initialized")
        
        try:
            # Generate branch name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            branch_name = f"security/byteguardx_fixes_{timestamp}"
            
            # Create and checkout new branch
            new_branch = self.repo.create_head(branch_name, base_branch)
            new_branch.checkout()
            
            logger.info(f"Created security branch: {branch_name}")
            return branch_name
            
        except Exception as e:
            logger.error(f"Failed to create security branch: {e}")
            raise
    
    async def generate_security_report(self, scan_results: List[ScanResult]) -> Dict[str, Any]:
        """Generate comprehensive security report for Git integration"""
        total_findings = sum(len(result.findings) for result in scan_results)
        
        severity_totals = {}
        files_with_issues = []
        
        for result in scan_results:
            if result.findings:
                files_with_issues.append({
                    'file_path': result.file_path,
                    'finding_count': len(result.findings),
                    'severity_counts': result.severity_counts
                })
                
                for severity, count in result.severity_counts.items():
                    severity_totals[severity] = severity_totals.get(severity, 0) + count
        
        # Generate recommendations
        recommendations = []
        
        if severity_totals.get('critical', 0) > 0:
            recommendations.append("🚨 Critical vulnerabilities found - immediate action required")
        
        if severity_totals.get('high', 0) > 0:
            recommendations.append("⚠️ High severity issues should be addressed before deployment")
        
        if total_findings == 0:
            recommendations.append("✅ No security issues detected in scanned files")
        
        return {
            'scan_summary': {
                'total_files_scanned': len(scan_results),
                'files_with_issues': len(files_with_issues),
                'total_findings': total_findings,
                'severity_breakdown': severity_totals
            },
            'files_with_issues': files_with_issues,
            'recommendations': recommendations,
            'scan_timestamp': datetime.now().isoformat(),
            'repository': str(self.repo_path)
        }
    
    def get_repository_info(self) -> Dict[str, Any]:
        """Get repository information"""
        if not self.repo:
            return {}
        
        try:
            return {
                'repository_path': str(self.repo_path),
                'current_branch': self.repo.active_branch.name,
                'latest_commit': self.repo.head.commit.hexsha[:8],
                'is_dirty': self.repo.is_dirty(),
                'untracked_files': len(self.repo.untracked_files),
                'remote_url': self.repo.remotes.origin.url if self.repo.remotes else None
            }
        except Exception as e:
            logger.error(f"Failed to get repository info: {e}")
            return {}
