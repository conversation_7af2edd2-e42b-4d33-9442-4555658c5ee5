"""
Comprehensive Vulnerability Scanner for ByteGuardX
Detects real security vulnerabilities in code
"""

import re
import logging
import ast
import json
from typing import List, Dict, Any, Tuple, Optional
from dataclasses import dataclass, asdict
from datetime import datetime
from pathlib import Path

logger = logging.getLogger(__name__)

@dataclass
class VulnerabilityMatch:
    """Data structure for vulnerability detection results"""
    type: str
    subtype: str
    severity: str
    confidence: float
    file_path: str
    line_number: int
    column_start: int
    column_end: int
    context: str
    description: str
    cwe_id: str
    owasp_category: str
    remediation: str
    
    # Enhanced fields
    detection_timestamp: datetime = None
    risk_score: float = 0.0
    exploitability: str = "unknown"
    impact: str = "unknown"
    
    def __post_init__(self):
        if self.detection_timestamp is None:
            self.detection_timestamp = datetime.now()
        self.risk_score = self._calculate_risk_score()
    
    def _calculate_risk_score(self) -> float:
        """Calculate risk score based on severity, confidence, and exploitability"""
        severity_weights = {'critical': 1.0, 'high': 0.8, 'medium': 0.6, 'low': 0.4}
        exploitability_weights = {'high': 1.0, 'medium': 0.7, 'low': 0.4, 'unknown': 0.5}
        
        base_score = severity_weights.get(self.severity.lower(), 0.5)
        exploit_score = exploitability_weights.get(self.exploitability.lower(), 0.5)
        
        return min((base_score * 0.6 + exploit_score * 0.2 + self.confidence * 0.2), 1.0)

class VulnerabilityScanner:
    """
    Comprehensive vulnerability scanner for multiple languages and frameworks
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.patterns = self._load_vulnerability_patterns()
        self.compiled_patterns = self._compile_patterns()
        
    def _load_vulnerability_patterns(self) -> Dict:
        """Load comprehensive vulnerability patterns"""
        return {
            "injection": {
                "sql_injection": {
                    "patterns": [
                        r"(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER)\s+.*\+.*['\"]",
                        r"['\"].*\+.*['\"].*WHERE",
                        r"execute\s*\(\s*['\"].*\+.*['\"]",
                        r"query\s*\(\s*['\"].*\+.*['\"]",
                        r"cursor\.execute\s*\(\s*['\"].*%.*['\"]",
                        r"Statement\.executeQuery\s*\(\s*['\"].*\+.*['\"]"
                    ],
                    "description": "SQL Injection vulnerability detected",
                    "severity": "critical",
                    "cwe_id": "CWE-89",
                    "owasp_category": "A03:2021 – Injection",
                    "exploitability": "high",
                    "impact": "high",
                    "remediation": "Use parameterized queries or prepared statements"
                },
                "command_injection": {
                    "patterns": [
                        r"os\.system\s*\(\s*.*\+.*\)",
                        r"subprocess\.(call|run|Popen)\s*\(\s*.*\+.*\)",
                        r"exec\s*\(\s*.*\+.*\)",
                        r"eval\s*\(\s*.*\+.*\)",
                        r"Runtime\.getRuntime\(\)\.exec\s*\(\s*.*\+.*\)",
                        r"ProcessBuilder\s*\(\s*.*\+.*\)"
                    ],
                    "description": "Command Injection vulnerability detected",
                    "severity": "critical",
                    "cwe_id": "CWE-78",
                    "owasp_category": "A03:2021 – Injection",
                    "exploitability": "high",
                    "impact": "critical",
                    "remediation": "Validate and sanitize all user inputs, use safe APIs"
                },
                "ldap_injection": {
                    "patterns": [
                        r"LdapContext\.search\s*\(\s*.*\+.*\)",
                        r"ldap\.search\s*\(\s*.*\+.*\)",
                        r"DirectorySearcher\s*\(\s*.*\+.*\)"
                    ],
                    "description": "LDAP Injection vulnerability detected",
                    "severity": "high",
                    "cwe_id": "CWE-90",
                    "owasp_category": "A03:2021 – Injection",
                    "exploitability": "medium",
                    "impact": "high",
                    "remediation": "Use parameterized LDAP queries and input validation"
                },
                "xpath_injection": {
                    "patterns": [
                        r"XPath\.compile\s*\(\s*.*\+.*\)",
                        r"xpath\.evaluate\s*\(\s*.*\+.*\)",
                        r"selectNodes\s*\(\s*.*\+.*\)"
                    ],
                    "description": "XPath Injection vulnerability detected",
                    "severity": "high",
                    "cwe_id": "CWE-643",
                    "owasp_category": "A03:2021 – Injection",
                    "exploitability": "medium",
                    "impact": "medium",
                    "remediation": "Use parameterized XPath queries"
                }
            },
            "xss": {
                "reflected_xss": {
                    "patterns": [
                        r"document\.write\s*\(\s*.*request\.",
                        r"innerHTML\s*=\s*.*request\.",
                        r"outerHTML\s*=\s*.*request\.",
                        r"response\.write\s*\(\s*.*request\.",
                        r"echo\s+.*\$_GET",
                        r"echo\s+.*\$_POST",
                        r"print\s+.*request\."
                    ],
                    "description": "Reflected XSS vulnerability detected",
                    "severity": "high",
                    "cwe_id": "CWE-79",
                    "owasp_category": "A03:2021 – Injection",
                    "exploitability": "high",
                    "impact": "medium",
                    "remediation": "Encode output and validate input"
                },
                "stored_xss": {
                    "patterns": [
                        r"innerHTML\s*=\s*.*database",
                        r"document\.write\s*\(\s*.*database",
                        r"response\.write\s*\(\s*.*database"
                    ],
                    "description": "Stored XSS vulnerability detected",
                    "severity": "high",
                    "cwe_id": "CWE-79",
                    "owasp_category": "A03:2021 – Injection",
                    "exploitability": "high",
                    "impact": "high",
                    "remediation": "Encode output and sanitize stored data"
                },
                "dom_xss": {
                    "patterns": [
                        r"document\.location\s*=\s*.*location\.hash",
                        r"window\.location\s*=\s*.*location\.search",
                        r"eval\s*\(\s*.*location\."
                    ],
                    "description": "DOM-based XSS vulnerability detected",
                    "severity": "medium",
                    "cwe_id": "CWE-79",
                    "owasp_category": "A03:2021 – Injection",
                    "exploitability": "medium",
                    "impact": "medium",
                    "remediation": "Validate and sanitize DOM manipulation"
                }
            },
            "authentication": {
                "weak_password_policy": {
                    "patterns": [
                        r"password\.length\s*<\s*[1-7]",
                        r"len\(password\)\s*<\s*[1-7]",
                        r"password_min_length\s*=\s*[1-7]"
                    ],
                    "description": "Weak password policy detected",
                    "severity": "medium",
                    "cwe_id": "CWE-521",
                    "owasp_category": "A07:2021 – Identification and Authentication Failures",
                    "exploitability": "medium",
                    "impact": "medium",
                    "remediation": "Implement strong password requirements"
                },
                "hardcoded_credentials": {
                    "patterns": [
                        r"password\s*=\s*['\"][^'\"]{1,}['\"]",
                        r"secret\s*=\s*['\"][^'\"]{1,}['\"]",
                        r"api_key\s*=\s*['\"][^'\"]{1,}['\"]"
                    ],
                    "description": "Hardcoded credentials detected",
                    "severity": "high",
                    "cwe_id": "CWE-798",
                    "owasp_category": "A07:2021 – Identification and Authentication Failures",
                    "exploitability": "high",
                    "impact": "high",
                    "remediation": "Use secure credential storage"
                },
                "missing_authentication": {
                    "patterns": [
                        r"@app\.route\s*\(\s*['\"][^'\"]*['\"].*methods.*POST.*\)",
                        r"@RequestMapping\s*\(\s*.*method.*POST.*\)",
                        r"app\.(post|put|delete)\s*\(\s*['\"][^'\"]*['\"]"
                    ],
                    "description": "Potentially missing authentication on sensitive endpoint",
                    "severity": "medium",
                    "cwe_id": "CWE-306",
                    "owasp_category": "A07:2021 – Identification and Authentication Failures",
                    "exploitability": "medium",
                    "impact": "high",
                    "remediation": "Implement proper authentication checks"
                }
            },
            "authorization": {
                "missing_access_control": {
                    "patterns": [
                        r"@app\.route\s*\(\s*['\"][^'\"]*admin[^'\"]*['\"]",
                        r"@RequestMapping\s*\(\s*['\"][^'\"]*admin[^'\"]*['\"]",
                        r"if\s+user\.is_admin\s*:",
                        r"if\s+.*\.role\s*==\s*['\"]admin['\"]"
                    ],
                    "description": "Potentially missing access control checks",
                    "severity": "high",
                    "cwe_id": "CWE-862",
                    "owasp_category": "A01:2021 – Broken Access Control",
                    "exploitability": "medium",
                    "impact": "high",
                    "remediation": "Implement proper authorization checks"
                },
                "privilege_escalation": {
                    "patterns": [
                        r"user\.role\s*=\s*['\"]admin['\"]",
                        r"setuid\s*\(\s*0\s*\)",
                        r"sudo\s+.*without.*password"
                    ],
                    "description": "Potential privilege escalation vulnerability",
                    "severity": "critical",
                    "cwe_id": "CWE-269",
                    "owasp_category": "A01:2021 – Broken Access Control",
                    "exploitability": "medium",
                    "impact": "critical",
                    "remediation": "Implement principle of least privilege"
                }
            },
            "cryptography": {
                "weak_encryption": {
                    "patterns": [
                        r"DES\s*\(",
                        r"MD5\s*\(",
                        r"SHA1\s*\(",
                        r"RC4\s*\(",
                        r"Cipher\.getInstance\s*\(\s*['\"]DES['\"]",
                        r"hashlib\.md5\s*\(",
                        r"hashlib\.sha1\s*\("
                    ],
                    "description": "Weak cryptographic algorithm detected",
                    "severity": "medium",
                    "cwe_id": "CWE-327",
                    "owasp_category": "A02:2021 – Cryptographic Failures",
                    "exploitability": "low",
                    "impact": "medium",
                    "remediation": "Use strong cryptographic algorithms (AES, SHA-256+)"
                },
                "hardcoded_crypto_key": {
                    "patterns": [
                        r"key\s*=\s*['\"][A-Za-z0-9+/=]{16,}['\"]",
                        r"secret_key\s*=\s*['\"][A-Za-z0-9+/=]{16,}['\"]",
                        r"encryption_key\s*=\s*['\"][A-Za-z0-9+/=]{16,}['\"]"
                    ],
                    "description": "Hardcoded cryptographic key detected",
                    "severity": "high",
                    "cwe_id": "CWE-321",
                    "owasp_category": "A02:2021 – Cryptographic Failures",
                    "exploitability": "high",
                    "impact": "high",
                    "remediation": "Use secure key management systems"
                },
                "insecure_random": {
                    "patterns": [
                        r"random\.random\s*\(",
                        r"Math\.random\s*\(",
                        r"rand\s*\(",
                        r"srand\s*\("
                    ],
                    "description": "Insecure random number generation",
                    "severity": "medium",
                    "cwe_id": "CWE-338",
                    "owasp_category": "A02:2021 – Cryptographic Failures",
                    "exploitability": "low",
                    "impact": "medium",
                    "remediation": "Use cryptographically secure random generators"
                }
            },
            "file_handling": {
                "path_traversal": {
                    "patterns": [
                        r"open\s*\(\s*.*\+.*\.\./",
                        r"File\s*\(\s*.*\+.*\.\./",
                        r"FileInputStream\s*\(\s*.*\+.*\.\./",
                        r"readFile\s*\(\s*.*\+.*\.\./"
                    ],
                    "description": "Path traversal vulnerability detected",
                    "severity": "high",
                    "cwe_id": "CWE-22",
                    "owasp_category": "A01:2021 – Broken Access Control",
                    "exploitability": "high",
                    "impact": "medium",
                    "remediation": "Validate and sanitize file paths"
                },
                "file_upload_vulnerability": {
                    "patterns": [
                        r"upload.*without.*validation",
                        r"move_uploaded_file\s*\(\s*.*without",
                        r"MultipartFile.*save.*without"
                    ],
                    "description": "Insecure file upload detected",
                    "severity": "high",
                    "cwe_id": "CWE-434",
                    "owasp_category": "A04:2021 – Insecure Design",
                    "exploitability": "high",
                    "impact": "high",
                    "remediation": "Implement file type validation and sandboxing"
                }
            },
            "deserialization": {
                "unsafe_deserialization": {
                    "patterns": [
                        r"pickle\.loads\s*\(",
                        r"yaml\.load\s*\(",
                        r"ObjectInputStream\.readObject\s*\(",
                        r"unserialize\s*\(",
                        r"JSON\.parse\s*\(\s*.*user"
                    ],
                    "description": "Unsafe deserialization detected",
                    "severity": "critical",
                    "cwe_id": "CWE-502",
                    "owasp_category": "A08:2021 – Software and Data Integrity Failures",
                    "exploitability": "high",
                    "impact": "critical",
                    "remediation": "Use safe deserialization methods and validate input"
                }
            },
            "logging": {
                "log_injection": {
                    "patterns": [
                        r"log\.(info|debug|error|warn)\s*\(\s*.*\+.*user",
                        r"logger\.(info|debug|error|warn)\s*\(\s*.*\+.*request",
                        r"console\.log\s*\(\s*.*\+.*user"
                    ],
                    "description": "Log injection vulnerability detected",
                    "severity": "medium",
                    "cwe_id": "CWE-117",
                    "owasp_category": "A09:2021 – Security Logging and Monitoring Failures",
                    "exploitability": "low",
                    "impact": "low",
                    "remediation": "Sanitize log inputs and use structured logging"
                },
                "sensitive_data_logging": {
                    "patterns": [
                        r"log.*password",
                        r"log.*secret",
                        r"log.*token",
                        r"console\.log.*password"
                    ],
                    "description": "Sensitive data in logs detected",
                    "severity": "medium",
                    "cwe_id": "CWE-532",
                    "owasp_category": "A09:2021 – Security Logging and Monitoring Failures",
                    "exploitability": "low",
                    "impact": "medium",
                    "remediation": "Remove sensitive data from logs"
                }
            }
        }

    def _compile_patterns(self) -> Dict:
        """Compile regex patterns for better performance"""
        compiled = {}
        for category, vulnerabilities in self.patterns.items():
            compiled[category] = {}
            for vuln_name, vuln_config in vulnerabilities.items():
                compiled[category][vuln_name] = {
                    "patterns": [],
                    "config": vuln_config
                }
                for pattern in vuln_config["patterns"]:
                    try:
                        compiled[category][vuln_name]["patterns"].append(
                            re.compile(pattern, re.IGNORECASE | re.MULTILINE)
                        )
                    except re.error as e:
                        logger.error(f"Invalid regex pattern for {vuln_name}: {e}")
        return compiled

    def scan_content(self, content: str, file_path: str, language: str = None) -> List[VulnerabilityMatch]:
        """
        Scan content for vulnerabilities
        """
        findings = []
        lines = content.splitlines()

        # Detect language if not provided
        if not language:
            language = self._detect_language(file_path)

        # Scan each category
        for category, vulnerabilities in self.compiled_patterns.items():
            for vuln_name, vuln_data in vulnerabilities.items():
                vuln_config = vuln_data["config"]

                # Skip if not applicable to this language
                if not self._is_applicable_to_language(vuln_name, language):
                    continue

                # Scan with each pattern
                for pattern in vuln_data["patterns"]:
                    matches = pattern.finditer(content)

                    for match in matches:
                        # Calculate line and column
                        line_num = content[:match.start()].count('\n') + 1
                        line_start = content.rfind('\n', 0, match.start()) + 1
                        column_start = match.start() - line_start
                        column_end = match.end() - line_start

                        # Get context
                        context_start = max(0, line_num - 3)
                        context_end = min(len(lines), line_num + 2)
                        context = '\n'.join(lines[context_start:context_end])

                        # Calculate confidence
                        confidence = self._calculate_confidence(match, context, vuln_config)

                        # Create vulnerability match
                        vulnerability = VulnerabilityMatch(
                            type="vulnerability",
                            subtype=vuln_name,
                            severity=vuln_config["severity"],
                            confidence=confidence,
                            file_path=file_path,
                            line_number=line_num,
                            column_start=column_start,
                            column_end=column_end,
                            context=context,
                            description=vuln_config["description"],
                            cwe_id=vuln_config["cwe_id"],
                            owasp_category=vuln_config["owasp_category"],
                            remediation=vuln_config["remediation"],
                            exploitability=vuln_config.get("exploitability", "unknown"),
                            impact=vuln_config.get("impact", "unknown")
                        )

                        findings.append(vulnerability)

        # Remove duplicates and low-confidence findings
        filtered_findings = self._filter_findings(findings)

        return filtered_findings

    def _detect_language(self, file_path: str) -> str:
        """Detect programming language from file extension"""
        extension_map = {
            '.py': 'python',
            '.js': 'javascript',
            '.ts': 'typescript',
            '.java': 'java',
            '.php': 'php',
            '.rb': 'ruby',
            '.go': 'go',
            '.cs': 'csharp',
            '.cpp': 'cpp',
            '.c': 'c',
            '.rs': 'rust',
            '.kt': 'kotlin',
            '.swift': 'swift',
            '.scala': 'scala'
        }

        ext = Path(file_path).suffix.lower()
        return extension_map.get(ext, 'unknown')

    def _is_applicable_to_language(self, vuln_name: str, language: str) -> bool:
        """Check if vulnerability pattern is applicable to the language"""
        language_patterns = {
            'python': ['sql_injection', 'command_injection', 'weak_encryption', 'unsafe_deserialization',
                      'path_traversal', 'hardcoded_credentials', 'log_injection', 'insecure_random'],
            'javascript': ['xss', 'sql_injection', 'command_injection', 'weak_encryption', 'unsafe_deserialization',
                          'missing_authentication', 'log_injection', 'insecure_random'],
            'java': ['sql_injection', 'command_injection', 'weak_encryption', 'unsafe_deserialization',
                    'path_traversal', 'hardcoded_credentials', 'missing_access_control', 'privilege_escalation'],
            'php': ['sql_injection', 'xss', 'command_injection', 'file_upload_vulnerability', 'path_traversal',
                   'weak_encryption', 'hardcoded_credentials', 'log_injection'],
            'csharp': ['sql_injection', 'command_injection', 'weak_encryption', 'unsafe_deserialization',
                      'path_traversal', 'hardcoded_credentials', 'missing_access_control'],
            'go': ['sql_injection', 'command_injection', 'weak_encryption', 'path_traversal',
                  'hardcoded_credentials', 'insecure_random'],
            'ruby': ['sql_injection', 'command_injection', 'weak_encryption', 'unsafe_deserialization',
                    'path_traversal', 'hardcoded_credentials', 'log_injection']
        }

        applicable_patterns = language_patterns.get(language, [])
        return vuln_name in applicable_patterns or language == 'unknown'

    def _calculate_confidence(self, match, context: str, vuln_config: Dict) -> float:
        """Calculate confidence score for a vulnerability match"""
        confidence = 0.7  # Base confidence

        # Adjust based on context
        context_lower = context.lower()

        # Increase confidence for certain indicators
        if any(indicator in context_lower for indicator in ['user', 'input', 'request', 'param']):
            confidence += 0.1

        # Decrease confidence for test/example code
        if any(indicator in context_lower for indicator in ['test', 'example', 'demo', 'mock']):
            confidence -= 0.2

        # Adjust based on match quality
        match_text = match.group(0)
        if len(match_text) > 50:  # Longer matches are often more specific
            confidence += 0.1

        # Language-specific adjustments
        if 'sql' in vuln_config.get('description', '').lower():
            if any(keyword in context_lower for keyword in ['where', 'select', 'insert', 'update']):
                confidence += 0.15

        return max(min(confidence, 1.0), 0.1)

    def _filter_findings(self, findings: List[VulnerabilityMatch]) -> List[VulnerabilityMatch]:
        """Filter out duplicate and low-confidence findings"""
        filtered = []
        seen_locations = set()

        # Sort by confidence (highest first)
        sorted_findings = sorted(findings, key=lambda x: x.confidence, reverse=True)

        for finding in sorted_findings:
            # Create location key
            location_key = f"{finding.file_path}:{finding.line_number}:{finding.subtype}"

            # Skip if we've seen this location with this vulnerability type
            if location_key in seen_locations:
                continue

            # Skip very low confidence findings
            if finding.confidence < 0.3:
                continue

            seen_locations.add(location_key)
            filtered.append(finding)

        return filtered

    def get_vulnerability_statistics(self) -> Dict[str, Any]:
        """Get vulnerability scanning statistics"""
        return {
            'total_patterns': sum(len(vulns) for vulns in self.patterns.values()),
            'categories': list(self.patterns.keys()),
            'supported_languages': ['python', 'javascript', 'java', 'php', 'csharp', 'go', 'ruby'],
            'severity_levels': ['critical', 'high', 'medium', 'low']
        }
