"""
ByteGuardX Plugin Marketplace
Complete plugin distribution system with ratings, reviews, and automatic updates
"""

import logging
import asyncio
import json
import hashlib
import aiohttp
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timedelta
from pathlib import Path
import semver
import tempfile
import zipfile
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.asymmetric import ed25519

from byteguardx.security.trust_chain import Trust<PERSON>hainManager
from byteguardx.plugins.enhanced_plugin_manager import EnhancedPluginManager

logger = logging.getLogger(__name__)

@dataclass
class PluginListing:
    """Plugin marketplace listing"""
    id: str
    name: str
    version: str
    author: str
    description: str
    category: str
    tags: List[str]
    download_url: str
    homepage_url: str
    documentation_url: str
    source_code_url: str
    license: str
    price: float  # 0.0 for free plugins
    rating: float
    review_count: int
    download_count: int
    last_updated: datetime
    compatibility: Dict[str, str]  # version requirements
    screenshots: List[str]
    changelog: str
    security_scan_status: str  # 'passed', 'failed', 'pending'
    verification_status: str  # 'verified', 'unverified', 'community'
    file_size: int
    checksum: str
    signature: str
    dependencies: List[str]
    permissions: List[str]

@dataclass
class PluginReview:
    """Plugin review and rating"""
    id: str
    plugin_id: str
    user_id: str
    username: str
    rating: int  # 1-5 stars
    title: str
    content: str
    created_at: datetime
    updated_at: datetime
    helpful_votes: int
    verified_purchase: bool

@dataclass
class PluginCategory:
    """Plugin category definition"""
    id: str
    name: str
    description: str
    icon: str
    parent_category: Optional[str]
    plugin_count: int

class PluginMarketplace:
    """
    Complete plugin marketplace with distribution, ratings, and updates
    """
    
    def __init__(self, marketplace_url: str = "https://marketplace.byteguardx.com"):
        self.marketplace_url = marketplace_url
        self.cache_dir = Path("data/marketplace_cache")
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
        self.trust_manager = TrustChainManager()
        self.plugin_manager = None  # Will be injected
        
        # Marketplace configuration
        self.config = {
            'cache_ttl': 3600,  # 1 hour
            'max_concurrent_downloads': 5,
            'auto_update_check_interval': 86400,  # 24 hours
            'security_scan_required': True,
            'verified_publishers_only': False,
            'max_plugin_size': 100 * 1024 * 1024,  # 100MB
        }
        
        # Local cache
        self.plugin_cache: Dict[str, PluginListing] = {}
        self.category_cache: Dict[str, PluginCategory] = {}
        self.review_cache: Dict[str, List[PluginReview]] = {}
        
        logger.info("Plugin Marketplace initialized")
    
    async def initialize(self, plugin_manager: EnhancedPluginManager):
        """Initialize marketplace with plugin manager"""
        self.plugin_manager = plugin_manager
        await self.trust_manager.initialize()
        
        # Load cached data
        await self._load_cache()
        
        # Start background tasks
        asyncio.create_task(self._periodic_update_check())
        
        logger.info("Plugin Marketplace ready")
    
    async def search_plugins(self, 
                           query: str = "", 
                           category: str = "", 
                           tags: List[str] = None,
                           min_rating: float = 0.0,
                           free_only: bool = False,
                           verified_only: bool = False,
                           sort_by: str = "popularity",
                           page: int = 1,
                           per_page: int = 20) -> Dict[str, Any]:
        """Search plugins in marketplace"""
        try:
            params = {
                'q': query,
                'category': category,
                'tags': ','.join(tags) if tags else '',
                'min_rating': min_rating,
                'free_only': free_only,
                'verified_only': verified_only,
                'sort': sort_by,
                'page': page,
                'per_page': per_page
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.marketplace_url}/api/v1/plugins/search",
                    params=params
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        
                        # Cache results
                        for plugin_data in data.get('plugins', []):
                            plugin = PluginListing(**plugin_data)
                            self.plugin_cache[plugin.id] = plugin
                        
                        return data
                    else:
                        logger.error(f"Search failed: {response.status}")
                        return {'plugins': [], 'total': 0, 'pages': 0}
        
        except Exception as e:
            logger.error(f"Plugin search failed: {e}")
            return {'plugins': [], 'total': 0, 'pages': 0}
    
    async def get_plugin_details(self, plugin_id: str) -> Optional[PluginListing]:
        """Get detailed plugin information"""
        try:
            # Check cache first
            if plugin_id in self.plugin_cache:
                cached_plugin = self.plugin_cache[plugin_id]
                if self._is_cache_valid(cached_plugin.last_updated):
                    return cached_plugin
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.marketplace_url}/api/v1/plugins/{plugin_id}"
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        plugin = PluginListing(**data)
                        self.plugin_cache[plugin_id] = plugin
                        return plugin
                    else:
                        logger.error(f"Plugin details fetch failed: {response.status}")
                        return None
        
        except Exception as e:
            logger.error(f"Failed to get plugin details: {e}")
            return None
    
    async def install_plugin(self, plugin_id: str, version: str = "latest") -> Dict[str, Any]:
        """Install plugin from marketplace"""
        try:
            # Get plugin details
            plugin = await self.get_plugin_details(plugin_id)
            if not plugin:
                return {'success': False, 'error': 'Plugin not found'}
            
            # Security checks
            if self.config['verified_publishers_only'] and plugin.verification_status != 'verified':
                return {'success': False, 'error': 'Only verified plugins allowed'}
            
            if plugin.security_scan_status != 'passed' and self.config['security_scan_required']:
                return {'success': False, 'error': 'Plugin failed security scan'}
            
            # Download plugin
            download_result = await self._download_plugin(plugin)
            if not download_result['success']:
                return download_result
            
            plugin_path = download_result['path']
            
            # Verify signature
            if not await self._verify_plugin_signature(plugin_path, plugin.signature):
                return {'success': False, 'error': 'Plugin signature verification failed'}
            
            # Install via plugin manager
            if self.plugin_manager:
                install_result = await self.plugin_manager.install_plugin(
                    plugin_path, 
                    trusted=plugin.verification_status == 'verified'
                )
                
                if install_result:
                    # Record installation
                    await self._record_installation(plugin_id, version)
                    
                    # Update download count
                    await self._update_download_count(plugin_id)
                    
                    return {
                        'success': True,
                        'plugin_id': plugin_id,
                        'version': version,
                        'message': f'Plugin {plugin.name} installed successfully'
                    }
                else:
                    return {'success': False, 'error': 'Plugin installation failed'}
            else:
                return {'success': False, 'error': 'Plugin manager not available'}
        
        except Exception as e:
            logger.error(f"Plugin installation failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def uninstall_plugin(self, plugin_id: str) -> Dict[str, Any]:
        """Uninstall plugin"""
        try:
            if self.plugin_manager:
                success = await self.plugin_manager.uninstall_plugin(plugin_id)
                if success:
                    await self._record_uninstallation(plugin_id)
                    return {'success': True, 'message': f'Plugin {plugin_id} uninstalled'}
                else:
                    return {'success': False, 'error': 'Uninstallation failed'}
            else:
                return {'success': False, 'error': 'Plugin manager not available'}
        
        except Exception as e:
            logger.error(f"Plugin uninstallation failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def update_plugin(self, plugin_id: str) -> Dict[str, Any]:
        """Update plugin to latest version"""
        try:
            # Check if plugin is installed
            if not self.plugin_manager or not self.plugin_manager.is_plugin_installed(plugin_id):
                return {'success': False, 'error': 'Plugin not installed'}
            
            # Get current version
            current_version = self.plugin_manager.get_plugin_version(plugin_id)
            
            # Get latest version from marketplace
            plugin = await self.get_plugin_details(plugin_id)
            if not plugin:
                return {'success': False, 'error': 'Plugin not found in marketplace'}
            
            # Check if update is needed
            if semver.compare(plugin.version, current_version) <= 0:
                return {'success': True, 'message': 'Plugin is already up to date'}
            
            # Perform update
            uninstall_result = await self.uninstall_plugin(plugin_id)
            if not uninstall_result['success']:
                return uninstall_result
            
            install_result = await self.install_plugin(plugin_id, plugin.version)
            if install_result['success']:
                return {
                    'success': True,
                    'message': f'Plugin updated from {current_version} to {plugin.version}'
                }
            else:
                # Rollback - reinstall old version
                await self.install_plugin(plugin_id, current_version)
                return {'success': False, 'error': 'Update failed, rolled back to previous version'}
        
        except Exception as e:
            logger.error(f"Plugin update failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def check_for_updates(self) -> List[Dict[str, Any]]:
        """Check for plugin updates"""
        updates_available = []
        
        try:
            if not self.plugin_manager:
                return updates_available
            
            installed_plugins = self.plugin_manager.list_plugins()
            
            for plugin_id, plugin_info in installed_plugins.items():
                current_version = plugin_info.get('version', '0.0.0')
                
                # Get latest version from marketplace
                marketplace_plugin = await self.get_plugin_details(plugin_id)
                if marketplace_plugin and semver.compare(marketplace_plugin.version, current_version) > 0:
                    updates_available.append({
                        'plugin_id': plugin_id,
                        'name': marketplace_plugin.name,
                        'current_version': current_version,
                        'latest_version': marketplace_plugin.version,
                        'changelog': marketplace_plugin.changelog
                    })
            
            return updates_available
        
        except Exception as e:
            logger.error(f"Update check failed: {e}")
            return updates_available
    
    async def get_plugin_reviews(self, plugin_id: str, page: int = 1, per_page: int = 10) -> List[PluginReview]:
        """Get plugin reviews and ratings"""
        try:
            # Check cache first
            cache_key = f"{plugin_id}_{page}_{per_page}"
            if cache_key in self.review_cache:
                return self.review_cache[cache_key]
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    f"{self.marketplace_url}/api/v1/plugins/{plugin_id}/reviews",
                    params={'page': page, 'per_page': per_page}
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        reviews = [PluginReview(**review_data) for review_data in data.get('reviews', [])]
                        self.review_cache[cache_key] = reviews
                        return reviews
                    else:
                        logger.error(f"Reviews fetch failed: {response.status}")
                        return []
        
        except Exception as e:
            logger.error(f"Failed to get plugin reviews: {e}")
            return []
    
    async def submit_review(self, plugin_id: str, rating: int, title: str, content: str) -> Dict[str, Any]:
        """Submit plugin review"""
        try:
            review_data = {
                'plugin_id': plugin_id,
                'rating': rating,
                'title': title,
                'content': content
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{self.marketplace_url}/api/v1/plugins/{plugin_id}/reviews",
                    json=review_data
                ) as response:
                    if response.status == 201:
                        # Clear review cache
                        self.review_cache.clear()
                        return {'success': True, 'message': 'Review submitted successfully'}
                    else:
                        return {'success': False, 'error': 'Review submission failed'}
        
        except Exception as e:
            logger.error(f"Review submission failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def get_categories(self) -> List[PluginCategory]:
        """Get plugin categories"""
        try:
            if self.category_cache:
                return list(self.category_cache.values())
            
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.marketplace_url}/api/v1/categories") as response:
                    if response.status == 200:
                        data = await response.json()
                        categories = [PluginCategory(**cat_data) for cat_data in data.get('categories', [])]
                        self.category_cache = {cat.id: cat for cat in categories}
                        return categories
                    else:
                        logger.error(f"Categories fetch failed: {response.status}")
                        return []
        
        except Exception as e:
            logger.error(f"Failed to get categories: {e}")
            return []
    
    async def _download_plugin(self, plugin: PluginListing) -> Dict[str, Any]:
        """Download plugin file"""
        try:
            if plugin.file_size > self.config['max_plugin_size']:
                return {'success': False, 'error': 'Plugin file too large'}
            
            async with aiohttp.ClientSession() as session:
                async with session.get(plugin.download_url) as response:
                    if response.status == 200:
                        # Create temporary file
                        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.zip')
                        
                        # Download with progress tracking
                        downloaded = 0
                        async for chunk in response.content.iter_chunked(8192):
                            temp_file.write(chunk)
                            downloaded += len(chunk)
                        
                        temp_file.close()
                        
                        # Verify checksum
                        if not await self._verify_checksum(temp_file.name, plugin.checksum):
                            return {'success': False, 'error': 'Checksum verification failed'}
                        
                        return {'success': True, 'path': Path(temp_file.name)}
                    else:
                        return {'success': False, 'error': f'Download failed: {response.status}'}
        
        except Exception as e:
            logger.error(f"Plugin download failed: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _verify_checksum(self, file_path: str, expected_checksum: str) -> bool:
        """Verify file checksum"""
        try:
            hasher = hashlib.sha256()
            with open(file_path, 'rb') as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hasher.update(chunk)
            
            actual_checksum = hasher.hexdigest()
            return actual_checksum == expected_checksum
        
        except Exception as e:
            logger.error(f"Checksum verification failed: {e}")
            return False
    
    async def _verify_plugin_signature(self, plugin_path: Path, signature: str) -> bool:
        """Verify plugin digital signature"""
        try:
            return await self.trust_manager.verify_plugin_signature(plugin_path, signature)
        except Exception as e:
            logger.error(f"Signature verification failed: {e}")
            return False
    
    async def _record_installation(self, plugin_id: str, version: str):
        """Record plugin installation"""
        try:
            installation_data = {
                'plugin_id': plugin_id,
                'version': version,
                'installed_at': datetime.now().isoformat()
            }
            
            # Store in local database or send to analytics
            logger.info(f"Plugin installed: {plugin_id} v{version}")
        
        except Exception as e:
            logger.error(f"Failed to record installation: {e}")
    
    async def _record_uninstallation(self, plugin_id: str):
        """Record plugin uninstallation"""
        try:
            logger.info(f"Plugin uninstalled: {plugin_id}")
        except Exception as e:
            logger.error(f"Failed to record uninstallation: {e}")
    
    async def _update_download_count(self, plugin_id: str):
        """Update plugin download count"""
        try:
            async with aiohttp.ClientSession() as session:
                await session.post(f"{self.marketplace_url}/api/v1/plugins/{plugin_id}/download")
        except Exception as e:
            logger.error(f"Failed to update download count: {e}")
    
    def _is_cache_valid(self, last_updated: datetime) -> bool:
        """Check if cache entry is still valid"""
        return (datetime.now() - last_updated).total_seconds() < self.config['cache_ttl']
    
    async def _load_cache(self):
        """Load cached marketplace data"""
        try:
            cache_file = self.cache_dir / "marketplace_cache.json"
            if cache_file.exists():
                with open(cache_file, 'r') as f:
                    cache_data = json.load(f)
                
                # Load plugin cache
                for plugin_data in cache_data.get('plugins', []):
                    plugin = PluginListing(**plugin_data)
                    self.plugin_cache[plugin.id] = plugin
                
                # Load category cache
                for cat_data in cache_data.get('categories', []):
                    category = PluginCategory(**cat_data)
                    self.category_cache[category.id] = category
        
        except Exception as e:
            logger.error(f"Failed to load cache: {e}")
    
    async def _save_cache(self):
        """Save marketplace data to cache"""
        try:
            cache_data = {
                'plugins': [asdict(plugin) for plugin in self.plugin_cache.values()],
                'categories': [asdict(category) for category in self.category_cache.values()],
                'last_updated': datetime.now().isoformat()
            }
            
            cache_file = self.cache_dir / "marketplace_cache.json"
            with open(cache_file, 'w') as f:
                json.dump(cache_data, f, indent=2, default=str)
        
        except Exception as e:
            logger.error(f"Failed to save cache: {e}")
    
    async def _periodic_update_check(self):
        """Periodic background update checking"""
        while True:
            try:
                await asyncio.sleep(self.config['auto_update_check_interval'])
                
                updates = await self.check_for_updates()
                if updates:
                    logger.info(f"Found {len(updates)} plugin updates available")
                    
                    # Notify about available updates
                    for update in updates:
                        logger.info(f"Update available: {update['name']} {update['current_version']} -> {update['latest_version']}")
                
                # Save cache periodically
                await self._save_cache()
                
            except Exception as e:
                logger.error(f"Periodic update check failed: {e}")

# Global marketplace instance
marketplace = PluginMarketplace()
