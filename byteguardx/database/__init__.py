"""
Database package for ByteGuardX
Provides SQLAlchemy-based data persistence layer
"""

from .models import (
    db, ScanResult, Finding, UserFeedback, Pattern,
    User, Organization, AuditLog, ScheduledScan
)
from .connection_pool import DatabaseManager
from .migrations import run_migrations

__all__ = [
    'db', 'ScanResult', 'Finding', 'UserFeedback', 'Pattern',
    'User', 'Organization', 'AuditLog', 'ScheduledScan', 'DatabaseManager', 'run_migrations'
]
