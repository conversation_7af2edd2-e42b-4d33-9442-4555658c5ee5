"""
Enhanced Plugin Manager for ByteGuardX
Implements complete plugin lifecycle with sandboxing, trust verification, and IPC
"""

import logging
import json
import toml
import asyncio
import tempfile
import shutil
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from pathlib import Path
import hashlib

logger = logging.getLogger(__name__)

@dataclass
class PluginManifest:
    """Complete plugin manifest structure"""
    # Metadata
    name: str
    version: str
    description: str
    author: str
    license: str
    homepage: str
    
    # Build info
    content_hash: str
    build_timestamp: str
    reproducible_build: bool
    build_environment: str
    
    # Security
    signing_key_id: str
    signature: str
    trusted: bool
    security_level: str
    
    # Capabilities
    filesystem: List[str] = field(default_factory=list)
    network: List[str] = field(default_factory=list)
    models: List[str] = field(default_factory=list)
    memory_limit_mb: int = 512
    cpu_limit_percent: int = 25
    execution_timeout_seconds: int = 300
    
    # Runtime
    entrypoint: str = "src/main.py"
    python_version: str = ">=3.9,<4.0"
    dependencies: List[str] = field(default_factory=list)
    
    # API
    input_schema: str = "schemas/input.json"
    output_schema: str = "schemas/output.json"
    ipc_version: str = "1.2"
    
    # Models
    required_models: List[Dict[str, Any]] = field(default_factory=list)

@dataclass
class PluginInfo:
    """Runtime plugin information"""
    manifest: PluginManifest
    plugin_path: Path
    is_loaded: bool = False
    is_trusted: bool = False
    sandbox_context: Optional[Any] = None
    ipc_handler: Optional[Any] = None

class EnhancedPluginManager:
    """
    Enhanced plugin management with complete trust verification and sandboxing
    """
    
    def __init__(self, plugins_dir: str = "plugins"):
        self.plugins_dir = Path(plugins_dir)
        self.plugins_dir.mkdir(parents=True, exist_ok=True)
        
        self.loaded_plugins: Dict[str, PluginInfo] = {}
        self.plugin_registry: Dict[str, PluginInfo] = {}
        self.trust_manager = None
        self.is_initialized = False
        
        logger.info("Enhanced Plugin Manager initialized")
    
    async def initialize(self, trust_manager):
        """Initialize with trust manager"""
        self.trust_manager = trust_manager
        await self._discover_plugins()
        await self._load_trusted_plugins()
        self.is_initialized = True
        logger.info(f"Enhanced Plugin Manager ready with {len(self.loaded_plugins)} plugins")
    
    async def _discover_plugins(self):
        """Discover all plugins with TOML manifests"""
        for plugin_dir in self.plugins_dir.iterdir():
            if plugin_dir.is_dir():
                manifest_path = plugin_dir / "plugin.toml"
                if manifest_path.exists():
                    try:
                        manifest = self._load_plugin_manifest(manifest_path)
                        plugin_info = PluginInfo(
                            manifest=manifest,
                            plugin_path=plugin_dir
                        )
                        self.plugin_registry[manifest.name] = plugin_info
                        logger.info(f"Discovered plugin: {manifest.name} v{manifest.version}")
                    except Exception as e:
                        logger.error(f"Failed to load plugin manifest from {manifest_path}: {e}")
    
    def _load_plugin_manifest(self, manifest_path: Path) -> PluginManifest:
        """Load and parse TOML plugin manifest"""
        try:
            manifest_data = toml.load(manifest_path)
            
            # Extract nested sections
            metadata = manifest_data.get('metadata', {})
            build = manifest_data.get('build', {})
            security = manifest_data.get('security', {})
            capabilities = manifest_data.get('capabilities', {})
            runtime = manifest_data.get('runtime', {})
            api = manifest_data.get('api', {})
            models = manifest_data.get('models', {})
            
            return PluginManifest(
                # Metadata
                name=metadata.get('name', ''),
                version=metadata.get('version', ''),
                description=metadata.get('description', ''),
                author=metadata.get('author', ''),
                license=metadata.get('license', ''),
                homepage=metadata.get('homepage', ''),
                
                # Build
                content_hash=build.get('content_hash', ''),
                build_timestamp=build.get('build_timestamp', ''),
                reproducible_build=build.get('reproducible_build', False),
                build_environment=build.get('build_environment', ''),
                
                # Security
                signing_key_id=security.get('signing_key_id', ''),
                signature=security.get('signature', ''),
                trusted=security.get('trusted', False),
                security_level=security.get('security_level', 'medium'),
                
                # Capabilities
                filesystem=capabilities.get('filesystem', []),
                network=capabilities.get('network', ['none']),
                models=capabilities.get('models', []),
                memory_limit_mb=capabilities.get('memory_limit_mb', 512),
                cpu_limit_percent=capabilities.get('cpu_limit_percent', 25),
                execution_timeout_seconds=capabilities.get('execution_timeout_seconds', 300),
                
                # Runtime
                entrypoint=runtime.get('entrypoint', 'src/main.py'),
                python_version=runtime.get('python_version', '>=3.9,<4.0'),
                dependencies=runtime.get('dependencies', []),
                
                # API
                input_schema=api.get('input_schema', 'schemas/input.json'),
                output_schema=api.get('output_schema', 'schemas/output.json'),
                ipc_version=api.get('ipc_version', '1.2'),
                
                # Models
                required_models=models.get('required_models', [])
            )
            
        except Exception as e:
            raise ValueError(f"Invalid plugin manifest: {e}")
    
    async def _load_trusted_plugins(self):
        """Load only trusted and verified plugins"""
        for plugin_name, plugin_info in self.plugin_registry.items():
            try:
                if await self._verify_plugin_trust(plugin_info):
                    await self._load_plugin(plugin_info)
                    plugin_info.is_trusted = True
                    self.loaded_plugins[plugin_name] = plugin_info
                    logger.info(f"Loaded trusted plugin: {plugin_name}")
                else:
                    logger.warning(f"Plugin {plugin_name} failed trust verification")
            except Exception as e:
                logger.error(f"Failed to load plugin {plugin_name}: {e}")
    
    async def _verify_plugin_trust(self, plugin_info: PluginInfo) -> bool:
        """Verify plugin signature and trust chain"""
        if not self.trust_manager:
            return False
        
        try:
            # Verify plugin signature
            signature_valid = await self.trust_manager.verify_plugin_signature(
                plugin_info.plugin_path,
                plugin_info.manifest
            )
            
            if not signature_valid:
                return False
            
            # Verify trust chain
            trust_chain_valid = await self.trust_manager.verify_trust_chain(
                plugin_info.manifest
            )
            
            return trust_chain_valid
            
        except Exception as e:
            logger.error(f"Trust verification failed for {plugin_info.manifest.name}: {e}")
            return False
    
    async def _load_plugin(self, plugin_info: PluginInfo):
        """Load plugin and setup sandbox environment"""
        # Validate dependencies
        await self._validate_plugin_dependencies(plugin_info)
        
        # Setup sandbox (placeholder - would integrate with actual sandbox)
        plugin_info.sandbox_context = f"sandbox_{plugin_info.manifest.name}"
        
        # Setup IPC handler (placeholder)
        plugin_info.ipc_handler = f"ipc_{plugin_info.manifest.name}"
        
        plugin_info.is_loaded = True
    
    async def _validate_plugin_dependencies(self, plugin_info: PluginInfo):
        """Validate plugin dependencies and models"""
        # Check required models
        for model_info in plugin_info.manifest.required_models:
            model_name = model_info.get('name')
            model_hash = model_info.get('hash')
            
            # Verify model exists and hash matches
            model_path = Path(f"models/{model_name}")
            if not model_path.exists():
                raise ValueError(f"Required model not found: {model_name}")
            
            # Verify model hash
            actual_hash = self._calculate_file_hash(model_path)
            expected_hash = model_hash.split(':')[1] if ':' in model_hash else model_hash
            
            if actual_hash != expected_hash:
                raise ValueError(f"Model hash mismatch for {model_name}")
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA-256 hash of file"""
        sha256_hash = hashlib.sha256()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                sha256_hash.update(chunk)
        return sha256_hash.hexdigest()
    
    async def get_active_plugins(self) -> List[PluginInfo]:
        """Get list of active, trusted plugins"""
        return [plugin for plugin in self.loaded_plugins.values() if plugin.is_loaded and plugin.is_trusted]
    
    async def create_sandbox(self, plugin_info: PluginInfo):
        """Create sandbox context for plugin execution"""
        if not plugin_info.sandbox_context:
            raise ValueError(f"No sandbox context for plugin {plugin_info.manifest.name}")
        
        return plugin_info.sandbox_context
    
    async def install_plugin(self, plugin_path: Path, trusted: bool = False) -> PluginInfo:
        """Install new plugin with trust verification"""
        try:
            # Extract plugin if it's an archive
            if plugin_path.suffix in ['.zip', '.tar.gz']:
                plugin_dir = await self._extract_plugin_archive(plugin_path)
            else:
                plugin_dir = plugin_path
            
            # Load manifest
            manifest_path = plugin_dir / "plugin.toml"
            if not manifest_path.exists():
                raise ValueError("Plugin manifest not found")
            
            manifest = self._load_plugin_manifest(manifest_path)
            
            # Create plugin info
            plugin_info = PluginInfo(
                manifest=manifest,
                plugin_path=plugin_dir
            )
            
            # Verify trust if required
            if not trusted:
                if not await self._verify_plugin_trust(plugin_info):
                    raise ValueError("Plugin trust verification failed")
            
            # Copy to plugins directory
            target_dir = self.plugins_dir / manifest.name
            if target_dir.exists():
                shutil.rmtree(target_dir)
            
            shutil.copytree(plugin_dir, target_dir)
            plugin_info.plugin_path = target_dir
            
            # Load plugin
            await self._load_plugin(plugin_info)
            plugin_info.is_trusted = True
            
            # Register plugin
            self.plugin_registry[manifest.name] = plugin_info
            self.loaded_plugins[manifest.name] = plugin_info
            
            logger.info(f"Successfully installed plugin: {manifest.name} v{manifest.version}")
            return plugin_info
            
        except Exception as e:
            logger.error(f"Plugin installation failed: {e}")
            raise
    
    async def _extract_plugin_archive(self, archive_path: Path) -> Path:
        """Extract plugin archive to temporary directory"""
        temp_dir = Path(tempfile.mkdtemp())
        
        if archive_path.suffix == '.zip':
            import zipfile
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                zip_ref.extractall(temp_dir)
        elif archive_path.suffix in ['.tar.gz', '.tgz']:
            import tarfile
            with tarfile.open(archive_path, 'r:gz') as tar_ref:
                tar_ref.extractall(temp_dir)
        else:
            raise ValueError(f"Unsupported archive format: {archive_path.suffix}")
        
        return temp_dir
    
    def list_plugins(self) -> Dict[str, Dict[str, Any]]:
        """List all plugins with their status"""
        plugin_list = {}
        
        for name, plugin_info in self.plugin_registry.items():
            plugin_list[name] = {
                'name': plugin_info.manifest.name,
                'version': plugin_info.manifest.version,
                'description': plugin_info.manifest.description,
                'author': plugin_info.manifest.author,
                'is_loaded': plugin_info.is_loaded,
                'is_trusted': plugin_info.is_trusted,
                'security_level': plugin_info.manifest.security_level,
                'capabilities': {
                    'filesystem': plugin_info.manifest.filesystem,
                    'network': plugin_info.manifest.network,
                    'models': plugin_info.manifest.models
                }
            }
        
        return plugin_list
