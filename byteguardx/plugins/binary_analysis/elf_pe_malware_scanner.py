"""
ELF & PE Malware Pattern Scanner Plugin
Detects malware patterns in ELF and PE binary files using static analysis and YARA rules
"""

import re
import struct
import hashlib
import logging
from typing import Dict, List, Any, Optional
from ..plugin_framework import BasePlugin, PluginManifest, PluginCategory

logger = logging.getLogger(__name__)

class ELFPEMalwareScanner(BasePlugin):
    """Scanner for malware patterns in ELF and PE binary files"""
    
    def __init__(self):
        manifest = PluginManifest(
            name="elf_pe_malware_scanner",
            version="1.0.0",
            author="ByteGuardX Security Team",
            description="Detects malware patterns in ELF and PE binaries using static analysis and YARA-like rules",
            category=PluginCategory.BINARY_ANALYSIS,
            supported_languages=["binary"],
            supported_file_types=[".exe", ".dll", ".so", ".elf", ".bin"],
            requires_network=False,
            requires_filesystem=False,
            max_memory_mb=512,
            max_cpu_percent=50,
            timeout_seconds=120,
            trust_level="high",
            dependencies=["yara-python"],
            api_version="1.0"
        )
        super().__init__(manifest)
        
        self.malware_signatures = self._load_malware_signatures()
        self.pe_patterns = self._load_pe_patterns()
        self.elf_patterns = self._load_elf_patterns()
        self.suspicious_strings = self._load_suspicious_strings()
    
    def _load_malware_signatures(self) -> Dict[str, Any]:
        """Load malware signature patterns"""
        return {
            "packer_signatures": {
                "upx": {
                    "signature": b"UPX!",
                    "description": "UPX packer detected",
                    "severity": "medium",
                    "family": "packer"
                },
                "aspack": {
                    "signature": b"aPLib",
                    "description": "ASPack packer detected",
                    "severity": "medium",
                    "family": "packer"
                },
                "themida": {
                    "signature": b"Themida",
                    "description": "Themida protector detected",
                    "severity": "high",
                    "family": "protector"
                }
            },
            "crypto_signatures": {
                "cryptoapi": {
                    "strings": [b"CryptAcquireContext", b"CryptCreateHash", b"CryptEncrypt"],
                    "description": "Windows Crypto API usage detected",
                    "severity": "low",
                    "family": "crypto"
                },
                "custom_crypto": {
                    "patterns": [
                        rb"[\x00-\xFF]{16,32}",  # Potential encryption keys
                        rb"AES|DES|RC4|Blowfish",
                        rb"encrypt|decrypt|cipher"
                    ],
                    "description": "Custom cryptographic implementation",
                    "severity": "medium",
                    "family": "crypto"
                }
            },
            "network_signatures": {
                "c2_communication": {
                    "strings": [
                        b"POST /gate.php",
                        b"User-Agent: Mozilla/4.0",
                        b"cmd.exe /c",
                        b"powershell.exe -enc"
                    ],
                    "description": "Command and control communication patterns",
                    "severity": "high",
                    "family": "c2"
                },
                "download_execute": {
                    "strings": [
                        b"URLDownloadToFile",
                        b"WinExec",
                        b"CreateProcess",
                        b"ShellExecute"
                    ],
                    "description": "Download and execute functionality",
                    "severity": "high",
                    "family": "downloader"
                }
            },
            "persistence_signatures": {
                "registry_persistence": {
                    "strings": [
                        b"SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\Run",
                        b"HKEY_LOCAL_MACHINE\\SOFTWARE",
                        b"RegSetValueEx",
                        b"RegCreateKeyEx"
                    ],
                    "description": "Registry persistence mechanism",
                    "severity": "medium",
                    "family": "persistence"
                },
                "service_persistence": {
                    "strings": [
                        b"CreateService",
                        b"StartService",
                        b"OpenSCManager",
                        b"ControlService"
                    ],
                    "description": "Windows service persistence",
                    "severity": "medium",
                    "family": "persistence"
                }
            },
            "evasion_signatures": {
                "anti_debug": {
                    "strings": [
                        b"IsDebuggerPresent",
                        b"CheckRemoteDebuggerPresent",
                        b"OutputDebugString",
                        b"FindWindow"
                    ],
                    "description": "Anti-debugging techniques",
                    "severity": "medium",
                    "family": "evasion"
                },
                "anti_vm": {
                    "strings": [
                        b"VMware",
                        b"VirtualBox",
                        b"QEMU",
                        b"Xen",
                        b"vbox",
                        b"vmtoolsd"
                    ],
                    "description": "Anti-virtualization techniques",
                    "severity": "medium",
                    "family": "evasion"
                }
            }
        }
    
    def _load_pe_patterns(self) -> Dict[str, Any]:
        """Load PE-specific malware patterns"""
        return {
            "suspicious_sections": {
                "patterns": [
                    rb"\.rsrc\x00\x00\x00[\x00-\xFF]{4}[\x00-\xFF]{4}\x40",  # Suspicious resource section
                    rb"\.data\x00\x00\x00[\x00-\xFF]{4}[\x00-\xFF]{4}\xC0",  # Executable data section
                    rb"\.text\x00\x00\x00[\x00-\xFF]{4}[\x00-\xFF]{4}\x20"   # Non-executable text section
                ],
                "description": "Suspicious PE section characteristics",
                "severity": "medium"
            },
            "import_anomalies": {
                "suspicious_imports": [
                    b"VirtualAlloc",
                    b"VirtualProtect",
                    b"WriteProcessMemory",
                    b"CreateRemoteThread",
                    b"SetWindowsHookEx",
                    b"GetProcAddress",
                    b"LoadLibrary"
                ],
                "description": "Suspicious API imports for code injection",
                "severity": "high"
            },
            "overlay_data": {
                "description": "PE overlay data detected (potential payload)",
                "severity": "medium"
            }
        }
    
    def _load_elf_patterns(self) -> Dict[str, Any]:
        """Load ELF-specific malware patterns"""
        return {
            "suspicious_sections": {
                "patterns": [
                    rb"\.init_array",
                    rb"\.fini_array",
                    rb"\.ctors",
                    rb"\.dtors"
                ],
                "description": "Suspicious ELF constructor/destructor sections",
                "severity": "medium"
            },
            "rootkit_indicators": {
                "strings": [
                    b"/proc/net/tcp",
                    b"/proc/modules",
                    b"sys_call_table",
                    b"hijack",
                    b"rootkit"
                ],
                "description": "Linux rootkit indicators",
                "severity": "high"
            },
            "privilege_escalation": {
                "strings": [
                    b"setuid",
                    b"setgid",
                    b"chmod 4755",
                    b"sudo",
                    b"/etc/passwd",
                    b"/etc/shadow"
                ],
                "description": "Privilege escalation indicators",
                "severity": "high"
            }
        }
    
    def _load_suspicious_strings(self) -> List[bytes]:
        """Load suspicious strings common in malware"""
        return [
            # Network indicators
            b"bot",
            b"ddos",
            b"flood",
            b"scan",
            b"exploit",
            
            # File operations
            b"delete",
            b"wipe",
            b"format",
            b"destroy",
            
            # System manipulation
            b"disable",
            b"kill",
            b"terminate",
            b"inject",
            
            # Obfuscation
            b"decode",
            b"decrypt",
            b"unpack",
            b"deobfuscate"
        ]
    
    def validate_input(self, content: str, file_path: str) -> bool:
        """Validate input for binary scanning"""
        if not content:
            return False
        
        # Check if it's a binary file
        try:
            binary_content = content.encode('latin-1') if isinstance(content, str) else content
            
            # Check for PE signature
            if len(binary_content) > 64:
                if binary_content[:2] == b'MZ':  # DOS header
                    return True
                
                # Check for ELF signature
                if binary_content[:4] == b'\x7fELF':
                    return True
            
            # Check file extension
            binary_extensions = ['.exe', '.dll', '.so', '.elf', '.bin']
            return any(file_path.lower().endswith(ext) for ext in binary_extensions)
            
        except Exception as e:
            logger.error(f"Binary validation error: {e}")
            return False
    
    def scan(self, content: str, file_path: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Scan binary content for malware patterns"""
        findings = []
        
        try:
            # Convert content to bytes
            binary_content = content.encode('latin-1') if isinstance(content, str) else content
            
            # Determine binary type
            binary_type = self._identify_binary_type(binary_content)
            
            # Perform signature-based detection
            findings.extend(self._scan_signatures(binary_content, file_path, binary_type))
            
            # Perform structural analysis
            if binary_type == "PE":
                findings.extend(self._analyze_pe_structure(binary_content, file_path))
            elif binary_type == "ELF":
                findings.extend(self._analyze_elf_structure(binary_content, file_path))
            
            # Perform string analysis
            findings.extend(self._analyze_strings(binary_content, file_path))
            
            # Calculate file hash for reputation checking
            file_hash = hashlib.sha256(binary_content).hexdigest()
            
            # Add metadata to all findings
            for finding in findings:
                finding["detection_metadata"].update({
                    "binary_type": binary_type,
                    "file_hash_sha256": file_hash,
                    "file_size": len(binary_content)
                })
            
        except Exception as e:
            logger.error(f"Binary scanning error: {e}")
            findings.append({
                "title": "Binary Scanning Error",
                "description": f"Failed to scan binary file: {str(e)}",
                "severity": "low",
                "confidence": 0.5,
                "file_path": file_path,
                "line_number": 0,
                "scanner_name": self.manifest.name,
                "detection_metadata": {"error": str(e)}
            })
        
        return findings
    
    def _identify_binary_type(self, binary_content: bytes) -> str:
        """Identify binary file type"""
        if len(binary_content) < 4:
            return "Unknown"
        
        # Check for PE signature
        if binary_content[:2] == b'MZ':
            return "PE"
        
        # Check for ELF signature
        if binary_content[:4] == b'\x7fELF':
            return "ELF"
        
        return "Unknown"
    
    def _scan_signatures(self, binary_content: bytes, file_path: str, binary_type: str) -> List[Dict[str, Any]]:
        """Scan for malware signatures"""
        findings = []
        
        for category, signatures in self.malware_signatures.items():
            for sig_name, sig_data in signatures.items():
                # Check byte signatures
                if "signature" in sig_data:
                    if sig_data["signature"] in binary_content:
                        findings.append(self._create_finding(
                            title=f"Malware Signature: {sig_data['description']}",
                            description=sig_data["description"],
                            severity=sig_data["severity"],
                            confidence=0.9,
                            file_path=file_path,
                            detection_type="signature",
                            malware_family=sig_data.get("family", "unknown"),
                            signature_name=sig_name
                        ))
                
                # Check string patterns
                if "strings" in sig_data:
                    for string_pattern in sig_data["strings"]:
                        if string_pattern in binary_content:
                            findings.append(self._create_finding(
                                title=f"Suspicious Pattern: {sig_data['description']}",
                                description=sig_data["description"],
                                severity=sig_data["severity"],
                                confidence=0.7,
                                file_path=file_path,
                                detection_type="string_pattern",
                                malware_family=sig_data.get("family", "unknown"),
                                pattern=string_pattern.decode('utf-8', errors='ignore')
                            ))
        
        return findings
    
    def _analyze_pe_structure(self, binary_content: bytes, file_path: str) -> List[Dict[str, Any]]:
        """Analyze PE file structure for anomalies"""
        findings = []
        
        try:
            # Basic PE header analysis
            if len(binary_content) < 64:
                return findings
            
            # Check DOS header
            dos_header = struct.unpack('<H', binary_content[:2])[0]
            if dos_header != 0x5A4D:  # 'MZ'
                return findings
            
            # Get PE header offset
            pe_offset = struct.unpack('<L', binary_content[60:64])[0]
            
            if pe_offset + 4 >= len(binary_content):
                return findings
            
            # Check PE signature
            pe_signature = binary_content[pe_offset:pe_offset+4]
            if pe_signature != b'PE\x00\x00':
                findings.append(self._create_finding(
                    title="Invalid PE Signature",
                    description="PE file has invalid signature",
                    severity="medium",
                    confidence=0.8,
                    file_path=file_path,
                    detection_type="structural_anomaly"
                ))
            
            # Check for overlay data
            if self._has_overlay_data(binary_content, pe_offset):
                findings.append(self._create_finding(
                    title="PE Overlay Data Detected",
                    description="PE file contains overlay data (potential hidden payload)",
                    severity="medium",
                    confidence=0.6,
                    file_path=file_path,
                    detection_type="overlay_data"
                ))
            
        except Exception as e:
            logger.error(f"PE analysis error: {e}")
        
        return findings
    
    def _analyze_elf_structure(self, binary_content: bytes, file_path: str) -> List[Dict[str, Any]]:
        """Analyze ELF file structure for anomalies"""
        findings = []
        
        try:
            if len(binary_content) < 16:
                return findings
            
            # Check ELF header
            elf_header = binary_content[:16]
            
            # Validate ELF magic
            if elf_header[:4] != b'\x7fELF':
                return findings
            
            # Check class (32/64 bit)
            elf_class = elf_header[4]
            if elf_class not in [1, 2]:  # 1=32bit, 2=64bit
                findings.append(self._create_finding(
                    title="Invalid ELF Class",
                    description="ELF file has invalid class field",
                    severity="medium",
                    confidence=0.8,
                    file_path=file_path,
                    detection_type="structural_anomaly"
                ))
            
            # Check for suspicious sections
            for pattern in self.elf_patterns["suspicious_sections"]["patterns"]:
                if pattern in binary_content:
                    findings.append(self._create_finding(
                        title="Suspicious ELF Section",
                        description=self.elf_patterns["suspicious_sections"]["description"],
                        severity=self.elf_patterns["suspicious_sections"]["severity"],
                        confidence=0.7,
                        file_path=file_path,
                        detection_type="suspicious_section"
                    ))
            
        except Exception as e:
            logger.error(f"ELF analysis error: {e}")
        
        return findings
    
    def _analyze_strings(self, binary_content: bytes, file_path: str) -> List[Dict[str, Any]]:
        """Analyze strings in binary for suspicious content"""
        findings = []
        
        try:
            # Extract printable strings
            strings = self._extract_strings(binary_content)
            
            # Check against suspicious string patterns
            for string in strings:
                string_bytes = string.encode('utf-8', errors='ignore')
                
                for suspicious_string in self.suspicious_strings:
                    if suspicious_string in string_bytes:
                        findings.append(self._create_finding(
                            title=f"Suspicious String: {string}",
                            description=f"Suspicious string pattern detected: {string}",
                            severity="low",
                            confidence=0.5,
                            file_path=file_path,
                            detection_type="suspicious_string",
                            suspicious_string=string
                        ))
            
        except Exception as e:
            logger.error(f"String analysis error: {e}")
        
        return findings
    
    def _extract_strings(self, binary_content: bytes, min_length: int = 4) -> List[str]:
        """Extract printable strings from binary content"""
        strings = []
        current_string = ""
        
        for byte in binary_content:
            if 32 <= byte <= 126:  # Printable ASCII
                current_string += chr(byte)
            else:
                if len(current_string) >= min_length:
                    strings.append(current_string)
                current_string = ""
        
        # Don't forget the last string
        if len(current_string) >= min_length:
            strings.append(current_string)
        
        return strings[:1000]  # Limit to first 1000 strings
    
    def _has_overlay_data(self, binary_content: bytes, pe_offset: int) -> bool:
        """Check if PE file has overlay data"""
        try:
            # This is a simplified check - real implementation would parse PE headers
            # to determine the actual end of the PE file
            return len(binary_content) > pe_offset + 1024  # Simplified heuristic
        except Exception:
            return False
    
    def _create_finding(self, title: str, description: str, severity: str, 
                       confidence: float, file_path: str, detection_type: str, **kwargs) -> Dict[str, Any]:
        """Create a standardized finding"""
        finding = {
            "title": title,
            "description": description,
            "severity": severity,
            "confidence": confidence,
            "file_path": file_path,
            "line_number": 0,  # Not applicable for binary files
            "column_start": 0,
            "column_end": 0,
            "context": "Binary file analysis",
            "scanner_name": self.manifest.name,
            "cwe_id": "CWE-506",  # Embedded Malicious Code
            "owasp_category": "A06:2021 – Vulnerable and Outdated Components",
            "remediation": "Quarantine file and perform detailed malware analysis",
            "detection_metadata": {
                "detection_type": detection_type,
                **kwargs
            }
        }
        
        return finding
