"""
Archive Exploit Scanner Plugin
Detects malicious content in ZIP/JAR/TAR archives
"""

import re
import logging
from typing import Dict, List, Any
from ..plugin_framework import BasePlugin, PluginManifest, PluginCategory

logger = logging.getLogger(__name__)

class ArchiveExploitScanner(BasePlugin):
    """Scanner for archive-based exploits"""
    
    def __init__(self):
        manifest = PluginManifest(
            name="archive_exploit_scanner",
            version="1.0.0",
            author="ByteGuardX Security Team",
            description="Detects malicious content in ZIP, JAR, TAR and other archive files",
            category=PluginCategory.BINARY_ANALYSIS,
            supported_languages=["binary"],
            supported_file_types=[".zip", ".jar", ".tar", ".gz", ".rar"],
            requires_network=False,
            requires_filesystem=False,
            max_memory_mb=512,
            max_cpu_percent=50,
            timeout_seconds=120,
            trust_level="high",
            dependencies=[],
            api_version="1.0"
        )
        super().__init__(manifest)
        
        self.archive_patterns = {
            "zip_bomb": {
                "patterns": [
                    rb'\x00{1000,}',  # Large sequences of null bytes
                    rb'(.)\1{10000,}'  # Repeated characters (compression bomb)
                ],
                "description": "Potential ZIP bomb detected",
                "severity": "high"
            },
            "path_traversal": {
                "patterns": [
                    rb'\.\./.*\.\.',
                    rb'\.\.\\.*\.\.',
                    rb'/\.\./.*/',
                    rb'\\\.\.\\'
                ],
                "description": "Path traversal in archive entry",
                "severity": "high"
            },
            "executable_content": {
                "patterns": [
                    rb'\.exe\x00',
                    rb'\.bat\x00',
                    rb'\.cmd\x00',
                    rb'\.scr\x00',
                    rb'\.com\x00'
                ],
                "description": "Executable files in archive",
                "severity": "medium"
            },
            "suspicious_extensions": {
                "patterns": [
                    rb'\.jar\x00',
                    rb'\.class\x00',
                    rb'\.dll\x00',
                    rb'\.so\x00'
                ],
                "description": "Suspicious file types in archive",
                "severity": "low"
            }
        }
    
    def validate_input(self, content: str, file_path: str) -> bool:
        archive_extensions = ['.zip', '.jar', '.tar', '.gz', '.rar']
        if any(file_path.lower().endswith(ext) for ext in archive_extensions):
            return True
        
        # Check for archive signatures
        try:
            binary_content = content.encode('latin-1') if isinstance(content, str) else content
            signatures = [
                b'PK\x03\x04',  # ZIP
                b'PK\x05\x06',  # ZIP
                b'Rar!\x1a\x07',  # RAR
                b'\x1f\x8b'  # GZIP
            ]
            return any(binary_content.startswith(sig) for sig in signatures)
        except:
            return False
    
    def scan(self, content: str, file_path: str, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        findings = []
        
        try:
            binary_content = content.encode('latin-1') if isinstance(content, str) else content
            
            for pattern_name, pattern_data in self.archive_patterns.items():
                for pattern in pattern_data["patterns"]:
                    if re.search(pattern, binary_content):
                        findings.append({
                            "title": f"Archive Exploit: {pattern_data['description']}",
                            "description": pattern_data["description"],
                            "severity": pattern_data["severity"],
                            "confidence": 0.7,
                            "file_path": file_path,
                            "line_number": 0,
                            "context": "Archive binary analysis",
                            "scanner_name": self.manifest.name,
                            "cwe_id": "CWE-506",
                            "remediation": "Validate archive contents before extraction",
                            "detection_metadata": {"pattern_type": pattern_name}
                        })
        
        except Exception as e:
            logger.error(f"Archive scanning error: {e}")
        
        return findings
