#!/bin/bash
# ByteGuardX Linux/macOS Launcher
# Automatically sets up and runs the complete ByteGuardX application

echo ""
echo "========================================"
echo "   ByteGuardX - AI Security Scanner"
echo "========================================"
echo ""

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo "ERROR: Python is not installed or not in PATH"
        echo "Please install Python 3.8+ from https://python.org"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "ERROR: Node.js is not installed or not in PATH"
    echo "Please install Node.js 16+ from https://nodejs.org"
    exit 1
fi

echo "Starting ByteGuardX setup and launch..."
echo ""

# Make the script executable
chmod +x setup_and_run_byteguardx.py

# Run the setup script
$PYTHON_CMD setup_and_run_byteguardx.py

echo ""
echo "ByteGuardX has been stopped."
