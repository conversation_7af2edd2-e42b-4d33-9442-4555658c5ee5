# ByteGuardX Liquid Glass Frontend

A next-generation AI-powered security scanning interface featuring dynamic Liquid Glass effects using WebGL/Three.js shaders.

## 🌟 Features

### Liquid Glass Components
- **LiquidGlassButton** - Interactive buttons with real-time liquid distortion
- **LiquidGlassCard** - Depth-aware cards with internal fluid motion
- **LiquidGlassModal** - Animated glass modals with refraction effects
- **LiquidGlassInput** - Form inputs with micro glass ripples
- **LiquidGlassPanel** - Resizable panels with flowing backgrounds
- **LiquidGlassLoader** - Custom liquid orb loading animations
- **LiquidGlassTabs** - Tabs with flowing internal motion
- **LiquidGlassNavbar** - Navigation with slow-moving internal fluid
- **LiquidGlassScrollbar** - Custom scrollbars with liquid thumb
- **LiquidGlassCodeViewer** - Code viewer with syntax highlighting

### Performance Optimization
- **GPU Performance Detection** - Automatically detects GPU capabilities
- **Fallback System** - Graceful degradation to glassmorphism on low-end devices
- **Shader Optimization** - Efficient GLSL shaders with minimal passes
- **Memory Management** - Proper cleanup and resource management

### Accessibility
- **High Contrast Support** - Adapts to system preferences
- **Reduced Motion** - Respects user motion preferences
- **Keyboard Navigation** - Full keyboard accessibility
- **Screen Reader Support** - Proper ARIA labels and semantics

## 🚀 Quick Start

### Prerequisites
- Node.js 16+
- npm or yarn
- Modern browser with WebGL 2.0 support

### Installation

```bash
# Install dependencies
npm install

# Start development server
npm start

# Build for production
npm run build
```

### Dependencies
```json
{
  "@react-three/fiber": "^8.15.12",
  "framer-motion": "^10.16.16",
  "react": "^18.2.0",
  "three": "^0.159.0",
  "typescript": "^4.9.5",
  "tailwindcss": "^3.3.6"
}
```

## 🎨 Design System

### Color Palette
- **Background**: Pure Black (#000000)
- **Accent**: Cyan (#00ffff)
- **Glass**: Semi-transparent white (15% opacity)
- **Text**: White with varying opacity levels

### Typography
- **Primary**: Inter, SF Pro Display, IBM Plex Sans
- **Monospace**: JetBrains Mono, SF Mono, Monaco

### Animations
- **Liquid Flow**: 8s ease-in-out infinite
- **Glass Shimmer**: 3s ease-in-out infinite
- **Cyber Pulse**: 2s ease-in-out infinite
- **Float**: 6s ease-in-out infinite

## 🔧 Component Usage

### Basic Button
```tsx
import { LiquidGlassButton } from './components/LiquidGlass';

<LiquidGlassButton
  variant="primary"
  size="md"
  onClick={handleClick}
  loading={isLoading}
>
  Scan File
</LiquidGlassButton>
```

### Interactive Card
```tsx
import { LiquidGlassCard } from './components/LiquidGlass';

<LiquidGlassCard
  hoverable
  clickable
  onClick={handleCardClick}
  depth={2}
  glowIntensity={0.5}
>
  <h3>Scan Results</h3>
  <p>42 vulnerabilities found</p>
</LiquidGlassCard>
```

### Code Viewer
```tsx
import { LiquidGlassCodeViewer } from './components/LiquidGlass';

<LiquidGlassCodeViewer
  code={sourceCode}
  language="javascript"
  highlightedLines={[15, 23, 45]}
  showLineNumbers
  onLineClick={handleLineClick}
/>
```

### Modal Dialog
```tsx
import { LiquidGlassModal } from './components/LiquidGlass';

<LiquidGlassModal
  isOpen={showModal}
  onClose={() => setShowModal(false)}
  title="Scan Results"
  size="lg"
>
  <ScanResultsContent />
</LiquidGlassModal>
```

## 🎯 Performance Guidelines

### GPU Performance Levels
- **High**: RTX/GTX series, Radeon RX series - Full liquid glass effects
- **Medium**: Integrated graphics - Simplified shaders
- **Low**: Older hardware - Fallback to glassmorphism

### Optimization Tips
1. Use `useGPUPerformance()` hook to detect capabilities
2. Limit concurrent shader instances
3. Implement proper cleanup in useEffect
4. Use React.memo for expensive components
5. Optimize shader uniforms updates

## 🛡️ Security Considerations

### Shader Security
- All shaders are statically defined
- No dynamic shader compilation from user input
- Proper input validation for uniform values
- Memory bounds checking

### Performance Security
- Frame rate limiting to prevent DoS
- GPU memory usage monitoring
- Graceful degradation on resource exhaustion

## 🎨 Customization

### Theme Configuration
```tsx
// tailwind.config.js
module.exports = {
  theme: {
    extend: {
      colors: {
        'byteguard': {
          'cyan': '#00ffff',
          'dark': '#000000',
          'glass': 'rgba(255, 255, 255, 0.15)',
        }
      }
    }
  }
}
```

### Shader Customization
```tsx
// Custom shader uniforms
const customUniforms = {
  uIntensity: { value: 1.5 },
  uDistortion: { value: 0.1 },
  uColor: { value: new Vector3(0.0, 1.0, 1.0) },
  uGlowStrength: { value: 0.8 }
};
```

## 🧪 Testing

### Unit Tests
```bash
npm test
```

### Performance Testing
```bash
npm run test:performance
```

### Accessibility Testing
```bash
npm run test:a11y
```

## 📱 Browser Support

### Minimum Requirements
- Chrome 80+
- Firefox 78+
- Safari 14+
- Edge 80+

### WebGL Support
- WebGL 2.0 required for full effects
- WebGL 1.0 fallback available
- Software rendering fallback

## 🚀 Deployment

### Production Build
```bash
npm run build
```

### Environment Variables
```env
REACT_APP_API_URL=https://api.byteguardx.com
REACT_APP_ENABLE_SHADERS=true
REACT_APP_PERFORMANCE_MODE=auto
```

### CDN Optimization
- Shader files are pre-compiled
- Three.js modules are tree-shaken
- Assets are optimized for CDN delivery

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create feature branch
3. Install dependencies
4. Run development server
5. Make changes
6. Test thoroughly
7. Submit pull request

### Code Standards
- TypeScript strict mode
- ESLint + Prettier
- Component documentation
- Performance testing
- Accessibility compliance

## 📄 License

MIT License - see LICENSE file for details.

## 🆘 Support

For technical support or questions:
- GitHub Issues
- Documentation Wiki
- Community Discord

---

**ByteGuardX** - Next-generation AI-powered security scanning with liquid glass interface technology.
