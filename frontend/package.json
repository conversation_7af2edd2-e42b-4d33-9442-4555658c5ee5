{"name": "byteguardx-frontend", "version": "1.0.0", "description": "ByteGuardX AI-Powered Security Scanner Frontend", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^27.5.2", "@types/node": "^16.18.11", "@types/react": "^18.0.26", "@types/react-dom": "^18.0.10", "react": "^18.2.0", "react-dom": "^18.2.0", "react-scripts": "5.0.1", "typescript": "^4.9.4", "web-vitals": "^2.1.4", "axios": "^1.6.0", "react-router-dom": "^6.8.0", "framer-motion": "^10.16.0", "recharts": "^2.8.0", "react-dropzone": "^14.2.3", "react-syntax-highlighter": "^15.5.0", "react-hot-toast": "^2.4.1", "lucide-react": "^0.294.0", "clsx": "^2.0.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@headlessui/react": "^1.7.17", "socket.io-client": "^4.7.4", "react-query": "^3.39.3", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "date-fns": "^2.30.0", "react-markdown": "^9.0.1", "prismjs": "^1.29.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "lint": "eslint src --ext .ts,.tsx", "lint:fix": "eslint src --ext .ts,.tsx --fix", "type-check": "tsc --noEmit"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/prismjs": "^1.26.3", "@types/react-syntax-highlighter": "^15.5.11", "eslint": "^8.55.0", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1"}, "proxy": "http://localhost:5000"}