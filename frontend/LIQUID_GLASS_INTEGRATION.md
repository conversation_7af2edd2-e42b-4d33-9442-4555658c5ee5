# ByteGuardX Liquid Glass Integration Guide

This guide shows how to upgrade your existing glassmorphism components with real-time liquid glass shader effects while preserving all functionality.

## Quick Start

### 1. Import the Components
```jsx
import LiquidGlassWrapper from './components/LiquidGlassWrapper';
import { useLiquidGlass } from './hooks/useLiquidGlass';
import './styles/liquidGlass.css';
```

### 2. Wrap Existing Elements

#### Before (Current Glassmorphism):
```jsx
<button className="backdrop-blur-md bg-black/20 border border-white/15 hover:bg-black/30 px-4 py-2">
  Scan File
</button>
```

#### After (Liquid Glass):
```jsx
<LiquidGlassWrapper 
  className="rounded-lg"
  fallbackClass="backdrop-blur-md bg-black/20 border border-white/15 hover:bg-black/30"
>
  <button className="px-4 py-2 w-full h-full bg-transparent border-none text-white">
    Scan File
  </button>
</LiquidGlassWrapper>
```

## Component Upgrades

### Buttons
```jsx
// Your existing button
const ScanButton = ({ onClick, children }) => (
  <button 
    className="backdrop-blur-md bg-black/20 border border-white/15 hover:bg-black/30 px-6 py-3 rounded-lg text-white font-medium"
    onClick={onClick}
  >
    {children}
  </button>
);

// Upgraded with liquid glass
const LiquidScanButton = ({ onClick, children }) => (
  <LiquidGlassWrapper 
    className="rounded-lg"
    fallbackClass="backdrop-blur-md bg-black/20 border border-white/15 hover:bg-black/30"
  >
    <button 
      className="px-6 py-3 w-full h-full bg-transparent border-none text-white font-medium"
      onClick={onClick}
    >
      {children}
    </button>
  </LiquidGlassWrapper>
);
```

### Cards
```jsx
// Your existing card
const ResultCard = ({ result, onClick }) => (
  <div 
    className="backdrop-blur-md bg-black/20 border border-white/15 p-6 rounded-xl cursor-pointer hover:bg-black/25"
    onClick={onClick}
  >
    <h3 className="text-white font-semibold">{result.title}</h3>
    <p className="text-white/60">{result.description}</p>
  </div>
);

// Upgraded with liquid glass
const LiquidResultCard = ({ result, onClick }) => (
  <LiquidGlassWrapper 
    className="rounded-xl cursor-pointer"
    fallbackClass="backdrop-blur-md bg-black/20 border border-white/15 hover:bg-black/25"
  >
    <div className="p-6" onClick={onClick}>
      <h3 className="text-white font-semibold">{result.title}</h3>
      <p className="text-white/60">{result.description}</p>
    </div>
  </LiquidGlassWrapper>
);
```

### Modals
```jsx
// Your existing modal
const ScanModal = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/60" onClick={onClose} />
      <div className="relative backdrop-blur-lg bg-black/40 border border-white/20 rounded-xl p-6 max-w-lg w-full">
        {children}
      </div>
    </div>
  );
};

// Upgraded with liquid glass
const LiquidScanModal = ({ isOpen, onClose, children }) => {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/60" onClick={onClose} />
      <LiquidGlassWrapper 
        className="relative rounded-xl max-w-lg w-full"
        fallbackClass="backdrop-blur-lg bg-black/40 border border-white/20"
        intensity={1.2}
      >
        <div className="p-6">
          {children}
        </div>
      </LiquidGlassWrapper>
    </div>
  );
};
```

### Navigation Panels
```jsx
// Your existing nav
const Sidebar = ({ children }) => (
  <nav className="backdrop-blur-lg bg-black/30 border-r border-white/10 p-4">
    {children}
  </nav>
);

// Upgraded with liquid glass
const LiquidSidebar = ({ children }) => (
  <LiquidGlassWrapper 
    fallbackClass="backdrop-blur-lg bg-black/30 border-r border-white/10"
    intensity={0.8}
  >
    <nav className="p-4">
      {children}
    </nav>
  </LiquidGlassWrapper>
);
```

## Advanced Integration

### Using the Hook for Custom Control
```jsx
const CustomComponent = ({ children }) => {
  const { elementRef, isActive, shouldUseShader } = useLiquidGlass({
    intensity: 1.0,
    enableOnHover: true,
    enableOnFocus: true
  });

  if (shouldUseShader) {
    return (
      <LiquidGlassWrapper ref={elementRef}>
        {children}
      </LiquidGlassWrapper>
    );
  }

  // Fallback to CSS
  return (
    <div 
      ref={elementRef}
      className="backdrop-blur-md bg-black/20 border border-white/15"
    >
      {children}
    </div>
  );
};
```

### Performance Optimization
```jsx
import { detectShaderCapabilities, PERFORMANCE_LEVELS } from './utils/shaderUtils';

const OptimizedComponent = ({ children }) => {
  const capabilities = detectShaderCapabilities();
  
  const shouldUseShaders = capabilities.level !== PERFORMANCE_LEVELS.LOW;
  
  return (
    <LiquidGlassWrapper 
      enableShader={shouldUseShaders}
      intensity={capabilities.level === PERFORMANCE_LEVELS.HIGH ? 1.0 : 0.7}
    >
      {children}
    </LiquidGlassWrapper>
  );
};
```

## CSS-Only Integration

For simpler integration, you can use CSS classes:

```jsx
// Add to your existing elements
<div className="liquid-glass lg-card">
  <div className="p-6">
    Your content here
  </div>
</div>

<button className="liquid-glass lg-button px-4 py-2">
  Your button
</button>
```

## Performance Considerations

1. **Automatic Detection**: The system automatically detects GPU capabilities
2. **Graceful Fallback**: Falls back to CSS glassmorphism on low-end devices
3. **Memory Management**: Properly cleans up WebGL resources
4. **Frame Rate Monitoring**: Reduces quality if FPS drops below 30

## Browser Support

- **Full Support**: Chrome 80+, Firefox 78+, Safari 14+, Edge 80+
- **Fallback**: All browsers with CSS backdrop-filter support
- **Graceful Degradation**: Standard CSS on unsupported browsers

## Integration Checklist

- [ ] Import LiquidGlassWrapper component
- [ ] Wrap existing glassmorphism elements
- [ ] Remove background/border classes from inner elements
- [ ] Add fallbackClass with original styles
- [ ] Test on different devices/browsers
- [ ] Verify performance is acceptable
- [ ] Ensure accessibility is maintained

## Troubleshooting

**Shaders not working?**
- Check browser console for WebGL errors
- Verify GPU drivers are up to date
- Test on different browsers

**Performance issues?**
- The system automatically reduces quality on slower devices
- You can manually disable shaders with `enableShader={false}`

**Visual glitches?**
- Ensure parent containers have proper positioning
- Check for CSS conflicts with z-index
- Verify canvas sizing is correct
