/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // ByteGuardX Brand Colors
        'byteguard': {
          'cyan': '#00ffff',
          'dark': '#000000',
          'glass': 'rgba(255, 255, 255, 0.15)',
        }
      },
      fontFamily: {
        'sans': ['Inter', 'SF Pro Display', 'IBM Plex Sans', 'system-ui', 'sans-serif'],
        'mono': ['JetBrains Mono', 'SF Mono', 'Monaco', 'Inconsolata', 'monospace'],
      },
      backdropBlur: {
        'xs': '2px',
        'sm': '4px',
        'md': '12px',
        'lg': '16px',
        'xl': '24px',
        '2xl': '40px',
        '3xl': '64px',
      },
      animation: {
        'liquid-flow': 'liquidFlow 8s ease-in-out infinite',
        'glass-shimmer': 'glassShimmer 3s ease-in-out infinite',
        'cyber-pulse': 'cyberPulse 2s ease-in-out infinite',
        'float': 'float 6s ease-in-out infinite',
        'glow': 'glow 2s ease-in-out infinite alternate',
      },
      keyframes: {
        liquidFlow: {
          '0%, 100%': { 
            transform: 'translateX(0%) translateY(0%) scale(1)',
            opacity: '0.3'
          },
          '25%': { 
            transform: 'translateX(5%) translateY(-2%) scale(1.02)',
            opacity: '0.4'
          },
          '50%': { 
            transform: 'translateX(-3%) translateY(3%) scale(0.98)',
            opacity: '0.5'
          },
          '75%': { 
            transform: 'translateX(2%) translateY(-1%) scale(1.01)',
            opacity: '0.4'
          },
        },
        glassShimmer: {
          '0%': { 
            backgroundPosition: '-200% 0'
          },
          '100%': { 
            backgroundPosition: '200% 0'
          },
        },
        cyberPulse: {
          '0%, 100%': { 
            boxShadow: '0 0 5px rgba(0, 255, 255, 0.3), 0 0 10px rgba(0, 255, 255, 0.2), 0 0 15px rgba(0, 255, 255, 0.1)',
            borderColor: 'rgba(0, 255, 255, 0.3)'
          },
          '50%': { 
            boxShadow: '0 0 10px rgba(0, 255, 255, 0.6), 0 0 20px rgba(0, 255, 255, 0.4), 0 0 30px rgba(0, 255, 255, 0.2)',
            borderColor: 'rgba(0, 255, 255, 0.6)'
          },
        },
        float: {
          '0%, 100%': { 
            transform: 'translateY(0px) rotate(0deg)' 
          },
          '33%': { 
            transform: 'translateY(-10px) rotate(1deg)' 
          },
          '66%': { 
            transform: 'translateY(5px) rotate(-1deg)' 
          },
        },
        glow: {
          '0%': { 
            textShadow: '0 0 5px rgba(0, 255, 255, 0.5), 0 0 10px rgba(0, 255, 255, 0.3), 0 0 15px rgba(0, 255, 255, 0.2)'
          },
          '100%': { 
            textShadow: '0 0 10px rgba(0, 255, 255, 0.8), 0 0 20px rgba(0, 255, 255, 0.5), 0 0 30px rgba(0, 255, 255, 0.3)'
          },
        },
      },
      backgroundImage: {
        'glass-gradient': 'linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%)',
        'cyber-gradient': 'linear-gradient(135deg, rgba(0, 255, 255, 0.1) 0%, rgba(0, 128, 255, 0.05) 100%)',
        'liquid-shimmer': 'linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent)',
      },
      boxShadow: {
        'glass': '0 8px 32px 0 rgba(31, 38, 135, 0.37)',
        'glass-inset': 'inset 0 1px 0 0 rgba(255, 255, 255, 0.05)',
        'cyber': '0 0 20px rgba(0, 255, 255, 0.3)',
        'cyber-lg': '0 0 40px rgba(0, 255, 255, 0.4)',
        'liquid': '0 8px 32px 0 rgba(0, 255, 255, 0.15)',
      },
      borderRadius: {
        'glass': '16px',
        'cyber': '8px',
      },
      spacing: {
        '18': '4.5rem',
        '88': '22rem',
        '128': '32rem',
      },
      zIndex: {
        '60': '60',
        '70': '70',
        '80': '80',
        '90': '90',
        '100': '100',
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
    function({ addUtilities }) {
      const newUtilities = {
        '.glass-morphism': {
          background: 'rgba(255, 255, 255, 0.05)',
          backdropFilter: 'blur(10px)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
        },
        '.liquid-glass': {
          background: 'rgba(0, 0, 0, 0.2)',
          backdropFilter: 'blur(12px)',
          border: '1px solid rgba(255, 255, 255, 0.15)',
          boxShadow: '0 8px 32px 0 rgba(0, 255, 255, 0.1)',
        },
        '.cyber-border': {
          border: '1px solid rgba(0, 255, 255, 0.3)',
          boxShadow: '0 0 10px rgba(0, 255, 255, 0.2)',
        },
        '.text-glow': {
          textShadow: '0 0 10px rgba(0, 255, 255, 0.5)',
        },
        '.scrollbar-hide': {
          '-ms-overflow-style': 'none',
          'scrollbar-width': 'none',
          '&::-webkit-scrollbar': {
            display: 'none'
          }
        }
      }
      addUtilities(newUtilities)
    }
  ],
}
