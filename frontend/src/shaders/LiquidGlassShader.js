// Liquid Glass Shader System for ByteGuardX
// Minimal, performant WebGL shaders for existing glassmorphism components

export const liquidGlassVertexShader = `
  attribute vec2 position;
  attribute vec2 uv;
  varying vec2 vUv;
  varying vec2 vPosition;
  
  void main() {
    vUv = uv;
    vPosition = position;
    gl_Position = vec4(position, 0.0, 1.0);
  }
`;

export const liquidGlassFragmentShader = `
  precision mediump float;
  
  uniform float uTime;
  uniform vec2 uResolution;
  uniform vec2 uMouse;
  uniform float uIntensity;
  uniform float uHover;
  uniform vec3 uAccentColor;
  
  varying vec2 vUv;
  varying vec2 vPosition;
  
  // Noise function for liquid distortion
  float noise(vec2 st) {
    return fract(sin(dot(st.xy, vec2(12.9898,78.233))) * 43758.5453123);
  }
  
  // Smooth noise
  float smoothNoise(vec2 st) {
    vec2 i = floor(st);
    vec2 f = fract(st);
    
    float a = noise(i);
    float b = noise(i + vec2(1.0, 0.0));
    float c = noise(i + vec2(0.0, 1.0));
    float d = noise(i + vec2(1.0, 1.0));
    
    vec2 u = f * f * (3.0 - 2.0 * f);
    
    return mix(a, b, u.x) + (c - a) * u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
  }
  
  // Liquid distortion
  vec2 liquidDistortion(vec2 uv, float time, float intensity) {
    float wave1 = sin(uv.x * 6.0 + time * 1.5) * 0.01 * intensity;
    float wave2 = cos(uv.y * 4.0 + time * 1.2) * 0.008 * intensity;
    float wave3 = sin((uv.x + uv.y) * 3.0 + time * 2.0) * 0.006 * intensity;
    
    // Mouse interaction ripple
    vec2 mouseInfluence = (uMouse - uv) * 0.5;
    float mouseDistance = length(mouseInfluence);
    float ripple = 0.0;
    
    if (mouseDistance < 0.3) {
      ripple = sin(mouseDistance * 15.0 - time * 6.0) * 0.015 * (0.3 - mouseDistance);
    }
    
    return vec2(wave1 + wave3 + ripple, wave2 + wave3 + ripple);
  }
  
  // Glass refraction effect
  vec3 glassEffect(vec2 uv, vec2 distortion) {
    vec2 refractedUV = uv + distortion;
    
    // Create depth illusion with noise
    float depth = smoothNoise(refractedUV * 8.0 + uTime * 0.3) * 0.1;
    
    // Base glass color (subtle cyan tint)
    vec3 glassColor = vec3(0.05, 0.1, 0.15) + vec3(0.0, 0.2, 0.3) * depth;
    
    // Add fresnel-like edge highlighting
    float edge = 1.0 - smoothstep(0.0, 0.1, min(min(uv.x, 1.0 - uv.x), min(uv.y, 1.0 - uv.y)));
    glassColor += vec3(0.1, 0.2, 0.3) * edge * 0.5;
    
    // Caustics effect
    float caustic = smoothNoise(refractedUV * 12.0 + uTime * 0.8);
    caustic = pow(caustic, 3.0) * 0.3;
    glassColor += vec3(0.0, 0.3, 0.4) * caustic;
    
    return glassColor;
  }
  
  void main() {
    vec2 uv = vUv;
    float time = uTime;
    float intensity = uIntensity * (1.0 + uHover * 0.5);
    
    // Apply liquid distortion
    vec2 distortion = liquidDistortion(uv, time, intensity);
    
    // Get glass effect
    vec3 color = glassEffect(uv, distortion);
    
    // Add hover accent color
    if (uHover > 0.0) {
      float hoverGlow = smoothstep(0.0, 1.0, 1.0 - length(uv - vec2(0.5)));
      color += uAccentColor * hoverGlow * uHover * 0.3;
    }
    
    // Subtle transparency
    float alpha = 0.15 + intensity * 0.1;
    
    gl_FragColor = vec4(color, alpha);
  }
`;

// WebGL utility class
export class LiquidGlassRenderer {
  constructor(canvas) {
    this.canvas = canvas;
    this.gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    if (!this.gl) {
      console.warn('WebGL not supported, falling back to CSS glassmorphism');
      return null;
    }
    
    this.program = null;
    this.uniforms = {};
    this.attributes = {};
    this.startTime = Date.now();
    this.mouse = { x: 0.5, y: 0.5 };
    this.isHovered = false;
    
    this.init();
  }
  
  init() {
    const gl = this.gl;
    
    // Create shaders
    const vertexShader = this.createShader(gl.VERTEX_SHADER, liquidGlassVertexShader);
    const fragmentShader = this.createShader(gl.FRAGMENT_SHADER, liquidGlassFragmentShader);
    
    // Create program
    this.program = gl.createProgram();
    gl.attachShader(this.program, vertexShader);
    gl.attachShader(this.program, fragmentShader);
    gl.linkProgram(this.program);
    
    if (!gl.getProgramParameter(this.program, gl.LINK_STATUS)) {
      console.error('Shader program failed to link:', gl.getProgramInfoLog(this.program));
      return;
    }
    
    // Get uniform and attribute locations
    this.uniforms = {
      uTime: gl.getUniformLocation(this.program, 'uTime'),
      uResolution: gl.getUniformLocation(this.program, 'uResolution'),
      uMouse: gl.getUniformLocation(this.program, 'uMouse'),
      uIntensity: gl.getUniformLocation(this.program, 'uIntensity'),
      uHover: gl.getUniformLocation(this.program, 'uHover'),
      uAccentColor: gl.getUniformLocation(this.program, 'uAccentColor')
    };
    
    this.attributes = {
      position: gl.getAttribLocation(this.program, 'position'),
      uv: gl.getAttribLocation(this.program, 'uv')
    };
    
    // Create geometry (full screen quad)
    this.createGeometry();
    
    // Start render loop
    this.render();
  }
  
  createShader(type, source) {
    const gl = this.gl;
    const shader = gl.createShader(type);
    gl.shaderSource(shader, source);
    gl.compileShader(shader);
    
    if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
      console.error('Shader compilation error:', gl.getShaderInfoLog(shader));
      gl.deleteShader(shader);
      return null;
    }
    
    return shader;
  }
  
  createGeometry() {
    const gl = this.gl;
    
    // Full screen quad vertices
    const vertices = new Float32Array([
      -1, -1, 0, 0,  // bottom-left
       1, -1, 1, 0,  // bottom-right
      -1,  1, 0, 1,  // top-left
       1,  1, 1, 1   // top-right
    ]);
    
    this.vertexBuffer = gl.createBuffer();
    gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer);
    gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);
  }
  
  updateMouse(x, y) {
    const rect = this.canvas.getBoundingClientRect();
    this.mouse.x = (x - rect.left) / rect.width;
    this.mouse.y = 1.0 - (y - rect.top) / rect.height;
  }
  
  setHover(hovered) {
    this.isHovered = hovered;
  }
  
  render() {
    if (!this.gl || !this.program) return;
    
    const gl = this.gl;
    const time = (Date.now() - this.startTime) * 0.001;
    
    // Set viewport
    gl.viewport(0, 0, this.canvas.width, this.canvas.height);
    
    // Clear
    gl.clearColor(0, 0, 0, 0);
    gl.clear(gl.COLOR_BUFFER_BIT);
    
    // Use program
    gl.useProgram(this.program);
    
    // Set uniforms
    gl.uniform1f(this.uniforms.uTime, time);
    gl.uniform2f(this.uniforms.uResolution, this.canvas.width, this.canvas.height);
    gl.uniform2f(this.uniforms.uMouse, this.mouse.x, this.mouse.y);
    gl.uniform1f(this.uniforms.uIntensity, 1.0);
    gl.uniform1f(this.uniforms.uHover, this.isHovered ? 1.0 : 0.0);
    gl.uniform3f(this.uniforms.uAccentColor, 0.0, 1.0, 1.0); // Cyan
    
    // Set attributes
    gl.bindBuffer(gl.ARRAY_BUFFER, this.vertexBuffer);
    
    gl.enableVertexAttribArray(this.attributes.position);
    gl.vertexAttribPointer(this.attributes.position, 2, gl.FLOAT, false, 16, 0);
    
    gl.enableVertexAttribArray(this.attributes.uv);
    gl.vertexAttribPointer(this.attributes.uv, 2, gl.FLOAT, false, 16, 8);
    
    // Enable blending for transparency
    gl.enable(gl.BLEND);
    gl.blendFunc(gl.SRC_ALPHA, gl.ONE_MINUS_SRC_ALPHA);
    
    // Draw
    gl.drawArrays(gl.TRIANGLE_STRIP, 0, 4);
    
    // Continue render loop
    requestAnimationFrame(() => this.render());
  }
  
  resize(width, height) {
    this.canvas.width = width;
    this.canvas.height = height;
  }
  
  destroy() {
    if (this.gl && this.program) {
      this.gl.deleteProgram(this.program);
    }
  }
}
