import React, { useState, useEffect } from 'react';
import {
  LiquidGlassNavbar,
  LiquidGlassCard,
  LiquidGlassButton,
  LiquidGlassInput,
  LiquidGlassPanel,
  LiquidGlassLoader,
  LiquidGlassTabs,
  LiquidGlassModal,
  LiquidGlassCodeViewer,
  LiquidGlassScrollbar
} from './LiquidGlass';

interface ScanResult {
  id: string;
  fileName: string;
  findings: Array<{
    type: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    line: number;
    description: string;
    evidence: string;
  }>;
  timestamp: Date;
  status: 'completed' | 'scanning' | 'failed';
}

const ByteGuardXDashboard: React.FC = () => {
  const [activeTab, setActiveTab] = useState('scanner');
  const [isScanning, setIsScanning] = useState(false);
  const [scanResults, setScanResults] = useState<ScanResult[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showModal, setShowModal] = useState(false);
  const [selectedResult, setSelectedResult] = useState<ScanResult | null>(null);

  // Mock scan function
  const handleScan = async () => {
    if (!selectedFile) return;

    setIsScanning(true);
    
    // Simulate scanning delay
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    const mockResult: ScanResult = {
      id: Date.now().toString(),
      fileName: selectedFile.name,
      findings: [
        {
          type: 'SQL Injection',
          severity: 'high',
          line: 42,
          description: 'Potential SQL injection vulnerability detected',
          evidence: 'query = "SELECT * FROM users WHERE id = " + userId'
        },
        {
          type: 'XSS',
          severity: 'medium',
          line: 67,
          description: 'Cross-site scripting vulnerability',
          evidence: 'innerHTML = userInput'
        }
      ],
      timestamp: new Date(),
      status: 'completed'
    };

    setScanResults(prev => [mockResult, ...prev]);
    setIsScanning(false);
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
    }
  };

  const navItems = [
    {
      id: 'scanner',
      label: 'Scanner',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
      active: activeTab === 'scanner'
    },
    {
      id: 'results',
      label: 'Results',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      badge: scanResults.length > 0 ? scanResults.length : undefined
    },
    {
      id: 'plugins',
      label: 'Plugins',
      icon: (
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
        </svg>
      )
    }
  ];

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      content: (
        <div className="space-y-6">
          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <LiquidGlassCard className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">Total Scans</p>
                  <p className="text-2xl font-bold text-white">{scanResults.length}</p>
                </div>
                <div className="w-12 h-12 bg-cyan-400/20 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
              </div>
            </LiquidGlassCard>

            <LiquidGlassCard className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">Critical Issues</p>
                  <p className="text-2xl font-bold text-red-400">
                    {scanResults.reduce((acc, result) => 
                      acc + result.findings.filter(f => f.severity === 'critical').length, 0
                    )}
                  </p>
                </div>
                <div className="w-12 h-12 bg-red-400/20 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
              </div>
            </LiquidGlassCard>

            <LiquidGlassCard className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-white/60 text-sm">Security Score</p>
                  <p className="text-2xl font-bold text-green-400">85%</p>
                </div>
                <div className="w-12 h-12 bg-green-400/20 rounded-lg flex items-center justify-center">
                  <svg className="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                  </svg>
                </div>
              </div>
            </LiquidGlassCard>
          </div>

          {/* File Upload */}
          <LiquidGlassCard className="p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Upload File for Scanning</h3>
            <div className="space-y-4">
              <div className="border-2 border-dashed border-white/20 rounded-lg p-8 text-center">
                <input
                  type="file"
                  onChange={handleFileSelect}
                  className="hidden"
                  id="file-upload"
                  accept=".js,.ts,.py,.java,.go,.php,.rb,.cpp,.c,.cs"
                />
                <label htmlFor="file-upload" className="cursor-pointer">
                  <div className="space-y-2">
                    <svg className="w-12 h-12 text-white/40 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <p className="text-white/60">
                      {selectedFile ? selectedFile.name : 'Click to upload or drag and drop'}
                    </p>
                    <p className="text-white/40 text-sm">
                      Supports: JS, TS, Python, Java, Go, PHP, Ruby, C/C++, C#
                    </p>
                  </div>
                </label>
              </div>
              
              <LiquidGlassButton
                onClick={handleScan}
                disabled={!selectedFile || isScanning}
                loading={isScanning}
                className="w-full"
              >
                {isScanning ? 'Scanning...' : 'Start Security Scan'}
              </LiquidGlassButton>
            </div>
          </LiquidGlassCard>
        </div>
      )
    },
    {
      id: 'history',
      label: 'Scan History',
      content: (
        <LiquidGlassScrollbar maxHeight="600px">
          <div className="space-y-4">
            {scanResults.length === 0 ? (
              <LiquidGlassCard className="p-8 text-center">
                <p className="text-white/60">No scan results yet. Upload a file to get started.</p>
              </LiquidGlassCard>
            ) : (
              scanResults.map((result) => (
                <LiquidGlassCard
                  key={result.id}
                  className="p-6 cursor-pointer"
                  hoverable
                  clickable
                  onClick={() => {
                    setSelectedResult(result);
                    setShowModal(true);
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="text-white font-medium">{result.fileName}</h4>
                      <p className="text-white/60 text-sm">
                        {result.findings.length} findings • {result.timestamp.toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      {result.findings.map((finding, index) => (
                        <span
                          key={index}
                          className={`px-2 py-1 text-xs rounded ${
                            finding.severity === 'critical' ? 'bg-red-500/20 text-red-400' :
                            finding.severity === 'high' ? 'bg-orange-500/20 text-orange-400' :
                            finding.severity === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                            'bg-blue-500/20 text-blue-400'
                          }`}
                        >
                          {finding.severity}
                        </span>
                      ))}
                    </div>
                  </div>
                </LiquidGlassCard>
              ))
            )}
          </div>
        </LiquidGlassScrollbar>
      )
    }
  ];

  return (
    <div className="min-h-screen bg-black">
      {/* Navigation */}
      <LiquidGlassNavbar
        title="ByteGuardX"
        logo={
          <div className="w-8 h-8 bg-cyan-400 rounded-lg flex items-center justify-center">
            <svg className="w-5 h-5 text-black" fill="currentColor" viewBox="0 0 24 24">
              <path d="M12 1L3 5v6c0 5.55 3.84 10.74 9 12 5.16-1.26 9-6.45 9-12V5l-9-4z"/>
            </svg>
          </div>
        }
        items={navItems.map(item => ({
          ...item,
          onClick: () => setActiveTab(item.id)
        }))}
        actions={
          <LiquidGlassButton size="sm" variant="secondary">
            Settings
          </LiquidGlassButton>
        }
      />

      {/* Main Content */}
      <div className="pt-20 px-6 pb-6">
        <div className="max-w-7xl mx-auto">
          {activeTab === 'scanner' && (
            <div className="space-y-6">
              <div className="flex items-center justify-between">
                <h1 className="text-3xl font-bold text-white">Security Scanner</h1>
                {isScanning && <LiquidGlassLoader size="sm" text="Analyzing..." />}
              </div>
              
              <LiquidGlassTabs
                tabs={tabs}
                defaultActiveTab="overview"
              />
            </div>
          )}

          {activeTab === 'results' && (
            <div className="space-y-6">
              <h1 className="text-3xl font-bold text-white">Scan Results</h1>
              <LiquidGlassScrollbar maxHeight="700px">
                <div className="space-y-4">
                  {scanResults.map((result) => (
                    <LiquidGlassCard key={result.id} className="p-6">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h3 className="text-xl font-semibold text-white">{result.fileName}</h3>
                          <span className="text-white/60 text-sm">
                            {result.timestamp.toLocaleString()}
                          </span>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {result.findings.map((finding, index) => (
                            <div
                              key={index}
                              className={`p-4 rounded border-l-4 ${
                                finding.severity === 'critical' ? 'border-red-500 bg-red-500/10' :
                                finding.severity === 'high' ? 'border-orange-500 bg-orange-500/10' :
                                finding.severity === 'medium' ? 'border-yellow-500 bg-yellow-500/10' :
                                'border-blue-500 bg-blue-500/10'
                              }`}
                            >
                              <div className="flex items-center justify-between mb-2">
                                <h4 className="font-medium text-white">{finding.type}</h4>
                                <span className={`px-2 py-1 text-xs rounded ${
                                  finding.severity === 'critical' ? 'bg-red-500/20 text-red-400' :
                                  finding.severity === 'high' ? 'bg-orange-500/20 text-orange-400' :
                                  finding.severity === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                                  'bg-blue-500/20 text-blue-400'
                                }`}>
                                  {finding.severity}
                                </span>
                              </div>
                              <p className="text-white/70 text-sm mb-2">{finding.description}</p>
                              <p className="text-white/50 text-xs">Line {finding.line}</p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </LiquidGlassCard>
                  ))}
                </div>
              </LiquidGlassScrollbar>
            </div>
          )}

          {activeTab === 'plugins' && (
            <div className="space-y-6">
              <h1 className="text-3xl font-bold text-white">Security Plugins</h1>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  { name: 'SQL Injection Scanner', status: 'active', description: 'Detects SQL injection vulnerabilities' },
                  { name: 'XSS Scanner', status: 'active', description: 'Cross-site scripting detection' },
                  { name: 'CSRF Scanner', status: 'inactive', description: 'Cross-site request forgery detection' },
                  { name: 'Path Traversal Scanner', status: 'active', description: 'Directory traversal vulnerability detection' },
                  { name: 'Command Injection Scanner', status: 'active', description: 'OS command injection detection' },
                  { name: 'Secrets Scanner', status: 'active', description: 'Hardcoded secrets and API keys detection' }
                ].map((plugin, index) => (
                  <LiquidGlassCard key={index} className="p-6">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <h3 className="font-semibold text-white">{plugin.name}</h3>
                        <span className={`px-2 py-1 text-xs rounded ${
                          plugin.status === 'active' ? 'bg-green-500/20 text-green-400' : 'bg-gray-500/20 text-gray-400'
                        }`}>
                          {plugin.status}
                        </span>
                      </div>
                      <p className="text-white/60 text-sm">{plugin.description}</p>
                      <LiquidGlassButton size="sm" variant="secondary" className="w-full">
                        {plugin.status === 'active' ? 'Configure' : 'Enable'}
                      </LiquidGlassButton>
                    </div>
                  </LiquidGlassCard>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Result Detail Modal */}
      <LiquidGlassModal
        isOpen={showModal}
        onClose={() => setShowModal(false)}
        title={selectedResult ? `Scan Results: ${selectedResult.fileName}` : ''}
        size="xl"
      >
        {selectedResult && (
          <div className="space-y-6">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-white/60">File:</span>
                <span className="text-white ml-2">{selectedResult.fileName}</span>
              </div>
              <div>
                <span className="text-white/60">Scanned:</span>
                <span className="text-white ml-2">{selectedResult.timestamp.toLocaleString()}</span>
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="text-lg font-semibold text-white">Findings</h4>
              {selectedResult.findings.map((finding, index) => (
                <LiquidGlassCard key={index} className="p-4">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h5 className="font-medium text-white">{finding.type}</h5>
                      <span className={`px-2 py-1 text-xs rounded ${
                        finding.severity === 'critical' ? 'bg-red-500/20 text-red-400' :
                        finding.severity === 'high' ? 'bg-orange-500/20 text-orange-400' :
                        finding.severity === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                        'bg-blue-500/20 text-blue-400'
                      }`}>
                        {finding.severity}
                      </span>
                    </div>
                    <p className="text-white/70 text-sm">{finding.description}</p>
                    <LiquidGlassCodeViewer
                      code={finding.evidence}
                      language="javascript"
                      maxHeight="200px"
                      highlightedLines={[1]}
                    />
                  </div>
                </LiquidGlassCard>
              ))}
            </div>
          </div>
        )}
      </LiquidGlassModal>
    </div>
  );
};

export default ByteGuardXDashboard;
