// LiquidGlassWrapper - Drop-in replacement for existing glassmorphism elements
import React, { useRef, useEffect, useState } from 'react';
import { LiquidGlassRenderer } from '../shaders/LiquidGlassShader';

const LiquidGlassWrapper = ({ 
  children, 
  className = '', 
  intensity = 1.0,
  enableShader = true,
  fallbackClass = 'backdrop-blur-md bg-black/20 border border-white/15',
  ...props 
}) => {
  const containerRef = useRef(null);
  const canvasRef = useRef(null);
  const rendererRef = useRef(null);
  const [isHovered, setIsHovered] = useState(false);
  const [shaderSupported, setShaderSupported] = useState(true);

  useEffect(() => {
    if (!enableShader || !canvasRef.current) return;

    // Initialize WebGL renderer
    const renderer = new LiquidGlassRenderer(canvasRef.current);
    
    if (!renderer || !renderer.gl) {
      setShaderSupported(false);
      return;
    }
    
    rendererRef.current = renderer;
    
    // Handle resize
    const handleResize = () => {
      if (containerRef.current && canvasRef.current && renderer) {
        const rect = containerRef.current.getBoundingClientRect();
        renderer.resize(rect.width, rect.height);
      }
    };
    
    // Initial resize
    handleResize();
    window.addEventListener('resize', handleResize);
    
    // Handle mouse movement
    const handleMouseMove = (e) => {
      if (renderer) {
        renderer.updateMouse(e.clientX, e.clientY);
      }
    };
    
    const container = containerRef.current;
    if (container) {
      container.addEventListener('mousemove', handleMouseMove);
    }
    
    return () => {
      window.removeEventListener('resize', handleResize);
      if (container) {
        container.removeEventListener('mousemove', handleMouseMove);
      }
      if (renderer) {
        renderer.destroy();
      }
    };
  }, [enableShader]);

  useEffect(() => {
    if (rendererRef.current) {
      rendererRef.current.setHover(isHovered);
    }
  }, [isHovered]);

  const handleMouseEnter = () => {
    setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
  };

  // If shader not supported or disabled, use fallback
  if (!enableShader || !shaderSupported) {
    return (
      <div 
        ref={containerRef}
        className={`${fallbackClass} ${className}`}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        {...props}
      >
        {children}
      </div>
    );
  }

  return (
    <div 
      ref={containerRef}
      className={`relative overflow-hidden ${className}`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      {...props}
    >
      {/* WebGL Canvas Background */}
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full pointer-events-none"
        style={{ 
          mixBlendMode: 'normal',
          zIndex: 0
        }}
      />
      
      {/* Content Layer */}
      <div className="relative z-10 w-full h-full">
        {children}
      </div>
      
      {/* Subtle border overlay */}
      <div className="absolute inset-0 border border-white/15 pointer-events-none" />
      
      {/* Hover accent overlay */}
      {isHovered && (
        <div className="absolute inset-0 bg-cyan-400/5 pointer-events-none transition-opacity duration-200" />
      )}
    </div>
  );
};

export default LiquidGlassWrapper;
