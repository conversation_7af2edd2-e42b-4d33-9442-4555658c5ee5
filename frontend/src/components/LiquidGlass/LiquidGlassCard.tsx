import React, { useState, useRef, useEffect } from 'react';
import { LiquidGlassCanvas, LiquidGlassPlane, useGPUPerformance, FallbackGlass } from './LiquidGlassCore';

interface LiquidGlassCardProps {
  children: React.ReactNode;
  className?: string;
  hoverable?: boolean;
  clickable?: boolean;
  onClick?: () => void;
  depth?: number;
  glowIntensity?: number;
}

const LiquidGlassCard: React.FC<LiquidGlassCardProps> = ({
  children,
  className = '',
  hoverable = true,
  clickable = false,
  onClick,
  depth = 1,
  glowIntensity = 0.3
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [mousePosition, setMousePosition] = useState({ x: 0.5, y: 0.5 });
  const cardRef = useRef<HTMLDivElement>(null);
  const performanceLevel = useGPUPerformance();

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!cardRef.current || !hoverable) return;
    
    const rect = cardRef.current.getBoundingClientRect();
    const x = (e.clientX - rect.left) / rect.width;
    const y = (e.clientY - rect.top) / rect.height;
    
    setMousePosition({ x, y });
  };

  const handleMouseEnter = () => {
    if (hoverable) setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setMousePosition({ x: 0.5, y: 0.5 });
  };

  const handleClick = () => {
    if (clickable && onClick) {
      onClick();
    }
  };

  const baseClasses = `
    relative overflow-hidden transition-all duration-300 ease-out
    border border-white/15 backdrop-blur-sm
    ${clickable ? 'cursor-pointer' : ''}
    ${isHovered ? 'border-cyan-400/30' : ''}
    ${className}
  `;

  // High-performance liquid glass version
  if (performanceLevel === 'high') {
    return (
      <div
        ref={cardRef}
        className={baseClasses}
        onMouseMove={handleMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
      >
        <LiquidGlassCanvas className="absolute inset-0">
          {/* Main glass layer */}
          <LiquidGlassPlane
            width={2}
            height={2}
            position={[0, 0, -0.1]}
            intensity={isHovered ? 1.2 : 0.6}
            distortion={0.08}
            hovered={isHovered}
            glowStrength={glowIntensity}
            opacity={0.25}
          />
          
          {/* Depth layers for realism */}
          {depth > 1 && (
            <LiquidGlassPlane
              width={1.8}
              height={1.8}
              position={[0, 0, -0.2]}
              intensity={isHovered ? 0.8 : 0.4}
              distortion={0.05}
              hovered={isHovered}
              glowStrength={glowIntensity * 0.6}
              opacity={0.15}
            />
          )}
          
          {depth > 2 && (
            <LiquidGlassPlane
              width={1.6}
              height={1.6}
              position={[0, 0, -0.3]}
              intensity={isHovered ? 0.6 : 0.3}
              distortion={0.03}
              hovered={isHovered}
              glowStrength={glowIntensity * 0.4}
              opacity={0.1}
            />
          )}
        </LiquidGlassCanvas>

        {/* Content overlay */}
        <div className="relative z-10 p-6">
          {children}
        </div>

        {/* Interactive light reflection */}
        {isHovered && (
          <div 
            className="absolute inset-0 opacity-20 pointer-events-none"
            style={{
              background: `radial-gradient(circle at ${mousePosition.x * 100}% ${mousePosition.y * 100}%, 
                rgba(0, 255, 255, 0.3) 0%, 
                rgba(0, 255, 255, 0.1) 30%, 
                transparent 60%)`
            }}
          />
        )}

        {/* Edge glow on hover */}
        {isHovered && (
          <div className="absolute inset-0 border border-cyan-400/50 rounded-inherit animate-pulse" />
        )}
      </div>
    );
  }

  // Fallback for medium/low performance
  return (
    <FallbackGlass 
      className={`${baseClasses} ${isHovered ? 'bg-black/30' : 'bg-black/20'}`}
    >
      <div
        ref={cardRef}
        className="relative z-10 p-6 w-full h-full"
        onMouseMove={handleMouseMove}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
      >
        {children}
        
        {/* Simple hover effect for fallback */}
        {isHovered && (
          <div 
            className="absolute inset-0 opacity-10 pointer-events-none transition-opacity duration-300"
            style={{
              background: `radial-gradient(circle at ${mousePosition.x * 100}% ${mousePosition.y * 100}%, 
                rgba(0, 255, 255, 0.5) 0%, 
                transparent 50%)`
            }}
          />
        )}
      </div>
    </FallbackGlass>
  );
};

export default LiquidGlassCard;
