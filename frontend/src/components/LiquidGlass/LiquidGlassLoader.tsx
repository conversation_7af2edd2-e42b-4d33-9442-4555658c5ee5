import React, { useRef, useMemo } from 'react';
import { Canvas, useFrame } from '@react-three/fiber';
import { ShaderMaterial, Vector3, SphereGeometry } from 'three';
import * as THREE from 'three';
import { useGPUPerformance } from './LiquidGlassCore';

// Liquid Glass Orb Shader
const liquidOrbVertexShader = `
  varying vec2 vUv;
  varying vec3 vPosition;
  varying vec3 vNormal;
  
  void main() {
    vUv = uv;
    vPosition = position;
    vNormal = normal;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`;

const liquidOrbFragmentShader = `
  uniform float uTime;
  uniform vec3 uColor;
  uniform float uIntensity;
  uniform float uSpeed;
  uniform float uComplexity;
  
  varying vec2 vUv;
  varying vec3 vPosition;
  varying vec3 vNormal;
  
  // Noise functions
  float random(vec3 st) {
    return fract(sin(dot(st.xyz, vec3(12.9898, 78.233, 45.164))) * 43758.5453123);
  }
  
  float noise(vec3 st) {
    vec3 i = floor(st);
    vec3 f = fract(st);
    
    float a = random(i);
    float b = random(i + vec3(1.0, 0.0, 0.0));
    float c = random(i + vec3(0.0, 1.0, 0.0));
    float d = random(i + vec3(1.0, 1.0, 0.0));
    float e = random(i + vec3(0.0, 0.0, 1.0));
    float f2 = random(i + vec3(1.0, 0.0, 1.0));
    float g = random(i + vec3(0.0, 1.0, 1.0));
    float h = random(i + vec3(1.0, 1.0, 1.0));
    
    vec3 u = f * f * (3.0 - 2.0 * f);
    
    return mix(
      mix(mix(a, b, u.x), mix(c, d, u.x), u.y),
      mix(mix(e, f2, u.x), mix(g, h, u.x), u.y),
      u.z
    );
  }
  
  // Fractal noise
  float fbm(vec3 st) {
    float value = 0.0;
    float amplitude = 0.5;
    float frequency = 1.0;
    
    for (int i = 0; i < 4; i++) {
      value += amplitude * noise(st * frequency);
      amplitude *= 0.5;
      frequency *= 2.0;
    }
    
    return value;
  }
  
  void main() {
    vec3 pos = vPosition;
    float time = uTime * uSpeed;
    
    // Create flowing liquid effect
    float flow1 = fbm(pos * uComplexity + vec3(time * 0.5, time * 0.3, time * 0.7));
    float flow2 = fbm(pos * uComplexity * 1.5 + vec3(time * 0.7, time * 0.5, time * 0.3));
    float flow3 = fbm(pos * uComplexity * 0.8 + vec3(time * 0.3, time * 0.7, time * 0.5));
    
    // Combine flows
    float liquidPattern = (flow1 + flow2 * 0.7 + flow3 * 0.5) / 2.2;
    
    // Create pulsing effect
    float pulse = sin(time * 2.0) * 0.5 + 0.5;
    liquidPattern *= (0.7 + pulse * 0.3);
    
    // Add surface tension effect
    float fresnel = pow(1.0 - dot(normalize(vNormal), vec3(0.0, 0.0, 1.0)), 2.0);
    float surface = fresnel * 0.8 + 0.2;
    
    // Final color
    vec3 color = uColor * liquidPattern * surface * uIntensity;
    
    // Add inner glow
    float innerGlow = smoothstep(0.0, 1.0, 1.0 - length(vPosition));
    color += uColor * innerGlow * 0.3;
    
    // Add rim lighting
    float rim = 1.0 - dot(normalize(vNormal), vec3(0.0, 0.0, 1.0));
    color += uColor * pow(rim, 3.0) * 0.5;
    
    gl_FragColor = vec4(color, liquidPattern * surface);
  }
`;

interface LiquidOrbProps {
  size?: number;
  intensity?: number;
  speed?: number;
  complexity?: number;
  color?: [number, number, number];
}

const LiquidOrb: React.FC<LiquidOrbProps> = ({
  size = 1,
  intensity = 1.5,
  speed = 1.0,
  complexity = 2.0,
  color = [0.0, 1.0, 1.0]
}) => {
  const meshRef = useRef<THREE.Mesh>(null);
  const materialRef = useRef<ShaderMaterial>(null);

  const uniforms = useMemo(() => ({
    uTime: { value: 0 },
    uColor: { value: new Vector3(...color) },
    uIntensity: { value: intensity },
    uSpeed: { value: speed },
    uComplexity: { value: complexity }
  }), [intensity, speed, complexity, color]);

  useFrame((state) => {
    if (materialRef.current) {
      materialRef.current.uniforms.uTime.value = state.clock.elapsedTime;
    }
    
    if (meshRef.current) {
      // Gentle rotation
      meshRef.current.rotation.x += 0.005;
      meshRef.current.rotation.y += 0.007;
      
      // Subtle floating animation
      meshRef.current.position.y = Math.sin(state.clock.elapsedTime * 0.8) * 0.1;
    }
  });

  return (
    <mesh ref={meshRef} scale={size}>
      <sphereGeometry args={[1, 64, 64]} />
      <shaderMaterial
        ref={materialRef}
        vertexShader={liquidOrbVertexShader}
        fragmentShader={liquidOrbFragmentShader}
        uniforms={uniforms}
        transparent
        side={THREE.DoubleSide}
      />
    </mesh>
  );
};

interface LiquidGlassLoaderProps {
  size?: 'sm' | 'md' | 'lg';
  text?: string;
  className?: string;
  intensity?: number;
  speed?: number;
}

const LiquidGlassLoader: React.FC<LiquidGlassLoaderProps> = ({
  size = 'md',
  text,
  className = '',
  intensity = 1.5,
  speed = 1.0
}) => {
  const performanceLevel = useGPUPerformance();

  const sizeConfig = {
    sm: { orbSize: 0.8, canvasHeight: 80 },
    md: { orbSize: 1.2, canvasHeight: 120 },
    lg: { orbSize: 1.6, canvasHeight: 160 }
  };

  const config = sizeConfig[size];

  // High-performance liquid glass version
  if (performanceLevel === 'high') {
    return (
      <div className={`flex flex-col items-center justify-center ${className}`}>
        <div 
          className="relative"
          style={{ height: config.canvasHeight }}
        >
          <Canvas
            camera={{ position: [0, 0, 3], fov: 45 }}
            gl={{ 
              alpha: true, 
              antialias: true,
              powerPreference: "high-performance"
            }}
          >
            <ambientLight intensity={0.3} />
            <pointLight position={[5, 5, 5]} intensity={0.7} />
            <pointLight position={[-5, -5, 5]} intensity={0.3} color="#00ffff" />
            
            <LiquidOrb
              size={config.orbSize}
              intensity={intensity}
              speed={speed}
              complexity={2.5}
              color={[0.0, 1.0, 1.0]}
            />
          </Canvas>
        </div>
        
        {text && (
          <p className="mt-4 text-white/80 text-center animate-pulse">
            {text}
          </p>
        )}
      </div>
    );
  }

  // Fallback for medium/low performance
  return (
    <div className={`flex flex-col items-center justify-center ${className}`}>
      <div className="relative" style={{ height: config.canvasHeight }}>
        {/* Simple animated circle */}
        <div className="flex items-center justify-center h-full">
          <div 
            className="border-4 border-cyan-400/30 border-t-cyan-400 rounded-full animate-spin"
            style={{ 
              width: config.orbSize * 40, 
              height: config.orbSize * 40 
            }}
          />
          
          {/* Inner glow effect */}
          <div 
            className="absolute border-2 border-cyan-400/20 rounded-full animate-pulse"
            style={{ 
              width: config.orbSize * 60, 
              height: config.orbSize * 60 
            }}
          />
        </div>
      </div>
      
      {text && (
        <p className="mt-4 text-white/80 text-center animate-pulse">
          {text}
        </p>
      )}
    </div>
  );
};

export default LiquidGlassLoader;
