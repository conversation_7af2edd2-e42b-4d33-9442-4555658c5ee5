import React, { useState, useEffect } from 'react';
import { LiquidGlassCanvas, LiquidGlassPlane, useGPUPerformance, FallbackGlass } from './LiquidGlassCore';
import LiquidGlassButton from './LiquidGlassButton';

interface NavItem {
  id: string;
  label: string;
  icon?: React.ReactNode;
  onClick?: () => void;
  active?: boolean;
  badge?: string | number;
}

interface LiquidGlassNavbarProps {
  title?: string;
  logo?: React.ReactNode;
  items: NavItem[];
  actions?: React.ReactNode;
  className?: string;
  fixed?: boolean;
  transparent?: boolean;
}

const LiquidGlassNavbar: React.FC<LiquidGlassNavbarProps> = ({
  title,
  logo,
  items,
  actions,
  className = '',
  fixed = true,
  transparent = true
}) => {
  const [scrolled, setScrolled] = useState(false);
  const [activeItem, setActiveItem] = useState<string | null>(
    items.find(item => item.active)?.id || null
  );
  const performanceLevel = useGPUPerformance();

  useEffect(() => {
    if (!fixed) return;

    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, [fixed]);

  const handleItemClick = (item: NavItem) => {
    setActiveItem(item.id);
    item.onClick?.();
  };

  const navClasses = `
    w-full transition-all duration-300 ease-out z-50
    ${fixed ? 'fixed top-0 left-0' : 'relative'}
    ${scrolled && !transparent ? 'backdrop-blur-lg' : ''}
    ${className}
  `;

  // High-performance liquid glass version
  if (performanceLevel === 'high') {
    return (
      <nav className={navClasses}>
        <div className="relative overflow-hidden border-b border-white/10">
          <LiquidGlassCanvas className="absolute inset-0">
            {/* Main navbar glass */}
            <LiquidGlassPlane
              width={4}
              height={1}
              position={[0, 0, -0.1]}
              intensity={scrolled ? 0.8 : 0.5}
              distortion={0.03}
              hovered={false}
              glowStrength={0.2}
              opacity={transparent ? 0.2 : 0.4}
            />
            
            {/* Flowing background layer */}
            <LiquidGlassPlane
              width={3.8}
              height={0.8}
              position={[0, 0, -0.2]}
              intensity={0.3}
              distortion={0.02}
              hovered={false}
              glowStrength={0.1}
              opacity={0.1}
            />
          </LiquidGlassCanvas>

          {/* Navbar content */}
          <div className="relative z-10 px-6 py-4">
            <div className="flex items-center justify-between">
              {/* Left section - Logo/Title */}
              <div className="flex items-center space-x-4">
                {logo && (
                  <div className="flex-shrink-0">
                    {logo}
                  </div>
                )}
                {title && (
                  <h1 className="text-xl font-bold text-white">
                    {title}
                  </h1>
                )}
              </div>

              {/* Center section - Navigation items */}
              <div className="hidden md:flex items-center space-x-1">
                {items.map((item) => (
                  <button
                    key={item.id}
                    onClick={() => handleItemClick(item)}
                    className={`
                      relative px-4 py-2 text-sm font-medium transition-all duration-200
                      ${activeItem === item.id ? 'text-white' : 'text-white/70 hover:text-white'}
                      flex items-center space-x-2
                    `}
                  >
                    {/* Active item liquid glass background */}
                    {activeItem === item.id && (
                      <div className="absolute inset-0 overflow-hidden rounded-lg">
                        <LiquidGlassCanvas className="absolute inset-0">
                          <LiquidGlassPlane
                            width={1.2}
                            height={0.6}
                            position={[0, 0, -0.1]}
                            intensity={1.0}
                            distortion={0.06}
                            hovered={true}
                            glowStrength={0.5}
                            opacity={0.3}
                          />
                        </LiquidGlassCanvas>
                      </div>
                    )}

                    {/* Item content */}
                    <div className="relative z-10 flex items-center space-x-2">
                      {item.icon && (
                        <span className="w-4 h-4">
                          {item.icon}
                        </span>
                      )}
                      <span>{item.label}</span>
                      {item.badge && (
                        <span className="px-2 py-1 text-xs bg-cyan-400 text-black rounded-full">
                          {item.badge}
                        </span>
                      )}
                    </div>

                    {/* Hover effect */}
                    {activeItem !== item.id && (
                      <div className="absolute inset-0 bg-white/5 opacity-0 hover:opacity-100 transition-opacity duration-200 rounded-lg" />
                    )}
                  </button>
                ))}
              </div>

              {/* Right section - Actions */}
              <div className="flex items-center space-x-3">
                {actions}
              </div>
            </div>
          </div>

          {/* Progress indicator for active scanning */}
          <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent animate-pulse opacity-0" />
        </div>
      </nav>
    );
  }

  // Fallback for medium/low performance
  return (
    <nav className={navClasses}>
      <FallbackGlass className={`
        border-b border-white/10 
        ${transparent ? 'bg-black/20' : 'bg-black/40'}
        ${scrolled ? 'bg-black/60' : ''}
        transition-all duration-300
      `}>
        <div className="relative z-10 px-6 py-4">
          <div className="flex items-center justify-between">
            {/* Left section - Logo/Title */}
            <div className="flex items-center space-x-4">
              {logo && (
                <div className="flex-shrink-0">
                  {logo}
                </div>
              )}
              {title && (
                <h1 className="text-xl font-bold text-white">
                  {title}
                </h1>
              )}
            </div>

            {/* Center section - Navigation items */}
            <div className="hidden md:flex items-center space-x-1">
              {items.map((item) => (
                <button
                  key={item.id}
                  onClick={() => handleItemClick(item)}
                  className={`
                    relative px-4 py-2 text-sm font-medium transition-all duration-200 rounded-lg
                    ${activeItem === item.id 
                      ? 'text-white bg-white/10' 
                      : 'text-white/70 hover:text-white hover:bg-white/5'
                    }
                    flex items-center space-x-2
                  `}
                >
                  {item.icon && (
                    <span className="w-4 h-4">
                      {item.icon}
                    </span>
                  )}
                  <span>{item.label}</span>
                  {item.badge && (
                    <span className="px-2 py-1 text-xs bg-cyan-400 text-black rounded-full">
                      {item.badge}
                    </span>
                  )}
                </button>
              ))}
            </div>

            {/* Right section - Actions */}
            <div className="flex items-center space-x-3">
              {actions}
            </div>
          </div>
        </div>
      </FallbackGlass>
    </nav>
  );
};

export default LiquidGlassNavbar;
