import React, { useRef, useMemo, useEffect } from 'react';
import { <PERSON><PERSON>, useFrame, useThree } from '@react-three/fiber';
import { ShaderMaterial, Vector2, Vector3, Clock } from 'three';
import * as THREE from 'three';

// Core Liquid Glass Shader
const liquidGlassVertexShader = `
  varying vec2 vUv;
  varying vec3 vPosition;
  varying vec3 vNormal;
  
  void main() {
    vUv = uv;
    vPosition = position;
    vNormal = normal;
    gl_Position = projectionMatrix * modelViewMatrix * vec4(position, 1.0);
  }
`;

const liquidGlassFragmentShader = `
  uniform float uTime;
  uniform vec2 uResolution;
  uniform vec2 uMouse;
  uniform float uIntensity;
  uniform float uDistortion;
  uniform vec3 uColor;
  uniform float uOpacity;
  uniform bool uHovered;
  uniform float uGlowStrength;
  
  varying vec2 vUv;
  varying vec3 vPosition;
  varying vec3 vNormal;
  
  // Noise functions
  float random(vec2 st) {
    return fract(sin(dot(st.xy, vec2(12.9898,78.233))) * 43758.5453123);
  }
  
  float noise(vec2 st) {
    vec2 i = floor(st);
    vec2 f = fract(st);
    float a = random(i);
    float b = random(i + vec2(1.0, 0.0));
    float c = random(i + vec2(0.0, 1.0));
    float d = random(i + vec2(1.0, 1.0));
    vec2 u = f * f * (3.0 - 2.0 * f);
    return mix(a, b, u.x) + (c - a)* u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
  }
  
  // Liquid distortion
  vec2 liquidDistortion(vec2 uv, float time, float intensity) {
    float wave1 = sin(uv.x * 8.0 + time * 2.0) * 0.02 * intensity;
    float wave2 = cos(uv.y * 6.0 + time * 1.5) * 0.015 * intensity;
    float wave3 = sin((uv.x + uv.y) * 4.0 + time * 3.0) * 0.01 * intensity;
    
    vec2 distortion = vec2(wave1 + wave3, wave2 + wave3);
    
    // Add mouse interaction
    vec2 mouseInfluence = (uMouse - uv) * 0.1;
    float mouseDistance = length(mouseInfluence);
    if (mouseDistance < 0.3) {
      float ripple = sin(mouseDistance * 20.0 - time * 8.0) * 0.02;
      distortion += normalize(mouseInfluence) * ripple * (0.3 - mouseDistance);
    }
    
    return distortion;
  }
  
  // Glass refraction effect
  vec3 glassRefraction(vec2 uv, vec2 distortion) {
    vec2 refractedUV = uv + distortion * uDistortion;
    
    // Create depth illusion
    float depth = noise(refractedUV * 10.0 + uTime * 0.5) * 0.1;
    vec3 refractedColor = vec3(0.0, 0.8, 1.0) * (0.3 + depth);
    
    // Add internal reflections
    float fresnel = pow(1.0 - dot(normalize(vNormal), vec3(0.0, 0.0, 1.0)), 2.0);
    vec3 reflection = vec3(0.1, 0.3, 0.5) * fresnel;
    
    return refractedColor + reflection;
  }
  
  void main() {
    vec2 uv = vUv;
    float time = uTime;
    float intensity = uIntensity;
    
    if (uHovered) {
      intensity *= 2.0;
      time *= 1.5;
    }
    
    // Apply liquid distortion
    vec2 distortion = liquidDistortion(uv, time, intensity);
    vec2 distortedUV = uv + distortion;
    
    // Base glass effect
    vec3 glassColor = glassRefraction(distortedUV, distortion);
    
    // Add noise texture for glass surface
    float surfaceNoise = noise(distortedUV * 20.0 + time * 0.2) * 0.1;
    glassColor += vec3(surfaceNoise);
    
    // Cyan glow on hover
    if (uHovered) {
      float glow = smoothstep(0.0, 1.0, 1.0 - length(uv - vec2(0.5)));
      glassColor += uColor * glow * uGlowStrength;
    }
    
    // Edge highlighting
    float edge = 1.0 - smoothstep(0.0, 0.1, min(min(uv.x, 1.0 - uv.x), min(uv.y, 1.0 - uv.y)));
    glassColor += vec3(0.2, 0.4, 0.6) * edge * 0.3;
    
    gl_FragColor = vec4(glassColor, uOpacity);
  }
`;

interface LiquidGlassMaterialProps {
  intensity?: number;
  distortion?: number;
  color?: [number, number, number];
  opacity?: number;
  hovered?: boolean;
  glowStrength?: number;
}

export const LiquidGlassMaterial: React.FC<LiquidGlassMaterialProps> = ({
  intensity = 1.0,
  distortion = 0.1,
  color = [0.0, 1.0, 1.0],
  opacity = 0.3,
  hovered = false,
  glowStrength = 0.5
}) => {
  const materialRef = useRef<ShaderMaterial>(null);
  const { size, mouse } = useThree();
  const clock = useMemo(() => new Clock(), []);

  const uniforms = useMemo(() => ({
    uTime: { value: 0 },
    uResolution: { value: new Vector2(size.width, size.height) },
    uMouse: { value: new Vector2(0.5, 0.5) },
    uIntensity: { value: intensity },
    uDistortion: { value: distortion },
    uColor: { value: new Vector3(...color) },
    uOpacity: { value: opacity },
    uHovered: { value: hovered },
    uGlowStrength: { value: glowStrength }
  }), [size, intensity, distortion, color, opacity, hovered, glowStrength]);

  useFrame(() => {
    if (materialRef.current) {
      materialRef.current.uniforms.uTime.value = clock.getElapsedTime();
      materialRef.current.uniforms.uMouse.value.set(
        (mouse.x + 1) / 2,
        (mouse.y + 1) / 2
      );
      materialRef.current.uniforms.uHovered.value = hovered;
    }
  });

  useEffect(() => {
    if (materialRef.current) {
      materialRef.current.uniforms.uResolution.value.set(size.width, size.height);
    }
  }, [size]);

  return (
    <shaderMaterial
      ref={materialRef}
      vertexShader={liquidGlassVertexShader}
      fragmentShader={liquidGlassFragmentShader}
      uniforms={uniforms}
      transparent
      side={THREE.DoubleSide}
    />
  );
};

interface LiquidGlassPlaneProps extends LiquidGlassMaterialProps {
  width?: number;
  height?: number;
  position?: [number, number, number];
}

export const LiquidGlassPlane: React.FC<LiquidGlassPlaneProps> = ({
  width = 2,
  height = 2,
  position = [0, 0, 0],
  ...materialProps
}) => {
  return (
    <mesh position={position}>
      <planeGeometry args={[width, height, 32, 32]} />
      <LiquidGlassMaterial {...materialProps} />
    </mesh>
  );
};

interface LiquidGlassCanvasProps {
  children: React.ReactNode;
  className?: string;
}

export const LiquidGlassCanvas: React.FC<LiquidGlassCanvasProps> = ({
  children,
  className = ""
}) => {
  return (
    <div className={`relative ${className}`}>
      <Canvas
        className="absolute inset-0 pointer-events-none"
        camera={{ position: [0, 0, 5], fov: 45 }}
        gl={{ 
          alpha: true, 
          antialias: true,
          powerPreference: "high-performance"
        }}
      >
        <ambientLight intensity={0.2} />
        <pointLight position={[10, 10, 10]} intensity={0.5} />
        {children}
      </Canvas>
    </div>
  );
};

// Performance monitoring hook
export const useGPUPerformance = () => {
  const [performanceLevel, setPerformanceLevel] = React.useState<'high' | 'medium' | 'low'>('high');
  
  useEffect(() => {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2') || canvas.getContext('webgl');
    
    if (!gl) {
      setPerformanceLevel('low');
      return;
    }
    
    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
    if (debugInfo) {
      const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
      
      // Basic GPU detection logic
      if (renderer.includes('Intel') && !renderer.includes('Iris')) {
        setPerformanceLevel('low');
      } else if (renderer.includes('GTX') || renderer.includes('RTX') || renderer.includes('Radeon')) {
        setPerformanceLevel('high');
      } else {
        setPerformanceLevel('medium');
      }
    }
  }, []);
  
  return performanceLevel;
};

// Fallback glassmorphism for low-performance devices
export const FallbackGlass: React.FC<{ children: React.ReactNode; className?: string }> = ({
  children,
  className = ""
}) => {
  return (
    <div className={`
      backdrop-blur-md bg-black/20 border border-white/15
      ${className}
    `}>
      {children}
    </div>
  );
};

export default LiquidGlassMaterial;
