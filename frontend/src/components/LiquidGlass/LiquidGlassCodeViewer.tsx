import React, { useState, useRef, useEffect } from 'react';
import { LiquidGlassCanvas, LiquidGlassPlane, useGPUPerformance, FallbackGlass } from './LiquidGlassCore';
import LiquidGlassScrollbar from './LiquidGlassScrollbar';

interface CodeLine {
  number: number;
  content: string;
  highlighted?: boolean;
  severity?: 'low' | 'medium' | 'high' | 'critical';
  annotation?: string;
}

interface LiquidGlassCodeViewerProps {
  code: string | CodeLine[];
  language?: string;
  theme?: 'dark' | 'darker';
  showLineNumbers?: boolean;
  highlightedLines?: number[];
  className?: string;
  maxHeight?: string;
  readOnly?: boolean;
  onLineClick?: (lineNumber: number) => void;
}

const LiquidGlassCodeViewer: React.FC<LiquidGlassCodeViewerProps> = ({
  code,
  language = 'javascript',
  theme = 'darker',
  showLineNumbers = true,
  highlightedLines = [],
  className = '',
  maxHeight = '500px',
  readOnly = true,
  onLineClick
}) => {
  const [hoveredLine, setHoveredLine] = useState<number | null>(null);
  const [selectedLine, setSelectedLine] = useState<number | null>(null);
  const codeRef = useRef<HTMLPreElement>(null);
  const performanceLevel = useGPUPerformance();

  // Process code into lines
  const codeLines: CodeLine[] = React.useMemo(() => {
    if (Array.isArray(code)) {
      return code;
    }
    
    return code.split('\n').map((content, index) => ({
      number: index + 1,
      content,
      highlighted: highlightedLines.includes(index + 1)
    }));
  }, [code, highlightedLines]);

  // Simple syntax highlighting
  const highlightSyntax = (content: string, lang: string): string => {
    if (!content.trim()) return content;

    let highlighted = content;

    // Keywords
    const keywords = {
      javascript: ['const', 'let', 'var', 'function', 'return', 'if', 'else', 'for', 'while', 'class', 'import', 'export'],
      python: ['def', 'class', 'import', 'from', 'return', 'if', 'else', 'elif', 'for', 'while', 'try', 'except'],
      java: ['public', 'private', 'class', 'interface', 'extends', 'implements', 'return', 'if', 'else', 'for', 'while'],
      go: ['func', 'var', 'const', 'type', 'struct', 'interface', 'return', 'if', 'else', 'for', 'range']
    };

    const langKeywords = keywords[lang as keyof typeof keywords] || keywords.javascript;
    
    // Highlight keywords
    langKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword}\\b`, 'g');
      highlighted = highlighted.replace(regex, `<span class="text-cyan-400 font-medium">${keyword}</span>`);
    });

    // Highlight strings
    highlighted = highlighted.replace(
      /(["'`])((?:\\.|(?!\1)[^\\])*?)\1/g,
      '<span class="text-green-400">$1$2$1</span>'
    );

    // Highlight comments
    highlighted = highlighted.replace(
      /(\/\/.*$|\/\*[\s\S]*?\*\/|#.*$)/gm,
      '<span class="text-gray-500 italic">$1</span>'
    );

    // Highlight numbers
    highlighted = highlighted.replace(
      /\b(\d+\.?\d*)\b/g,
      '<span class="text-yellow-400">$1</span>'
    );

    return highlighted;
  };

  const getSeverityColor = (severity?: string) => {
    switch (severity) {
      case 'critical': return 'border-red-500 bg-red-500/10';
      case 'high': return 'border-orange-500 bg-orange-500/10';
      case 'medium': return 'border-yellow-500 bg-yellow-500/10';
      case 'low': return 'border-blue-500 bg-blue-500/10';
      default: return 'border-cyan-400/30 bg-cyan-400/5';
    }
  };

  const handleLineClick = (lineNumber: number) => {
    setSelectedLine(lineNumber);
    onLineClick?.(lineNumber);
  };

  // High-performance liquid glass version
  if (performanceLevel === 'high') {
    return (
      <div className={`relative overflow-hidden border border-white/15 ${className}`}>
        <LiquidGlassCanvas className="absolute inset-0">
          {/* Main code viewer glass */}
          <LiquidGlassPlane
            width={3}
            height={2.5}
            position={[0, 0, -0.1]}
            intensity={0.4}
            distortion={0.02}
            hovered={false}
            glowStrength={0.1}
            opacity={0.15}
          />
          
          {/* Background depth layer */}
          <LiquidGlassPlane
            width={2.8}
            height={2.3}
            position={[0, 0, -0.2]}
            intensity={0.2}
            distortion={0.01}
            hovered={false}
            glowStrength={0.05}
            opacity={0.08}
          />
        </LiquidGlassCanvas>

        {/* Code content */}
        <div className="relative z-10 bg-black/20">
          <LiquidGlassScrollbar maxHeight={maxHeight} className="font-mono text-sm">
            <div className="flex">
              {/* Line numbers */}
              {showLineNumbers && (
                <div className="flex-shrink-0 px-4 py-4 text-white/40 bg-black/20 border-r border-white/10 select-none">
                  {codeLines.map((line) => (
                    <div
                      key={line.number}
                      className={`
                        h-6 flex items-center justify-end cursor-pointer transition-colors
                        ${selectedLine === line.number ? 'text-cyan-400' : ''}
                        ${hoveredLine === line.number ? 'text-white/60' : ''}
                      `}
                      onClick={() => handleLineClick(line.number)}
                      onMouseEnter={() => setHoveredLine(line.number)}
                      onMouseLeave={() => setHoveredLine(null)}
                    >
                      {line.number}
                    </div>
                  ))}
                </div>
              )}

              {/* Code content */}
              <div className="flex-1 relative">
                <pre ref={codeRef} className="p-4 text-white/90 leading-6">
                  {codeLines.map((line) => (
                    <div
                      key={line.number}
                      className={`
                        relative h-6 flex items-center cursor-pointer group
                        ${line.highlighted ? getSeverityColor(line.severity) : ''}
                        ${selectedLine === line.number ? 'bg-cyan-400/10' : ''}
                        ${hoveredLine === line.number ? 'bg-white/5' : ''}
                      `}
                      onClick={() => handleLineClick(line.number)}
                      onMouseEnter={() => setHoveredLine(line.number)}
                      onMouseLeave={() => setHoveredLine(null)}
                    >
                      {/* Liquid glass highlight for important lines */}
                      {line.highlighted && (
                        <div className="absolute inset-0 overflow-hidden">
                          <LiquidGlassCanvas className="absolute inset-0">
                            <LiquidGlassPlane
                              width={2}
                              height={0.3}
                              position={[0, 0, -0.1]}
                              intensity={hoveredLine === line.number ? 1.0 : 0.6}
                              distortion={0.03}
                              hovered={hoveredLine === line.number}
                              glowStrength={0.4}
                              opacity={0.2}
                              color={line.severity === 'critical' ? [1.0, 0.3, 0.3] : [0.0, 1.0, 1.0]}
                            />
                          </LiquidGlassCanvas>
                        </div>
                      )}

                      {/* Code content */}
                      <code
                        className="relative z-10 block w-full"
                        dangerouslySetInnerHTML={{
                          __html: highlightSyntax(line.content || ' ', language)
                        }}
                      />

                      {/* Annotation tooltip */}
                      {line.annotation && hoveredLine === line.number && (
                        <div className="absolute left-full ml-2 top-0 z-20 px-3 py-2 bg-black/90 border border-white/20 rounded text-xs text-white/90 whitespace-nowrap">
                          {line.annotation}
                        </div>
                      )}

                      {/* Terminal-like cursor effect */}
                      {selectedLine === line.number && (
                        <div className="absolute right-2 top-1/2 transform -translate-y-1/2 w-2 h-4 bg-cyan-400 animate-pulse" />
                      )}
                    </div>
                  ))}
                </pre>
              </div>
            </div>
          </LiquidGlassScrollbar>
        </div>

        {/* Subtle flicker effect for terminal feel */}
        <div className="absolute inset-0 bg-cyan-400/1 animate-pulse pointer-events-none opacity-30" />
      </div>
    );
  }

  // Fallback for medium/low performance
  return (
    <FallbackGlass className={`border border-white/15 bg-black/30 ${className}`}>
      <LiquidGlassScrollbar maxHeight={maxHeight} className="font-mono text-sm">
        <div className="flex">
          {/* Line numbers */}
          {showLineNumbers && (
            <div className="flex-shrink-0 px-4 py-4 text-white/40 bg-black/20 border-r border-white/10 select-none">
              {codeLines.map((line) => (
                <div
                  key={line.number}
                  className={`
                    h-6 flex items-center justify-end cursor-pointer transition-colors
                    ${selectedLine === line.number ? 'text-cyan-400' : ''}
                    ${hoveredLine === line.number ? 'text-white/60' : ''}
                  `}
                  onClick={() => handleLineClick(line.number)}
                  onMouseEnter={() => setHoveredLine(line.number)}
                  onMouseLeave={() => setHoveredLine(null)}
                >
                  {line.number}
                </div>
              ))}
            </div>
          )}

          {/* Code content */}
          <div className="flex-1 relative">
            <pre ref={codeRef} className="p-4 text-white/90 leading-6">
              {codeLines.map((line) => (
                <div
                  key={line.number}
                  className={`
                    relative h-6 flex items-center cursor-pointer group transition-colors
                    ${line.highlighted ? getSeverityColor(line.severity) : ''}
                    ${selectedLine === line.number ? 'bg-cyan-400/10' : ''}
                    ${hoveredLine === line.number ? 'bg-white/5' : ''}
                  `}
                  onClick={() => handleLineClick(line.number)}
                  onMouseEnter={() => setHoveredLine(line.number)}
                  onMouseLeave={() => setHoveredLine(null)}
                >
                  {/* Code content */}
                  <code
                    className="block w-full"
                    dangerouslySetInnerHTML={{
                      __html: highlightSyntax(line.content || ' ', language)
                    }}
                  />

                  {/* Annotation tooltip */}
                  {line.annotation && hoveredLine === line.number && (
                    <div className="absolute left-full ml-2 top-0 z-20 px-3 py-2 bg-black/90 border border-white/20 rounded text-xs text-white/90 whitespace-nowrap">
                      {line.annotation}
                    </div>
                  )}
                </div>
              ))}
            </pre>
          </div>
        </div>
      </LiquidGlassScrollbar>
    </FallbackGlass>
  );
};

export default LiquidGlassCodeViewer;
