import React, { useRef, useEffect, useState, useCallback } from 'react';
import { LiquidGlassCanvas, LiquidGlassPlane, useGPUPerformance } from './LiquidGlassCore';

interface LiquidGlassScrollbarProps {
  children: React.ReactNode;
  className?: string;
  maxHeight?: string;
  showScrollbar?: boolean;
  onScroll?: (scrollTop: number, scrollHeight: number, clientHeight: number) => void;
}

const LiquidGlassScrollbar: React.FC<LiquidGlassScrollbarProps> = ({
  children,
  className = '',
  maxHeight = '400px',
  showScrollbar = true,
  onScroll
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const contentRef = useRef<HTMLDivElement>(null);
  const thumbRef = useRef<HTMLDivElement>(null);
  const [scrollState, setScrollState] = useState({
    scrollTop: 0,
    scrollHeight: 0,
    clientHeight: 0,
    thumbHeight: 0,
    thumbTop: 0,
    isScrolling: false,
    isDragging: false
  });
  const [isHovered, setIsHovered] = useState(false);
  const performanceLevel = useGPUPerformance();

  const updateScrollState = useCallback(() => {
    if (!containerRef.current) return;

    const { scrollTop, scrollHeight, clientHeight } = containerRef.current;
    const scrollRatio = scrollTop / (scrollHeight - clientHeight);
    const thumbHeight = Math.max((clientHeight / scrollHeight) * clientHeight, 20);
    const thumbTop = scrollRatio * (clientHeight - thumbHeight);

    const newState = {
      scrollTop,
      scrollHeight,
      clientHeight,
      thumbHeight,
      thumbTop,
      isScrolling: false,
      isDragging: scrollState.isDragging
    };

    setScrollState(newState);
    onScroll?.(scrollTop, scrollHeight, clientHeight);
  }, [onScroll, scrollState.isDragging]);

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const handleScroll = () => {
      updateScrollState();
      setScrollState(prev => ({ ...prev, isScrolling: true }));
      
      // Clear scrolling state after delay
      setTimeout(() => {
        setScrollState(prev => ({ ...prev, isScrolling: false }));
      }, 150);
    };

    container.addEventListener('scroll', handleScroll);
    
    // Initial update
    updateScrollState();

    // Update on resize
    const resizeObserver = new ResizeObserver(updateScrollState);
    resizeObserver.observe(container);

    return () => {
      container.removeEventListener('scroll', handleScroll);
      resizeObserver.disconnect();
    };
  }, [updateScrollState]);

  const handleThumbMouseDown = useCallback((e: React.MouseEvent) => {
    e.preventDefault();
    const startY = e.clientY;
    const startThumbTop = scrollState.thumbTop;

    setScrollState(prev => ({ ...prev, isDragging: true }));

    const handleMouseMove = (e: MouseEvent) => {
      const deltaY = e.clientY - startY;
      const newThumbTop = Math.max(0, Math.min(
        scrollState.clientHeight - scrollState.thumbHeight,
        startThumbTop + deltaY
      ));
      
      const scrollRatio = newThumbTop / (scrollState.clientHeight - scrollState.thumbHeight);
      const newScrollTop = scrollRatio * (scrollState.scrollHeight - scrollState.clientHeight);
      
      if (containerRef.current) {
        containerRef.current.scrollTop = newScrollTop;
      }
    };

    const handleMouseUp = () => {
      setScrollState(prev => ({ ...prev, isDragging: false }));
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [scrollState]);

  const handleTrackClick = useCallback((e: React.MouseEvent) => {
    if (!containerRef.current || e.target === thumbRef.current) return;

    const rect = e.currentTarget.getBoundingClientRect();
    const clickY = e.clientY - rect.top;
    const scrollRatio = clickY / scrollState.clientHeight;
    const newScrollTop = scrollRatio * (scrollState.scrollHeight - scrollState.clientHeight);
    
    containerRef.current.scrollTop = newScrollTop;
  }, [scrollState]);

  const needsScrollbar = scrollState.scrollHeight > scrollState.clientHeight;

  return (
    <div 
      className={`relative ${className}`}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Scrollable content */}
      <div
        ref={containerRef}
        className="overflow-auto scrollbar-hide"
        style={{ maxHeight }}
      >
        <div ref={contentRef}>
          {children}
        </div>
      </div>

      {/* Custom scrollbar */}
      {showScrollbar && needsScrollbar && (
        <div
          className={`
            absolute top-0 right-0 w-3 h-full transition-opacity duration-200
            ${isHovered || scrollState.isScrolling || scrollState.isDragging ? 'opacity-100' : 'opacity-30'}
          `}
          onClick={handleTrackClick}
        >
          {/* Scrollbar track */}
          <div className="relative w-full h-full">
            {performanceLevel === 'high' ? (
              <div className="absolute inset-0 overflow-hidden">
                <LiquidGlassCanvas className="absolute inset-0">
                  <LiquidGlassPlane
                    width={0.3}
                    height={2}
                    position={[0, 0, -0.1]}
                    intensity={0.3}
                    distortion={0.02}
                    hovered={false}
                    glowStrength={0.1}
                    opacity={0.15}
                  />
                </LiquidGlassCanvas>
              </div>
            ) : (
              <div className="absolute inset-0 bg-white/10 backdrop-blur-sm" />
            )}
          </div>

          {/* Scrollbar thumb */}
          <div
            ref={thumbRef}
            className="absolute right-0 w-full cursor-pointer transition-all duration-200"
            style={{
              height: scrollState.thumbHeight,
              top: scrollState.thumbTop,
            }}
            onMouseDown={handleThumbMouseDown}
          >
            {performanceLevel === 'high' ? (
              <div className="relative w-full h-full overflow-hidden">
                <LiquidGlassCanvas className="absolute inset-0">
                  <LiquidGlassPlane
                    width={0.25}
                    height={1}
                    position={[0, 0, -0.1]}
                    intensity={scrollState.isDragging ? 1.5 : (isHovered ? 1.0 : 0.6)}
                    distortion={scrollState.isDragging ? 0.08 : 0.04}
                    hovered={isHovered || scrollState.isDragging}
                    glowStrength={scrollState.isDragging ? 0.8 : 0.4}
                    opacity={scrollState.isDragging ? 0.6 : 0.4}
                  />
                </LiquidGlassCanvas>
                
                {/* Ripple effect when dragging */}
                {scrollState.isDragging && (
                  <div className="absolute inset-0 bg-gradient-to-b from-cyan-400/20 via-transparent to-cyan-400/20 animate-pulse" />
                )}
              </div>
            ) : (
              <div className={`
                w-full h-full transition-all duration-200
                ${scrollState.isDragging ? 'bg-cyan-400/60' : (isHovered ? 'bg-cyan-400/40' : 'bg-white/30')}
                backdrop-blur-sm
              `} />
            )}
          </div>

          {/* Glow effect on hover/drag */}
          {(isHovered || scrollState.isDragging) && (
            <div className="absolute inset-0 bg-cyan-400/10 animate-pulse pointer-events-none" />
          )}
        </div>
      )}

      {/* Hide default scrollbar */}
      <style jsx>{`
        .scrollbar-hide {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }
        .scrollbar-hide::-webkit-scrollbar {
          display: none;
        }
      `}</style>
    </div>
  );
};

export default LiquidGlassScrollbar;
