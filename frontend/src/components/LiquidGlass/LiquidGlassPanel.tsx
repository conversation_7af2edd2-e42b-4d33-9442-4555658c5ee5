import React, { useState, useRef, useCallback } from 'react';
import { LiquidGlassCanvas, LiquidGlassPlane, useGPUPerformance, FallbackGlass } from './LiquidGlassCore';

interface LiquidGlassPanelProps {
  children: React.ReactNode;
  title?: string;
  collapsible?: boolean;
  defaultCollapsed?: boolean;
  resizable?: boolean;
  className?: string;
  headerActions?: React.ReactNode;
  onResize?: (width: number, height: number) => void;
}

const LiquidGlassPanel: React.FC<LiquidGlassPanelProps> = ({
  children,
  title,
  collapsible = false,
  defaultCollapsed = false,
  resizable = false,
  className = '',
  headerActions,
  onResize
}) => {
  const [isCollapsed, setIsCollapsed] = useState(defaultCollapsed);
  const [isResizing, setIsResizing] = useState(false);
  const [dimensions, setDimensions] = useState({ width: 0, height: 0 });
  const panelRef = useRef<HTMLDivElement>(null);
  const performanceLevel = useGPUPerformance();

  const handleToggleCollapse = () => {
    if (collapsible) {
      setIsCollapsed(!isCollapsed);
    }
  };

  const handleResizeStart = useCallback((e: React.MouseEvent) => {
    if (!resizable) return;
    
    setIsResizing(true);
    const startX = e.clientX;
    const startY = e.clientY;
    const startWidth = panelRef.current?.offsetWidth || 0;
    const startHeight = panelRef.current?.offsetHeight || 0;

    const handleMouseMove = (e: MouseEvent) => {
      const newWidth = startWidth + (e.clientX - startX);
      const newHeight = startHeight + (e.clientY - startY);
      
      setDimensions({ width: newWidth, height: newHeight });
      onResize?.(newWidth, newHeight);
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  }, [resizable, onResize]);

  const baseClasses = `
    relative overflow-hidden transition-all duration-300 ease-out
    border border-white/15 backdrop-blur-sm
    ${isResizing ? 'select-none' : ''}
    ${className}
  `;

  const panelStyle = resizable && dimensions.width > 0 ? {
    width: dimensions.width,
    height: dimensions.height
  } : {};

  // High-performance liquid glass version
  if (performanceLevel === 'high') {
    return (
      <div
        ref={panelRef}
        className={baseClasses}
        style={panelStyle}
      >
        <LiquidGlassCanvas className="absolute inset-0">
          {/* Main panel glass */}
          <LiquidGlassPlane
            width={2.5}
            height={2}
            position={[0, 0, -0.1]}
            intensity={isResizing ? 1.2 : 0.7}
            distortion={isResizing ? 0.1 : 0.05}
            hovered={isResizing}
            glowStrength={0.3}
            opacity={0.25}
          />
          
          {/* Background depth layer */}
          <LiquidGlassPlane
            width={2.3}
            height={1.8}
            position={[0, 0, -0.2]}
            intensity={0.4}
            distortion={0.03}
            hovered={false}
            glowStrength={0.15}
            opacity={0.15}
          />
        </LiquidGlassCanvas>

        {/* Header */}
        {title && (
          <div className="relative z-10 flex items-center justify-between p-4 border-b border-white/10">
            <div className="flex items-center space-x-3">
              {collapsible && (
                <button
                  onClick={handleToggleCollapse}
                  className="p-1 text-white/60 hover:text-white transition-colors"
                >
                  <svg
                    className={`w-4 h-4 transition-transform duration-200 ${
                      isCollapsed ? 'rotate-0' : 'rotate-90'
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              )}
              <h3 className="text-lg font-medium text-white">{title}</h3>
            </div>
            
            {headerActions && (
              <div className="flex items-center space-x-2">
                {headerActions}
              </div>
            )}
          </div>
        )}

        {/* Content */}
        <div
          className={`
            relative z-10 transition-all duration-300 ease-out overflow-hidden
            ${isCollapsed ? 'max-h-0' : 'max-h-full'}
          `}
        >
          <div className="p-4">
            {children}
          </div>
        </div>

        {/* Resize handle */}
        {resizable && (
          <div
            className="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize z-20"
            onMouseDown={handleResizeStart}
          >
            <div className="absolute bottom-1 right-1 w-2 h-2 border-r-2 border-b-2 border-white/40" />
          </div>
        )}

        {/* Resize indicator */}
        {isResizing && (
          <div className="absolute inset-0 border-2 border-cyan-400/50 pointer-events-none animate-pulse" />
        )}
      </div>
    );
  }

  // Fallback for medium/low performance
  return (
    <FallbackGlass 
      className={`${baseClasses} ${isResizing ? 'bg-black/30' : 'bg-black/20'}`}
      style={panelStyle}
    >
      <div ref={panelRef} className="relative z-10 w-full h-full">
        {/* Header */}
        {title && (
          <div className="flex items-center justify-between p-4 border-b border-white/10">
            <div className="flex items-center space-x-3">
              {collapsible && (
                <button
                  onClick={handleToggleCollapse}
                  className="p-1 text-white/60 hover:text-white transition-colors"
                >
                  <svg
                    className={`w-4 h-4 transition-transform duration-200 ${
                      isCollapsed ? 'rotate-0' : 'rotate-90'
                    }`}
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              )}
              <h3 className="text-lg font-medium text-white">{title}</h3>
            </div>
            
            {headerActions && (
              <div className="flex items-center space-x-2">
                {headerActions}
              </div>
            )}
          </div>
        )}

        {/* Content */}
        <div
          className={`
            transition-all duration-300 ease-out overflow-hidden
            ${isCollapsed ? 'max-h-0' : 'max-h-full'}
          `}
        >
          <div className="p-4">
            {children}
          </div>
        </div>

        {/* Resize handle */}
        {resizable && (
          <div
            className="absolute bottom-0 right-0 w-4 h-4 cursor-se-resize z-20"
            onMouseDown={handleResizeStart}
          >
            <div className="absolute bottom-1 right-1 w-2 h-2 border-r-2 border-b-2 border-white/40" />
          </div>
        )}

        {/* Simple resize indicator */}
        {isResizing && (
          <div className="absolute inset-0 bg-cyan-400/10 pointer-events-none" />
        )}
      </div>
    </FallbackGlass>
  );
};

export default LiquidGlassPanel;
