import React, { useState, useRef } from 'react';
import { LiquidGlassCanvas, LiquidGlassPlane, useGPUPerformance, FallbackGlass } from './LiquidGlassCore';

interface LiquidGlassButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  variant?: 'primary' | 'secondary' | 'icon';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  className?: string;
  loading?: boolean;
}

const LiquidGlassButton: React.FC<LiquidGlassButtonProps> = ({
  children,
  onClick,
  variant = 'primary',
  size = 'md',
  disabled = false,
  className = '',
  loading = false
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [isPressed, setIsPressed] = useState(false);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const performanceLevel = useGPUPerformance();

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  };

  const variantClasses = {
    primary: 'text-white font-medium',
    secondary: 'text-cyan-300 font-normal',
    icon: 'p-2'
  };

  const baseClasses = `
    relative overflow-hidden transition-all duration-200 ease-out
    border border-white/15 backdrop-blur-sm
    ${sizeClasses[size]} ${variantClasses[variant]}
    ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
    ${isPressed ? 'scale-95' : 'scale-100'}
    ${className}
  `;

  const handleMouseEnter = () => {
    if (!disabled) setIsHovered(true);
  };

  const handleMouseLeave = () => {
    setIsHovered(false);
    setIsPressed(false);
  };

  const handleMouseDown = () => {
    if (!disabled) setIsPressed(true);
  };

  const handleMouseUp = () => {
    setIsPressed(false);
  };

  const handleClick = () => {
    if (!disabled && !loading && onClick) {
      onClick();
    }
  };

  // High-performance liquid glass version
  if (performanceLevel === 'high') {
    return (
      <button
        ref={buttonRef}
        className={baseClasses}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onClick={handleClick}
        disabled={disabled}
      >
        <LiquidGlassCanvas className="absolute inset-0">
          <LiquidGlassPlane
            width={2}
            height={1}
            position={[0, 0, -0.1]}
            intensity={isHovered ? 1.5 : 0.8}
            distortion={isPressed ? 0.2 : 0.1}
            hovered={isHovered}
            glowStrength={variant === 'primary' ? 0.8 : 0.4}
            opacity={variant === 'primary' ? 0.4 : 0.2}
          />
        </LiquidGlassCanvas>
        
        {/* Content overlay */}
        <div className="relative z-10 flex items-center justify-center">
          {loading ? (
            <div className="flex items-center space-x-2">
              <div className="w-4 h-4 border-2 border-cyan-400 border-t-transparent rounded-full animate-spin" />
              <span>Loading...</span>
            </div>
          ) : (
            children
          )}
        </div>

        {/* Hover glow effect */}
        {isHovered && (
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400/20 to-transparent animate-pulse" />
        )}
      </button>
    );
  }

  // Fallback for medium/low performance
  return (
    <FallbackGlass className={baseClasses}>
      <button
        ref={buttonRef}
        className="w-full h-full relative z-10 flex items-center justify-center"
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onMouseDown={handleMouseDown}
        onMouseUp={handleMouseUp}
        onClick={handleClick}
        disabled={disabled}
      >
        {loading ? (
          <div className="flex items-center space-x-2">
            <div className="w-4 h-4 border-2 border-cyan-400 border-t-transparent rounded-full animate-spin" />
            <span>Loading...</span>
          </div>
        ) : (
          children
        )}
        
        {/* Simple hover effect for fallback */}
        {isHovered && (
          <div className="absolute inset-0 bg-cyan-400/10 transition-opacity duration-200" />
        )}
      </button>
    </FallbackGlass>
  );
};

export default LiquidGlassButton;
