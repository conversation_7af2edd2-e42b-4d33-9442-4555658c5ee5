import React, { useState, useRef } from 'react';
import { LiquidGlassCanvas, LiquidGlassPlane, useGPUPerformance, FallbackGlass } from './LiquidGlassCore';
import { motion } from 'framer-motion';

interface Tab {
  id: string;
  label: string;
  content: React.ReactNode;
  icon?: React.ReactNode;
  disabled?: boolean;
}

interface LiquidGlassTabsProps {
  tabs: Tab[];
  defaultActiveTab?: string;
  onTabChange?: (tabId: string) => void;
  className?: string;
  variant?: 'default' | 'pills' | 'underline';
}

const LiquidGlassTabs: React.FC<LiquidGlassTabsProps> = ({
  tabs,
  defaultActiveTab,
  onTabChange,
  className = '',
  variant = 'default'
}) => {
  const [activeTab, setActiveTab] = useState(defaultActiveTab || tabs[0]?.id);
  const [hoveredTab, setHoveredTab] = useState<string | null>(null);
  const tabRefs = useRef<{ [key: string]: HTMLButtonElement | null }>({});
  const performanceLevel = useGPUPerformance();

  const handleTabClick = (tabId: string) => {
    if (tabs.find(tab => tab.id === tabId)?.disabled) return;
    
    setActiveTab(tabId);
    onTabChange?.(tabId);
  };

  const activeTabContent = tabs.find(tab => tab.id === activeTab)?.content;

  const getTabClasses = (tab: Tab, isActive: boolean, isHovered: boolean) => {
    const baseClasses = `
      relative px-4 py-2 text-sm font-medium transition-all duration-200
      ${tab.disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
      ${isActive ? 'text-white' : 'text-white/70 hover:text-white'}
    `;

    switch (variant) {
      case 'pills':
        return `${baseClasses} rounded-lg mx-1`;
      case 'underline':
        return `${baseClasses} border-b-2 ${isActive ? 'border-cyan-400' : 'border-transparent'}`;
      default:
        return baseClasses;
    }
  };

  // High-performance liquid glass version
  if (performanceLevel === 'high') {
    return (
      <div className={`w-full ${className}`}>
        {/* Tab Headers */}
        <div className="relative flex border-b border-white/10">
          {tabs.map((tab) => {
            const isActive = activeTab === tab.id;
            const isHovered = hoveredTab === tab.id;
            
            return (
              <button
                key={tab.id}
                ref={(el) => (tabRefs.current[tab.id] = el)}
                className={getTabClasses(tab, isActive, isHovered)}
                onClick={() => handleTabClick(tab.id)}
                onMouseEnter={() => setHoveredTab(tab.id)}
                onMouseLeave={() => setHoveredTab(null)}
                disabled={tab.disabled}
              >
                {/* Liquid glass background for active/hovered tabs */}
                {(isActive || isHovered) && (
                  <div className="absolute inset-0 overflow-hidden">
                    <LiquidGlassCanvas className="absolute inset-0">
                      <LiquidGlassPlane
                        width={1.5}
                        height={0.8}
                        position={[0, 0, -0.1]}
                        intensity={isActive ? 1.2 : 0.8}
                        distortion={isActive ? 0.08 : 0.05}
                        hovered={isHovered}
                        glowStrength={isActive ? 0.6 : 0.3}
                        opacity={isActive ? 0.4 : 0.2}
                      />
                    </LiquidGlassCanvas>
                  </div>
                )}

                {/* Tab content */}
                <div className="relative z-10 flex items-center space-x-2">
                  {tab.icon && (
                    <span className="w-4 h-4">
                      {tab.icon}
                    </span>
                  )}
                  <span>{tab.label}</span>
                </div>

                {/* Active indicator */}
                {isActive && variant === 'default' && (
                  <motion.div
                    layoutId="activeTab"
                    className="absolute bottom-0 left-0 right-0 h-0.5 bg-cyan-400"
                    initial={false}
                    transition={{ type: "spring", stiffness: 500, damping: 30 }}
                  />
                )}

                {/* Flowing effect on hover */}
                {isHovered && !isActive && (
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400/10 to-transparent animate-pulse" />
                )}
              </button>
            );
          })}
        </div>

        {/* Tab Content */}
        <div className="relative mt-4">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
          >
            {activeTabContent}
          </motion.div>
        </div>
      </div>
    );
  }

  // Fallback for medium/low performance
  return (
    <div className={`w-full ${className}`}>
      {/* Tab Headers */}
      <div className="relative flex border-b border-white/10">
        {tabs.map((tab) => {
          const isActive = activeTab === tab.id;
          const isHovered = hoveredTab === tab.id;
          
          return (
            <button
              key={tab.id}
              ref={(el) => (tabRefs.current[tab.id] = el)}
              className={getTabClasses(tab, isActive, isHovered)}
              onClick={() => handleTabClick(tab.id)}
              onMouseEnter={() => setHoveredTab(tab.id)}
              onMouseLeave={() => setHoveredTab(null)}
              disabled={tab.disabled}
            >
              {/* Simple background for active/hovered tabs */}
              {(isActive || isHovered) && (
                <FallbackGlass className={`
                  absolute inset-0 
                  ${isActive ? 'bg-black/40' : 'bg-black/20'}
                  transition-all duration-200
                `} />
              )}

              {/* Tab content */}
              <div className="relative z-10 flex items-center space-x-2">
                {tab.icon && (
                  <span className="w-4 h-4">
                    {tab.icon}
                  </span>
                )}
                <span>{tab.label}</span>
              </div>

              {/* Active indicator */}
              {isActive && variant === 'default' && (
                <motion.div
                  layoutId="activeTabFallback"
                  className="absolute bottom-0 left-0 right-0 h-0.5 bg-cyan-400"
                  initial={false}
                  transition={{ type: "spring", stiffness: 500, damping: 30 }}
                />
              )}

              {/* Simple hover effect */}
              {isHovered && !isActive && (
                <div className="absolute inset-0 bg-cyan-400/5 transition-opacity duration-200" />
              )}
            </button>
          );
        })}
      </div>

      {/* Tab Content */}
      <div className="relative mt-4">
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.2 }}
        >
          {activeTabContent}
        </motion.div>
      </div>
    </div>
  );
};

export default LiquidGlassTabs;
