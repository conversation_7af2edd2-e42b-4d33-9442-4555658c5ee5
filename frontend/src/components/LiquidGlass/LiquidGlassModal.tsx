import React, { useState, useEffect, useRef } from 'react';
import { createPortal } from 'react-dom';
import { LiquidGlassCanvas, LiquidGlassPlane, useGPUPerformance, FallbackGlass } from './LiquidGlassCore';
import { motion, AnimatePresence } from 'framer-motion';

interface LiquidGlassModalProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  title?: string;
  size?: 'sm' | 'md' | 'lg' | 'xl';
  closeOnOverlayClick?: boolean;
  className?: string;
}

const LiquidGlassModal: React.FC<LiquidGlassModalProps> = ({
  isOpen,
  onClose,
  children,
  title,
  size = 'md',
  closeOnOverlayClick = true,
  className = ''
}) => {
  const [isAnimating, setIsAnimating] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);
  const performanceLevel = useGPUPerformance();

  const sizeClasses = {
    sm: 'max-w-md',
    md: 'max-w-lg',
    lg: 'max-w-2xl',
    xl: 'max-w-4xl'
  };

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
      setIsAnimating(true);
    } else {
      document.body.style.overflow = 'unset';
      setIsAnimating(false);
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  const handleOverlayClick = (e: React.MouseEvent) => {
    if (closeOnOverlayClick && e.target === e.currentTarget) {
      onClose();
    }
  };

  const handleKeyDown = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  useEffect(() => {
    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
      return () => document.removeEventListener('keydown', handleKeyDown);
    }
  }, [isOpen]);

  if (!isOpen) return null;

  const modalContent = (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="fixed inset-0 z-50 flex items-center justify-center p-4"
          onClick={handleOverlayClick}
        >
          {/* Backdrop */}
          <div className="absolute inset-0 bg-black/60 backdrop-blur-md" />

          {/* Modal */}
          <motion.div
            ref={modalRef}
            initial={{ scale: 0.9, opacity: 0, y: 20 }}
            animate={{ scale: 1, opacity: 1, y: 0 }}
            exit={{ scale: 0.9, opacity: 0, y: 20 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
            className={`
              relative w-full ${sizeClasses[size]} max-h-[90vh] overflow-hidden
              ${className}
            `}
          >
            {performanceLevel === 'high' ? (
              <div className="relative border border-white/20 backdrop-blur-sm overflow-hidden">
                <LiquidGlassCanvas className="absolute inset-0">
                  {/* Main modal glass */}
                  <LiquidGlassPlane
                    width={3}
                    height={2.5}
                    position={[0, 0, -0.1]}
                    intensity={isAnimating ? 1.0 : 0.7}
                    distortion={0.06}
                    hovered={isAnimating}
                    glowStrength={0.4}
                    opacity={0.35}
                  />
                  
                  {/* Background depth layer */}
                  <LiquidGlassPlane
                    width={2.8}
                    height={2.3}
                    position={[0, 0, -0.2]}
                    intensity={0.5}
                    distortion={0.04}
                    hovered={false}
                    glowStrength={0.2}
                    opacity={0.2}
                  />
                </LiquidGlassCanvas>

                {/* Content */}
                <div className="relative z-10 bg-black/20">
                  {/* Header */}
                  {title && (
                    <div className="flex items-center justify-between p-6 border-b border-white/10">
                      <h2 className="text-xl font-semibold text-white">{title}</h2>
                      <button
                        onClick={onClose}
                        className="p-2 text-white/60 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  )}

                  {/* Body */}
                  <div className="p-6 max-h-[70vh] overflow-y-auto">
                    {children}
                  </div>
                </div>

                {/* Animated border glow */}
                <div className="absolute inset-0 border border-cyan-400/30 rounded-inherit animate-pulse opacity-50" />
              </div>
            ) : (
              <FallbackGlass className="border border-white/20 bg-black/40">
                <div className="relative z-10">
                  {/* Header */}
                  {title && (
                    <div className="flex items-center justify-between p-6 border-b border-white/10">
                      <h2 className="text-xl font-semibold text-white">{title}</h2>
                      <button
                        onClick={onClose}
                        className="p-2 text-white/60 hover:text-white hover:bg-white/10 rounded-lg transition-colors"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  )}

                  {/* Body */}
                  <div className="p-6 max-h-[70vh] overflow-y-auto">
                    {children}
                  </div>
                </div>
              </FallbackGlass>
            )}
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  );

  return createPortal(modalContent, document.body);
};

export default LiquidGlassModal;
