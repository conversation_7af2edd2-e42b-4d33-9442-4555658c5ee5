import React, { useState, useRef, useEffect } from 'react';
import { LiquidGlassCanvas, LiquidGlassPlane, useGPUPerformance, FallbackGlass } from './LiquidGlassCore';

interface LiquidGlassInputProps {
  type?: 'text' | 'email' | 'password' | 'number' | 'search';
  placeholder?: string;
  value?: string;
  onChange?: (value: string) => void;
  onFocus?: () => void;
  onBlur?: () => void;
  disabled?: boolean;
  error?: string;
  label?: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  icon?: React.ReactNode;
}

const LiquidGlassInput: React.FC<LiquidGlassInputProps> = ({
  type = 'text',
  placeholder,
  value,
  onChange,
  onFocus,
  onBlur,
  disabled = false,
  error,
  label,
  className = '',
  size = 'md',
  icon
}) => {
  const [isFocused, setIsFocused] = useState(false);
  const [hasValue, setHasValue] = useState(false);
  const inputRef = useRef<HTMLInputElement>(null);
  const performanceLevel = useGPUPerformance();

  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-5 py-4 text-lg'
  };

  useEffect(() => {
    setHasValue(!!value);
  }, [value]);

  const handleFocus = () => {
    setIsFocused(true);
    onFocus?.();
  };

  const handleBlur = () => {
    setIsFocused(false);
    onBlur?.();
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setHasValue(!!newValue);
    onChange?.(newValue);
  };

  const baseClasses = `
    relative w-full transition-all duration-300 ease-out
    ${className}
  `;

  const inputClasses = `
    w-full bg-transparent border-none outline-none text-white placeholder-white/40
    ${sizeClasses[size]}
    ${icon ? 'pl-10' : ''}
    ${disabled ? 'cursor-not-allowed opacity-50' : ''}
  `;

  // High-performance liquid glass version
  if (performanceLevel === 'high') {
    return (
      <div className={baseClasses}>
        {label && (
          <label className="block text-sm font-medium text-white/80 mb-2">
            {label}
          </label>
        )}
        
        <div className="relative overflow-hidden border border-white/15 backdrop-blur-sm">
          <LiquidGlassCanvas className="absolute inset-0">
            <LiquidGlassPlane
              width={2}
              height={0.8}
              position={[0, 0, -0.1]}
              intensity={isFocused ? 1.5 : (hasValue ? 0.8 : 0.5)}
              distortion={isFocused ? 0.12 : 0.06}
              hovered={isFocused}
              glowStrength={isFocused ? 0.8 : 0.3}
              opacity={isFocused ? 0.4 : 0.2}
              color={error ? [1.0, 0.3, 0.3] : [0.0, 1.0, 1.0]}
            />
          </LiquidGlassCanvas>

          {/* Icon */}
          {icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 z-10">
              {icon}
            </div>
          )}

          {/* Input */}
          <input
            ref={inputRef}
            type={type}
            placeholder={placeholder}
            value={value}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            disabled={disabled}
            className={`${inputClasses} relative z-10`}
          />

          {/* Focus glow effect */}
          {isFocused && (
            <div className={`
              absolute inset-0 border-2 pointer-events-none
              ${error ? 'border-red-400/50' : 'border-cyan-400/50'}
              animate-pulse
            `} />
          )}

          {/* Ripple effect on focus */}
          {isFocused && (
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400/10 to-transparent animate-pulse" />
          )}
        </div>

        {/* Error message */}
        {error && (
          <p className="mt-2 text-sm text-red-400 animate-pulse">
            {error}
          </p>
        )}
      </div>
    );
  }

  // Fallback for medium/low performance
  return (
    <div className={baseClasses}>
      {label && (
        <label className="block text-sm font-medium text-white/80 mb-2">
          {label}
        </label>
      )}
      
      <FallbackGlass className={`
        border border-white/15 
        ${isFocused ? 'border-cyan-400/50 bg-black/30' : 'bg-black/20'}
        ${error ? 'border-red-400/50' : ''}
        transition-all duration-300
      `}>
        <div className="relative">
          {/* Icon */}
          {icon && (
            <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/60 z-10">
              {icon}
            </div>
          )}

          {/* Input */}
          <input
            ref={inputRef}
            type={type}
            placeholder={placeholder}
            value={value}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            disabled={disabled}
            className={inputClasses}
          />

          {/* Simple focus effect */}
          {isFocused && (
            <div className={`
              absolute inset-0 bg-gradient-to-r from-transparent via-cyan-400/5 to-transparent
              pointer-events-none
            `} />
          )}
        </div>
      </FallbackGlass>

      {/* Error message */}
      {error && (
        <p className="mt-2 text-sm text-red-400">
          {error}
        </p>
      )}
    </div>
  );
};

export default LiquidGlassInput;
