// Custom hook for liquid glass effects
import { useEffect, useRef, useState } from 'react';

export const useLiquidGlass = (options = {}) => {
  const {
    intensity = 1.0,
    enableOnHover = true,
    enableOnFocus = true,
    autoDetectPerformance = true
  } = options;

  const elementRef = useRef(null);
  const [isActive, setIsActive] = useState(false);
  const [performanceLevel, setPerformanceLevel] = useState('high');

  // Detect GPU performance
  useEffect(() => {
    if (!autoDetectPerformance) return;

    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    
    if (!gl) {
      setPerformanceLevel('low');
      return;
    }

    const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
    if (debugInfo) {
      const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
      
      // Basic GPU detection
      if (renderer.includes('Intel') && !renderer.includes('Iris')) {
        setPerformanceLevel('medium');
      } else if (renderer.includes('GTX') || renderer.includes('RTX') || renderer.includes('Radeon')) {
        setPerformanceLevel('high');
      } else {
        setPerformanceLevel('medium');
      }
    }
  }, [autoDetectPerformance]);

  // Handle interactions
  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    const handleMouseEnter = () => {
      if (enableOnHover) setIsActive(true);
    };

    const handleMouseLeave = () => {
      if (enableOnHover) setIsActive(false);
    };

    const handleFocus = () => {
      if (enableOnFocus) setIsActive(true);
    };

    const handleBlur = () => {
      if (enableOnFocus) setIsActive(false);
    };

    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
    element.addEventListener('focus', handleFocus);
    element.addEventListener('blur', handleBlur);

    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
      element.removeEventListener('focus', handleFocus);
      element.removeEventListener('blur', handleBlur);
    };
  }, [enableOnHover, enableOnFocus]);

  return {
    elementRef,
    isActive,
    performanceLevel,
    shouldUseShader: performanceLevel !== 'low',
    glassProps: {
      'data-liquid-glass': true,
      'data-intensity': intensity,
      'data-active': isActive
    }
  };
};

// Performance monitoring hook
export const usePerformanceMonitor = () => {
  const [fps, setFps] = useState(60);
  const [memoryUsage, setMemoryUsage] = useState(0);
  const frameCount = useRef(0);
  const lastTime = useRef(performance.now());

  useEffect(() => {
    let animationId;

    const monitor = () => {
      frameCount.current++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime.current >= 1000) {
        setFps(frameCount.current);
        frameCount.current = 0;
        lastTime.current = currentTime;
        
        // Memory usage (if available)
        if (performance.memory) {
          setMemoryUsage(performance.memory.usedJSHeapSize / 1024 / 1024);
        }
      }
      
      animationId = requestAnimationFrame(monitor);
    };

    monitor();

    return () => {
      if (animationId) {
        cancelAnimationFrame(animationId);
      }
    };
  }, []);

  return { fps, memoryUsage };
};

// Shader compatibility detection
export const useShaderSupport = () => {
  const [support, setSupport] = useState({
    webgl: false,
    webgl2: false,
    extensions: []
  });

  useEffect(() => {
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
    const gl2 = canvas.getContext('webgl2');

    const extensions = [];
    if (gl) {
      const availableExtensions = gl.getSupportedExtensions() || [];
      extensions.push(...availableExtensions);
    }

    setSupport({
      webgl: !!gl,
      webgl2: !!gl2,
      extensions
    });
  }, []);

  return support;
};
