/* Liquid Glass CSS Integration for ByteGuardX */
/* These classes can be applied to existing elements for instant liquid glass upgrade */

/* Base liquid glass container */
.liquid-glass {
  position: relative;
  overflow: hidden;
}

.liquid-glass canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 0;
}

.liquid-glass > * {
  position: relative;
  z-index: 1;
}

/* Fallback glassmorphism styles */
.liquid-glass-fallback {
  backdrop-filter: blur(12px);
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* Component-specific liquid glass styles */
.liquid-glass-button {
  transition: all 0.2s ease;
}

.liquid-glass-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 20px rgba(0, 255, 255, 0.1);
}

.liquid-glass-button:active {
  transform: translateY(0);
}

.liquid-glass-card {
  transition: all 0.3s ease;
}

.liquid-glass-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 255, 255, 0.15);
}

.liquid-glass-modal {
  animation: liquidGlassModalIn 0.3s ease-out;
}

@keyframes liquidGlassModalIn {
  from {
    opacity: 0;
    transform: scale(0.95) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.liquid-glass-nav {
  backdrop-filter: blur(16px);
  background: rgba(0, 0, 0, 0.3);
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* Hover effects for liquid glass elements */
.liquid-glass-hover:hover::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 255, 255, 0.05);
  pointer-events: none;
  z-index: 1;
}

/* Performance-based classes */
.liquid-glass-high-performance {
  /* Full shader effects enabled */
}

.liquid-glass-medium-performance {
  /* Reduced shader complexity */
  backdrop-filter: blur(8px);
}

.liquid-glass-low-performance {
  /* CSS-only fallback */
  backdrop-filter: blur(6px);
  background: rgba(0, 0, 0, 0.25);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* Utility classes for easy integration */
.lg-button {
  @apply liquid-glass liquid-glass-button rounded-lg;
}

.lg-card {
  @apply liquid-glass liquid-glass-card rounded-xl;
}

.lg-modal {
  @apply liquid-glass liquid-glass-modal rounded-xl;
}

.lg-nav {
  @apply liquid-glass liquid-glass-nav;
}

.lg-panel {
  @apply liquid-glass rounded-lg;
}

/* Responsive liquid glass */
@media (max-width: 768px) {
  .liquid-glass {
    /* Reduce effects on mobile for performance */
  }
  
  .liquid-glass-fallback {
    backdrop-filter: blur(8px);
  }
}

@media (prefers-reduced-motion: reduce) {
  .liquid-glass-button,
  .liquid-glass-card,
  .liquid-glass-modal {
    transition: none;
    animation: none;
  }
}

/* Dark theme optimizations */
@media (prefers-color-scheme: dark) {
  .liquid-glass-fallback {
    background: rgba(0, 0, 0, 0.3);
    border-color: rgba(255, 255, 255, 0.2);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .liquid-glass-fallback {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid rgba(255, 255, 255, 0.5);
  }
}

/* Print styles */
@media print {
  .liquid-glass,
  .liquid-glass-fallback {
    background: white !important;
    border: 1px solid black !important;
    backdrop-filter: none !important;
  }
  
  .liquid-glass canvas {
    display: none !important;
  }
}
