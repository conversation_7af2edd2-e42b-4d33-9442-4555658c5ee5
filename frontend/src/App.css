@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* ByteGuardX Global Styles */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  background: #000000;
  color: #ffffff;
  font-family: 'Inter', 'SF Pro Display', 'IBM Plex Sans', system-ui, sans-serif;
  overflow-x: hidden;
}

body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 255, 255, 0.3);
  border-radius: 4px;
  transition: background 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 255, 255, 0.5);
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 255, 255, 0.3) rgba(255, 255, 255, 0.05);
}

/* Selection styles */
::selection {
  background: rgba(0, 255, 255, 0.3);
  color: #ffffff;
}

::-moz-selection {
  background: rgba(0, 255, 255, 0.3);
  color: #ffffff;
}

/* Focus styles */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid rgba(0, 255, 255, 0.5);
  outline-offset: 2px;
}

/* Disable text selection on UI elements */
button, .no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Code elements should allow selection */
code, pre, .code-viewer {
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
}

/* Smooth transitions for interactive elements */
button, input, textarea, select {
  transition: all 0.2s ease;
}

/* Glass morphism utility classes */
.glass-panel {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

.liquid-glass-panel {
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 8px 32px 0 rgba(0, 255, 255, 0.1);
}

/* Cyber glow effects */
.cyber-glow {
  box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
}

.cyber-glow-strong {
  box-shadow: 0 0 40px rgba(0, 255, 255, 0.5);
}

/* Text glow effects */
.text-cyber-glow {
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

.text-cyber-glow-strong {
  text-shadow: 0 0 20px rgba(0, 255, 255, 0.8);
}

/* Loading animations */
@keyframes liquidPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

.liquid-pulse {
  animation: liquidPulse 2s ease-in-out infinite;
}

/* Ripple effect */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(0, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::before {
  width: 300px;
  height: 300px;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .glass-panel,
  .liquid-glass-panel {
    border: 2px solid rgba(255, 255, 255, 0.5);
    background: rgba(0, 0, 0, 0.8);
  }
  
  .text-white\/60 {
    color: rgba(255, 255, 255, 0.9) !important;
  }
  
  .text-white\/40 {
    color: rgba(255, 255, 255, 0.7) !important;
  }
}

/* Print styles */
@media print {
  * {
    background: transparent !important;
    color: black !important;
    box-shadow: none !important;
    text-shadow: none !important;
  }
  
  .glass-panel,
  .liquid-glass-panel {
    border: 1px solid black;
    background: white;
  }
}
