/* ByteGuardX App Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: #000000;
  color: #ffffff;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.App {
  min-height: 100vh;
  background: #000000;
}

/* Glassmorphism styles */
.glass {
  backdrop-filter: blur(12px);
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.glass:hover {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(0, 255, 255, 0.3);
}

/* Button styles */
.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary {
  background: rgba(0, 255, 255, 0.1);
  color: #00ffff;
  border: 1px solid rgba(0, 255, 255, 0.3);
}

.btn-primary:hover {
  background: rgba(0, 255, 255, 0.2);
  border-color: #00ffff;
}

/* Card styles */
.card {
  padding: 24px;
  border-radius: 12px;
  margin-bottom: 16px;
}

/* Navigation */
.navbar {
  padding: 16px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* Utility classes */
.text-cyan {
  color: #00ffff;
}

.text-white {
  color: #ffffff;
}

.text-gray {
  color: rgba(255, 255, 255, 0.6);
}

.mb-4 {
  margin-bottom: 16px;
}

.p-4 {
  padding: 16px;
}

.p-6 {
  padding: 24px;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.space-x-4 > * + * {
  margin-left: 16px;
}

.rounded {
  border-radius: 8px;
}

.rounded-lg {
  border-radius: 12px;
}

.w-full {
  width: 100%;
}

.max-w-4xl {
  max-width: 896px;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}
