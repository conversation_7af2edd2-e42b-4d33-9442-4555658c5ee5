// Shader utilities and performance optimizations for ByteGuardX

// Shader performance levels
export const PERFORMANCE_LEVELS = {
  HIGH: 'high',
  MEDIUM: 'medium', 
  LOW: 'low'
};

// Detect optimal shader settings based on device
export const detectShaderCapabilities = () => {
  const canvas = document.createElement('canvas');
  const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
  
  if (!gl) {
    return {
      level: PERFORMANCE_LEVELS.LOW,
      maxTextureSize: 0,
      extensions: [],
      canUseShaders: false
    };
  }

  const maxTextureSize = gl.getParameter(gl.MAX_TEXTURE_SIZE);
  const extensions = gl.getSupportedExtensions() || [];
  
  // Check for important extensions
  const hasFloatTextures = extensions.includes('OES_texture_float');
  const hasDerivatives = extensions.includes('OES_standard_derivatives');
  
  // Determine performance level
  let level = PERFORMANCE_LEVELS.MEDIUM;
  
  const debugInfo = gl.getExtension('WEBGL_debug_renderer_info');
  if (debugInfo) {
    const renderer = gl.getParameter(debugInfo.UNMASKED_RENDERER_WEBGL);
    
    if (renderer.includes('Intel') && !renderer.includes('Iris')) {
      level = PERFORMANCE_LEVELS.LOW;
    } else if (renderer.includes('GTX') || renderer.includes('RTX') || renderer.includes('Radeon')) {
      level = PERFORMANCE_LEVELS.HIGH;
    }
  }

  return {
    level,
    maxTextureSize,
    extensions,
    canUseShaders: true,
    hasFloatTextures,
    hasDerivatives
  };
};

// Optimized shader configurations for different performance levels
export const getShaderConfig = (performanceLevel) => {
  const configs = {
    [PERFORMANCE_LEVELS.HIGH]: {
      complexity: 1.0,
      updateFrequency: 60,
      noiseOctaves: 4,
      distortionStrength: 1.0,
      enableCaustics: true,
      enableRipples: true
    },
    [PERFORMANCE_LEVELS.MEDIUM]: {
      complexity: 0.7,
      updateFrequency: 30,
      noiseOctaves: 3,
      distortionStrength: 0.7,
      enableCaustics: true,
      enableRipples: false
    },
    [PERFORMANCE_LEVELS.LOW]: {
      complexity: 0.4,
      updateFrequency: 15,
      noiseOctaves: 2,
      distortionStrength: 0.4,
      enableCaustics: false,
      enableRipples: false
    }
  };

  return configs[performanceLevel] || configs[PERFORMANCE_LEVELS.MEDIUM];
};

// Throttled animation frame for performance
export class ThrottledAnimationFrame {
  constructor(callback, fps = 60) {
    this.callback = callback;
    this.fps = fps;
    this.interval = 1000 / fps;
    this.lastTime = 0;
    this.animationId = null;
    this.isRunning = false;
  }

  start() {
    if (this.isRunning) return;
    this.isRunning = true;
    this.lastTime = performance.now();
    this.tick();
  }

  stop() {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
  }

  tick = () => {
    if (!this.isRunning) return;

    const currentTime = performance.now();
    const deltaTime = currentTime - this.lastTime;

    if (deltaTime >= this.interval) {
      this.callback(currentTime);
      this.lastTime = currentTime - (deltaTime % this.interval);
    }

    this.animationId = requestAnimationFrame(this.tick);
  };

  setFPS(fps) {
    this.fps = fps;
    this.interval = 1000 / fps;
  }
}

// Memory-efficient shader uniform management
export class UniformManager {
  constructor() {
    this.uniforms = new Map();
    this.dirty = new Set();
  }

  set(name, value) {
    const current = this.uniforms.get(name);
    
    // Only mark as dirty if value actually changed
    if (current !== value) {
      this.uniforms.set(name, value);
      this.dirty.add(name);
    }
  }

  get(name) {
    return this.uniforms.get(name);
  }

  getDirty() {
    return Array.from(this.dirty);
  }

  clearDirty() {
    this.dirty.clear();
  }

  has(name) {
    return this.uniforms.has(name);
  }
}

// Shader compilation with error handling
export const compileShader = (gl, type, source) => {
  const shader = gl.createShader(type);
  gl.shaderSource(shader, source);
  gl.compileShader(shader);

  if (!gl.getShaderParameter(shader, gl.COMPILE_STATUS)) {
    const error = gl.getShaderInfoLog(shader);
    gl.deleteShader(shader);
    throw new Error(`Shader compilation error: ${error}`);
  }

  return shader;
};

// Program linking with validation
export const createShaderProgram = (gl, vertexSource, fragmentSource) => {
  try {
    const vertexShader = compileShader(gl, gl.VERTEX_SHADER, vertexSource);
    const fragmentShader = compileShader(gl, gl.FRAGMENT_SHADER, fragmentSource);

    const program = gl.createProgram();
    gl.attachShader(program, vertexShader);
    gl.attachShader(program, fragmentShader);
    gl.linkProgram(program);

    if (!gl.getProgramParameter(program, gl.LINK_STATUS)) {
      const error = gl.getProgramInfoLog(program);
      gl.deleteProgram(program);
      throw new Error(`Program linking error: ${error}`);
    }

    // Clean up shaders
    gl.deleteShader(vertexShader);
    gl.deleteShader(fragmentShader);

    return program;
  } catch (error) {
    console.error('Failed to create shader program:', error);
    return null;
  }
};

// Optimized geometry creation
export const createQuadGeometry = (gl) => {
  const vertices = new Float32Array([
    -1, -1, 0, 0,  // bottom-left
     1, -1, 1, 0,  // bottom-right
    -1,  1, 0, 1,  // top-left
     1,  1, 1, 1   // top-right
  ]);

  const buffer = gl.createBuffer();
  gl.bindBuffer(gl.ARRAY_BUFFER, buffer);
  gl.bufferData(gl.ARRAY_BUFFER, vertices, gl.STATIC_DRAW);

  return {
    buffer,
    vertexCount: 4,
    stride: 16, // 4 floats * 4 bytes
    positionOffset: 0,
    uvOffset: 8 // 2 floats * 4 bytes
  };
};

// CSS fallback classes for non-shader elements
export const FALLBACK_CLASSES = {
  button: 'backdrop-blur-md bg-black/20 border border-white/15 hover:bg-black/30 hover:border-cyan-400/30 transition-all duration-200',
  card: 'backdrop-blur-md bg-black/20 border border-white/15 hover:bg-black/25 transition-all duration-300',
  modal: 'backdrop-blur-lg bg-black/40 border border-white/20',
  panel: 'backdrop-blur-md bg-black/15 border border-white/10',
  navbar: 'backdrop-blur-lg bg-black/30 border-b border-white/10'
};

// Performance monitoring
export const createPerformanceMonitor = () => {
  let frameCount = 0;
  let lastTime = performance.now();
  let fps = 60;

  return {
    update() {
      frameCount++;
      const currentTime = performance.now();
      
      if (currentTime - lastTime >= 1000) {
        fps = frameCount;
        frameCount = 0;
        lastTime = currentTime;
      }
      
      return fps;
    },
    getFPS: () => fps,
    shouldReduceQuality: () => fps < 30,
    shouldDisableShaders: () => fps < 15
  };
};
