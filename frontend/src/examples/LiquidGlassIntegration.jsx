// Integration examples for existing ByteGuardX components
// These show how to upgrade your current glassmorphism elements

import React from 'react';
import LiquidGlassWrapper from '../components/LiquidGlassWrapper';
import { useLiquidGlass } from '../hooks/useLiquidGlass';

// Example 1: Upgrade existing button
const ExistingButton = ({ children, className, onClick, ...props }) => {
  // Your existing button logic here
  return (
    <button 
      className={`px-4 py-2 text-white font-medium ${className}`}
      onClick={onClick}
      {...props}
    >
      {children}
    </button>
  );
};

// Upgraded button with liquid glass
const LiquidGlassButton = ({ children, className = '', ...props }) => {
  return (
    <LiquidGlassWrapper
      className={`rounded-lg overflow-hidden ${className}`}
      fallbackClass="backdrop-blur-md bg-black/20 border border-white/15 hover:bg-black/30 hover:border-cyan-400/30 transition-all duration-200"
    >
      <ExistingButton className="w-full h-full bg-transparent border-none" {...props}>
        {children}
      </ExistingButton>
    </LiquidGlassWrapper>
  );
};

// Example 2: Upgrade existing card component
const ExistingCard = ({ children, className, ...props }) => {
  return (
    <div className={`p-6 ${className}`} {...props}>
      {children}
    </div>
  );
};

// Upgraded card with liquid glass
const LiquidGlassCard = ({ children, className = '', ...props }) => {
  return (
    <LiquidGlassWrapper
      className={`rounded-xl ${className}`}
      fallbackClass="backdrop-blur-md bg-black/20 border border-white/15 hover:bg-black/25 transition-all duration-300"
    >
      <ExistingCard className="bg-transparent" {...props}>
        {children}
      </ExistingCard>
    </LiquidGlassWrapper>
  );
};

// Example 3: Modal background upgrade
const ExistingModal = ({ isOpen, children, onClose }) => {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/60" onClick={onClose} />
      <div className="relative max-w-lg w-full">
        {children}
      </div>
    </div>
  );
};

// Upgraded modal with liquid glass
const LiquidGlassModal = ({ isOpen, children, onClose }) => {
  if (!isOpen) return null;
  
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
      <div className="absolute inset-0 bg-black/60" onClick={onClose} />
      <LiquidGlassWrapper
        className="relative max-w-lg w-full rounded-xl"
        fallbackClass="backdrop-blur-lg bg-black/40 border border-white/20"
        intensity={1.2}
      >
        <div className="p-6">
          {children}
        </div>
      </LiquidGlassWrapper>
    </div>
  );
};

// Example 4: Navigation panel upgrade
const ExistingNavPanel = ({ children, className }) => {
  return (
    <nav className={`p-4 ${className}`}>
      {children}
    </nav>
  );
};

const LiquidGlassNavPanel = ({ children, className = '' }) => {
  return (
    <LiquidGlassWrapper
      className={`${className}`}
      fallbackClass="backdrop-blur-lg bg-black/30 border-r border-white/10"
      intensity={0.8}
    >
      <ExistingNavPanel className="bg-transparent">
        {children}
      </ExistingNavPanel>
    </LiquidGlassWrapper>
  );
};

// Example 5: Using the hook for custom integration
const CustomLiquidGlassElement = ({ children, className }) => {
  const { elementRef, isActive, shouldUseShader, glassProps } = useLiquidGlass({
    intensity: 1.0,
    enableOnHover: true
  });

  if (shouldUseShader) {
    return (
      <LiquidGlassWrapper
        ref={elementRef}
        className={className}
        {...glassProps}
      >
        {children}
      </LiquidGlassWrapper>
    );
  }

  // Fallback to CSS glassmorphism
  return (
    <div 
      ref={elementRef}
      className={`backdrop-blur-md bg-black/20 border border-white/15 ${className}`}
      {...glassProps}
    >
      {children}
    </div>
  );
};

// Example 6: Scan results card with liquid glass
const ScanResultCard = ({ result, onClick }) => {
  return (
    <LiquidGlassCard 
      className="mb-4 cursor-pointer hover:scale-[1.02] transition-transform duration-200"
      onClick={onClick}
    >
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-white font-semibold">{result.fileName}</h3>
          <p className="text-white/60 text-sm">
            {result.findings.length} findings • {result.timestamp}
          </p>
        </div>
        <div className="flex space-x-2">
          {result.findings.map((finding, index) => (
            <span
              key={index}
              className={`px-2 py-1 text-xs rounded ${
                finding.severity === 'high' ? 'bg-red-500/20 text-red-400' :
                finding.severity === 'medium' ? 'bg-yellow-500/20 text-yellow-400' :
                'bg-blue-500/20 text-blue-400'
              }`}
            >
              {finding.severity}
            </span>
          ))}
        </div>
      </div>
    </LiquidGlassCard>
  );
};

// Example 7: Action buttons with liquid glass
const ActionButton = ({ children, variant = 'primary', ...props }) => {
  const baseClasses = "px-6 py-3 font-medium rounded-lg transition-all duration-200";
  const variantClasses = {
    primary: "text-white",
    secondary: "text-cyan-300"
  };

  return (
    <LiquidGlassButton 
      className={`${baseClasses} ${variantClasses[variant]}`}
      {...props}
    >
      {children}
    </LiquidGlassButton>
  );
};

export {
  LiquidGlassButton,
  LiquidGlassCard,
  LiquidGlassModal,
  LiquidGlassNavPanel,
  CustomLiquidGlassElement,
  ScanResultCard,
  ActionButton
};
