import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, 
  AlertTriangle, 
  Activity, 
  TrendingUp,
  FileText,
  Clock,
  Zap,
  Target
} from 'lucide-react';
import { useQuery } from 'react-query';
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  BarChart,
  Bar
} from 'recharts';
import { fetchDashboardData, fetchRecentScans } from '../services/api';

const Dashboard: React.FC = () => {
  const [timeRange, setTimeRange] = useState('7d');

  const { data: dashboardData, isLoading } = useQuery(
    ['dashboardData', timeRange],
    () => fetchDashboardData(timeRange),
    { refetchInterval: 30000 }
  );

  const { data: recentScans } = useQuery('recentScans', fetchRecentScans, {
    refetchInterval: 10000
  });

  // Mock data for charts
  const vulnerabilityTrends = [
    { date: '2024-01-01', critical: 2, high: 5, medium: 12, low: 8 },
    { date: '2024-01-02', critical: 1, high: 7, medium: 15, low: 10 },
    { date: '2024-01-03', critical: 3, high: 4, medium: 18, low: 12 },
    { date: '2024-01-04', critical: 0, high: 6, medium: 14, low: 9 },
    { date: '2024-01-05', critical: 1, high: 3, medium: 16, low: 11 },
    { date: '2024-01-06', critical: 2, high: 8, medium: 13, low: 7 },
    { date: '2024-01-07', critical: 0, high: 5, medium: 19, low: 14 },
  ];

  const severityDistribution = [
    { name: 'Critical', value: 9, color: '#f44336' },
    { name: 'High', value: 38, color: '#ff9800' },
    { name: 'Medium', value: 107, color: '#ffeb3b' },
    { name: 'Low', value: 71, color: '#4caf50' },
  ];

  const scanPerformance = [
    { name: 'Mon', scans: 24, avgTime: 1.2 },
    { name: 'Tue', scans: 31, avgTime: 1.1 },
    { name: 'Wed', scans: 28, avgTime: 1.3 },
    { name: 'Thu', scans: 35, avgTime: 1.0 },
    { name: 'Fri', scans: 42, avgTime: 1.4 },
    { name: 'Sat', scans: 18, avgTime: 0.9 },
    { name: 'Sun', scans: 15, avgTime: 0.8 },
  ];

  const stats = [
    {
      title: 'Total Vulnerabilities',
      value: dashboardData?.totalVulnerabilities || 225,
      change: -12,
      icon: Shield,
      color: 'text-red-400',
      bgColor: 'bg-red-500/10',
    },
    {
      title: 'Critical Issues',
      value: dashboardData?.criticalIssues || 9,
      change: -3,
      icon: AlertTriangle,
      color: 'text-orange-400',
      bgColor: 'bg-orange-500/10',
    },
    {
      title: 'Files Scanned',
      value: dashboardData?.filesScanned || 1847,
      change: 156,
      icon: FileText,
      color: 'text-blue-400',
      bgColor: 'bg-blue-500/10',
    },
    {
      title: 'Scan Efficiency',
      value: `${dashboardData?.scanEfficiency || 94}%`,
      change: 2.1,
      icon: TrendingUp,
      color: 'text-green-400',
      bgColor: 'bg-green-500/10',
    },
  ];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-cyan-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white">Security Dashboard</h1>
          <p className="text-gray-400 mt-2">
            Real-time security analytics and vulnerability insights
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="bg-gray-900 border border-gray-700 rounded-lg px-4 py-2 text-sm focus:outline-none focus:border-cyan-500"
          >
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          
          <button className="bg-cyan-600 hover:bg-cyan-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
            Export Report
          </button>
        </div>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {stats.map((stat, index) => (
          <motion.div
            key={stat.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-gray-900/50 backdrop-blur-xl border border-gray-700 rounded-xl p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-400 text-sm">{stat.title}</p>
                <p className="text-2xl font-bold text-white mt-1">{stat.value}</p>
                <div className="flex items-center mt-2">
                  <span className={`text-sm ${
                    stat.change > 0 ? 'text-green-400' : 'text-red-400'
                  }`}>
                    {stat.change > 0 ? '+' : ''}{stat.change}
                    {typeof stat.value === 'string' && stat.value.includes('%') ? 'pp' : ''}
                  </span>
                  <span className="text-gray-500 text-sm ml-1">vs last period</span>
                </div>
              </div>
              <div className={`p-3 rounded-lg ${stat.bgColor}`}>
                <stat.icon className={`w-6 h-6 ${stat.color}`} />
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Vulnerability Trends */}
        <motion.div
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gray-900/50 backdrop-blur-xl border border-gray-700 rounded-xl p-6"
        >
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-white">Vulnerability Trends</h3>
            <div className="flex items-center space-x-4 text-sm">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                <span className="text-gray-400">Critical</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                <span className="text-gray-400">High</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
                <span className="text-gray-400">Medium</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-gray-400">Low</span>
              </div>
            </div>
          </div>
          
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={vulnerabilityTrends}>
              <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
              <XAxis 
                dataKey="date" 
                stroke="#9CA3AF"
                fontSize={12}
                tickFormatter={(value) => new Date(value).toLocaleDateString()}
              />
              <YAxis stroke="#9CA3AF" fontSize={12} />
              <Tooltip 
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB'
                }}
              />
              <Line type="monotone" dataKey="critical" stroke="#f44336" strokeWidth={2} />
              <Line type="monotone" dataKey="high" stroke="#ff9800" strokeWidth={2} />
              <Line type="monotone" dataKey="medium" stroke="#ffeb3b" strokeWidth={2} />
              <Line type="monotone" dataKey="low" stroke="#4caf50" strokeWidth={2} />
            </LineChart>
          </ResponsiveContainer>
        </motion.div>

        {/* Severity Distribution */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gray-900/50 backdrop-blur-xl border border-gray-700 rounded-xl p-6"
        >
          <h3 className="text-lg font-semibold text-white mb-6">Severity Distribution</h3>
          
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={severityDistribution}
                cx="50%"
                cy="50%"
                innerRadius={60}
                outerRadius={120}
                paddingAngle={5}
                dataKey="value"
              >
                {severityDistribution.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip 
                contentStyle={{
                  backgroundColor: '#1F2937',
                  border: '1px solid #374151',
                  borderRadius: '8px',
                  color: '#F9FAFB'
                }}
              />
            </PieChart>
          </ResponsiveContainer>
          
          <div className="grid grid-cols-2 gap-4 mt-4">
            {severityDistribution.map((item) => (
              <div key={item.name} className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <div 
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: item.color }}
                  ></div>
                  <span className="text-gray-400 text-sm">{item.name}</span>
                </div>
                <span className="text-white font-medium">{item.value}</span>
              </div>
            ))}
          </div>
        </motion.div>
      </div>

      {/* Scan Performance */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-gray-900/50 backdrop-blur-xl border border-gray-700 rounded-xl p-6"
      >
        <h3 className="text-lg font-semibold text-white mb-6">Scan Performance</h3>
        
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={scanPerformance}>
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis dataKey="name" stroke="#9CA3AF" fontSize={12} />
            <YAxis stroke="#9CA3AF" fontSize={12} />
            <Tooltip 
              contentStyle={{
                backgroundColor: '#1F2937',
                border: '1px solid #374151',
                borderRadius: '8px',
                color: '#F9FAFB'
              }}
            />
            <Bar dataKey="scans" fill="#00bcd4" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </motion.div>

      {/* Recent Activity */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5 }}
        className="bg-gray-900/50 backdrop-blur-xl border border-gray-700 rounded-xl p-6"
      >
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-white">Recent Scans</h3>
          <button className="text-cyan-400 hover:text-cyan-300 text-sm transition-colors">
            View All
          </button>
        </div>
        
        <div className="space-y-4">
          {(recentScans || [
            {
              id: 1,
              fileName: 'auth/login.py',
              status: 'completed',
              vulnerabilities: 3,
              severity: 'high',
              timestamp: '2 minutes ago'
            },
            {
              id: 2,
              fileName: 'api/users.js',
              status: 'completed',
              vulnerabilities: 0,
              severity: 'none',
              timestamp: '5 minutes ago'
            },
            {
              id: 3,
              fileName: 'database/queries.sql',
              status: 'scanning',
              vulnerabilities: null,
              severity: null,
              timestamp: 'In progress'
            }
          ]).map((scan) => (
            <div key={scan.id} className="flex items-center justify-between p-4 bg-gray-800/30 rounded-lg">
              <div className="flex items-center space-x-4">
                <div className={`p-2 rounded-lg ${
                  scan.status === 'completed' ? 'bg-green-500/10' :
                  scan.status === 'scanning' ? 'bg-blue-500/10' : 'bg-gray-500/10'
                }`}>
                  {scan.status === 'completed' ? (
                    <Shield className="w-4 h-4 text-green-400" />
                  ) : scan.status === 'scanning' ? (
                    <Activity className="w-4 h-4 text-blue-400 animate-pulse" />
                  ) : (
                    <Clock className="w-4 h-4 text-gray-400" />
                  )}
                </div>
                
                <div>
                  <p className="text-white font-medium">{scan.fileName}</p>
                  <p className="text-gray-400 text-sm">{scan.timestamp}</p>
                </div>
              </div>
              
              <div className="flex items-center space-x-4">
                {scan.vulnerabilities !== null && (
                  <div className="text-right">
                    <p className="text-white font-medium">
                      {scan.vulnerabilities} vulnerabilities
                    </p>
                    {scan.severity && scan.severity !== 'none' && (
                      <p className={`text-sm ${
                        scan.severity === 'critical' ? 'text-red-400' :
                        scan.severity === 'high' ? 'text-orange-400' :
                        scan.severity === 'medium' ? 'text-yellow-400' : 'text-green-400'
                      }`}>
                        {scan.severity} severity
                      </p>
                    )}
                  </div>
                )}
                
                <button className="text-cyan-400 hover:text-cyan-300 text-sm transition-colors">
                  View Details
                </button>
              </div>
            </div>
          ))}
        </div>
      </motion.div>
    </div>
  );
};

export default Dashboard;
