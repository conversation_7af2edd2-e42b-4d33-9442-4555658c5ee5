name: Release Automation

on:
  push:
    tags:
      - 'v*'
  workflow_dispatch:
    inputs:
      version:
        description: 'Release version (e.g., v1.0.0)'
        required: true
        type: string

env:
  PYTHON_VERSION: '3.9'
  NODE_VERSION: '18'

jobs:
  # Create GitHub Release
  create-release:
    name: Create GitHub Release
    runs-on: ubuntu-latest
    outputs:
      upload_url: ${{ steps.create_release.outputs.upload_url }}
      release_id: ${{ steps.create_release.outputs.id }}
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Generate changelog
      id: changelog
      run: |
        # Generate changelog from git commits
        echo "## Changes" > CHANGELOG.md
        git log --pretty=format:"- %s" $(git describe --tags --abbrev=0 HEAD^)..HEAD >> CHANGELOG.md
        echo "" >> CHANGELOG.md
        echo "**Full Changelog**: https://github.com/${{ github.repository }}/compare/$(git describe --tags --abbrev=0 HEAD^)...${GITHUB_REF#refs/tags/}" >> CHANGELOG.md
    
    - name: Create Release
      id: create_release
      uses: actions/create-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: ${{ github.ref_name }}
        release_name: ByteGuardX ${{ github.ref_name }}
        body_path: CHANGELOG.md
        draft: false
        prerelease: ${{ contains(github.ref_name, 'beta') || contains(github.ref_name, 'alpha') }}

  # Build Python Package
  build-python-package:
    name: Build Python Package
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install build dependencies
      run: |
        python -m pip install --upgrade pip
        pip install build twine wheel
    
    - name: Build package
      run: |
        python -m build
    
    - name: Check package
      run: |
        twine check dist/*
    
    - name: Upload to PyPI
      env:
        TWINE_USERNAME: __token__
        TWINE_PASSWORD: ${{ secrets.PYPI_API_TOKEN }}
      run: |
        twine upload dist/*
    
    - name: Upload Python package to release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: dist/byteguardx-*.tar.gz
        asset_name: byteguardx-${{ github.ref_name }}.tar.gz
        asset_content_type: application/gzip

  # Build VS Code Extension
  build-vscode-extension:
    name: Build VS Code Extension
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: extensions/vscode/package-lock.json
    
    - name: Install dependencies
      working-directory: extensions/vscode
      run: npm ci
    
    - name: Update version
      working-directory: extensions/vscode
      run: |
        VERSION=${GITHUB_REF_NAME#v}
        npm version $VERSION --no-git-tag-version
    
    - name: Package extension
      working-directory: extensions/vscode
      run: |
        npm run package
        ls -la *.vsix
    
    - name: Publish to VS Code Marketplace
      working-directory: extensions/vscode
      env:
        VSCE_PAT: ${{ secrets.VSCE_PAT }}
      run: |
        npx vsce publish --packagePath *.vsix
    
    - name: Upload VS Code extension to release
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: extensions/vscode/byteguardx-*.vsix
        asset_name: byteguardx-vscode-${{ github.ref_name }}.vsix
        asset_content_type: application/zip

  # Build Electron Desktop App
  build-electron-app:
    name: Build Electron App
    runs-on: ${{ matrix.os }}
    needs: create-release
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        include:
          - os: ubuntu-latest
            platform: linux
            arch: x64
          - os: windows-latest
            platform: win32
            arch: x64
          - os: macos-latest
            platform: darwin
            arch: x64
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: desktop-app/package-lock.json
    
    - name: Install dependencies
      working-directory: desktop-app
      run: npm ci
    
    - name: Build Electron app
      working-directory: desktop-app
      run: |
        npm run build:${{ matrix.platform }}
    
    - name: Upload Electron app to release (Linux)
      if: matrix.os == 'ubuntu-latest'
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: desktop-app/dist/ByteGuardX-*.AppImage
        asset_name: ByteGuardX-${{ github.ref_name }}-linux.AppImage
        asset_content_type: application/octet-stream
    
    - name: Upload Electron app to release (Windows)
      if: matrix.os == 'windows-latest'
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: desktop-app/dist/ByteGuardX-*.exe
        asset_name: ByteGuardX-${{ github.ref_name }}-windows.exe
        asset_content_type: application/octet-stream
    
    - name: Upload Electron app to release (macOS)
      if: matrix.os == 'macos-latest'
      uses: actions/upload-release-asset@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        upload_url: ${{ needs.create-release.outputs.upload_url }}
        asset_path: desktop-app/dist/ByteGuardX-*.dmg
        asset_name: ByteGuardX-${{ github.ref_name }}-macos.dmg
        asset_content_type: application/octet-stream

  # Build Mobile App
  build-mobile-app:
    name: Build Mobile App
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: mobile-app/package-lock.json
    
    - name: Set up Expo CLI
      run: npm install -g @expo/cli
    
    - name: Install dependencies
      working-directory: mobile-app
      run: npm ci
    
    - name: Build for Android
      working-directory: mobile-app
      env:
        EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
      run: |
        eas build --platform android --non-interactive
    
    - name: Build for iOS
      working-directory: mobile-app
      env:
        EXPO_TOKEN: ${{ secrets.EXPO_TOKEN }}
      run: |
        eas build --platform ios --non-interactive

  # Build Docker Images
  build-docker-images:
    name: Build and Push Docker Images
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
    
    - name: Log in to Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}
    
    - name: Log in to GitHub Container Registry
      uses: docker/login-action@v3
      with:
        registry: ghcr.io
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}
    
    - name: Build and push backend image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.backend
        push: true
        tags: |
          byteguardx/backend:${{ github.ref_name }}
          byteguardx/backend:latest
          ghcr.io/${{ github.repository }}-backend:${{ github.ref_name }}
          ghcr.io/${{ github.repository }}-backend:latest
        platforms: linux/amd64,linux/arm64
    
    - name: Build and push frontend image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: ./Dockerfile.frontend
        push: true
        tags: |
          byteguardx/frontend:${{ github.ref_name }}
          byteguardx/frontend:latest
          ghcr.io/${{ github.repository }}-frontend:${{ github.ref_name }}
          ghcr.io/${{ github.repository }}-frontend:latest
        platforms: linux/amd64,linux/arm64

  # Update Documentation
  update-docs:
    name: Update Documentation
    runs-on: ubuntu-latest
    needs: create-release
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install documentation dependencies
      run: |
        pip install mkdocs mkdocs-material
    
    - name: Build documentation
      run: |
        mkdocs build
    
    - name: Deploy to GitHub Pages
      uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./site

  # Notify Release Completion
  notify-completion:
    name: Notify Release Completion
    runs-on: ubuntu-latest
    needs: [build-python-package, build-vscode-extension, build-electron-app, build-docker-images]
    if: always()
    
    steps:
    - name: Notify success
      if: ${{ needs.build-python-package.result == 'success' }}
      run: |
        echo "🎉 Release ${{ github.ref_name }} completed successfully!"
        echo "✅ Python package published to PyPI"
        echo "✅ VS Code extension published to marketplace"
        echo "✅ Electron apps built for all platforms"
        echo "✅ Docker images pushed to registries"
    
    - name: Notify failure
      if: ${{ needs.build-python-package.result == 'failure' || needs.build-vscode-extension.result == 'failure' }}
      run: |
        echo "❌ Release ${{ github.ref_name }} failed!"
        echo "Please check the logs and retry."
        exit 1
