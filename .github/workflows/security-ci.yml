name: Security CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  schedule:
    # Run security scans daily at 2 AM UTC
    - cron: '0 2 * * *'

env:
  PYTHON_VERSION: '3.9'
  NODE_VERSION: '18'

jobs:
  # Python Security Analysis
  python-security:
    name: Python Security Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 1  # Shallow clone for faster checkout
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Cache Python dependencies
      uses: actions/cache@v3
      with:
        path: ~/.cache/pip
        key: ${{ runner.os }}-pip-${{ hashFiles('**/requirements.txt') }}
        restore-keys: |
          ${{ runner.os }}-pip-
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install bandit safety semgrep pytest pytest-cov
    
    - name: Run Bandit (SAST)
      run: |
        bandit -r byteguardx/ -f json -o bandit-report.json || true
        bandit -r byteguardx/ -f txt
      continue-on-error: true
    
    - name: Run Safety (Dependency Vulnerability Check)
      run: |
        safety check --json --output safety-report.json || true
        safety check
      continue-on-error: true
    
    - name: Run Semgrep (Advanced SAST)
      run: |
        semgrep --config=auto --json --output=semgrep-report.json byteguardx/ || true
        semgrep --config=auto byteguardx/
      continue-on-error: true

    - name: Run pip-audit for dependency vulnerabilities
      run: |
        pip install pip-audit
        pip-audit --desc --format=json --output=pip-audit-report.json || true
        pip-audit --desc
      continue-on-error: true

    - name: Run comprehensive security tests
      run: |
        python -m pytest tests/test_security.py -v
        python -m pytest tests/test_auth.py -v
        python -m pytest tests/test_security_enhancements.py -v
      continue-on-error: true
    
    - name: Upload security reports
      uses: actions/upload-artifact@v3
      with:
        name: python-security-reports
        path: |
          bandit-report.json
          safety-report.json
          semgrep-report.json
          pip-audit-report.json
        retention-days: 30

  # Secrets Scanning
  secrets-scan:
    name: Secrets Scanning
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Run TruffleHog
      uses: trufflesecurity/trufflehog@main
      with:
        path: ./
        base: main
        head: HEAD
        extra_args: --debug --only-verified
    
    - name: Run GitLeaks
      uses: gitleaks/gitleaks-action@v2
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        GITLEAKS_LICENSE: ${{ secrets.GITLEAKS_LICENSE }}

  # Node.js Security Analysis
  nodejs-security:
    name: Node.js Security Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run npm audit
      run: |
        npm audit --audit-level=moderate --json > npm-audit-report.json || true
        npm audit --audit-level=moderate
      continue-on-error: true
    
    - name: Run ESLint Security Plugin
      run: |
        npx eslint src/ --ext .js,.jsx,.ts,.tsx --format json --output-file eslint-security-report.json || true
        npx eslint src/ --ext .js,.jsx,.ts,.tsx
      continue-on-error: true
    
    - name: Upload Node.js security reports
      uses: actions/upload-artifact@v3
      with:
        name: nodejs-security-reports
        path: |
          npm-audit-report.json
          eslint-security-report.json
        retention-days: 30

  # Container Security Scanning
  container-security:
    name: Container Security Scanning
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Build Docker images with cache
      run: |
        docker buildx build --cache-from=type=gha --cache-to=type=gha,mode=max -f Dockerfile.backend -t byteguardx-backend:test . &
        docker buildx build --cache-from=type=gha --cache-to=type=gha,mode=max -f Dockerfile.frontend -t byteguardx-frontend:test . &
        wait
    
    - name: Run Trivy vulnerability scanner (Backend)
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'byteguardx-backend:test'
        format: 'sarif'
        output: 'trivy-backend-results.sarif'
    
    - name: Run Trivy vulnerability scanner (Frontend)
      uses: aquasecurity/trivy-action@master
      with:
        image-ref: 'byteguardx-frontend:test'
        format: 'sarif'
        output: 'trivy-frontend-results.sarif'
    
    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-backend-results.sarif'
    
    - name: Upload Trivy scan results (Frontend)
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-frontend-results.sarif'

  # Security Testing
  security-tests:
    name: Security Feature Tests
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install pytest pytest-cov pytest-mock
    
    - name: Run security feature tests
      run: |
        pytest tests/test_security_features.py -v --cov=byteguardx.security --cov-report=xml --cov-report=html
    
    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage.xml
        flags: security-tests
        name: security-coverage
    
    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: security-test-results
        path: |
          htmlcov/
          coverage.xml
        retention-days: 30

  # License Compliance Check
  license-check:
    name: License Compliance
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install license checker
      run: |
        pip install pip-licenses licensecheck
    
    - name: Check Python licenses
      run: |
        pip-licenses --format=json --output-file=python-licenses.json
        pip-licenses --format=plain
    
    - name: Set up Node.js for license check
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
    
    - name: Install Node.js dependencies
      run: npm ci
    
    - name: Check Node.js licenses
      run: |
        npx license-checker --json --out nodejs-licenses.json
        npx license-checker
    
    - name: Upload license reports
      uses: actions/upload-artifact@v3
      with:
        name: license-reports
        path: |
          python-licenses.json
          nodejs-licenses.json
        retention-days: 30

  # Code Quality and Security Metrics
  code-quality:
    name: Code Quality Analysis
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0
    
    - name: Set up Python
      uses: actions/setup-python@v4
      with:
        python-version: ${{ env.PYTHON_VERSION }}
    
    - name: Install quality tools
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
        pip install flake8 black isort mypy radon
    
    - name: Run Black (Code Formatting)
      run: |
        black --check --diff byteguardx/
    
    - name: Run isort (Import Sorting)
      run: |
        isort --check-only --diff byteguardx/
    
    - name: Run Flake8 (Linting)
      run: |
        flake8 byteguardx/ --max-line-length=100 --extend-ignore=E203,W503
    
    - name: Run MyPy (Type Checking)
      run: |
        mypy byteguardx/ --ignore-missing-imports
      continue-on-error: true
    
    - name: Calculate Code Complexity
      run: |
        radon cc byteguardx/ --json > complexity-report.json
        radon cc byteguardx/ --show-complexity
    
    - name: Upload code quality reports
      uses: actions/upload-artifact@v3
      with:
        name: code-quality-reports
        path: |
          complexity-report.json
        retention-days: 30

  # Security Summary Report
  security-summary:
    name: Security Summary
    runs-on: ubuntu-latest
    needs: [python-security, secrets-scan, nodejs-security, container-security, security-tests]
    if: always()
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Download all artifacts
      uses: actions/download-artifact@v3
    
    - name: Generate Security Summary
      run: |
        echo "# Security CI/CD Summary" > security-summary.md
        echo "" >> security-summary.md
        echo "## Scan Results" >> security-summary.md
        echo "" >> security-summary.md
        
        # Check if security reports exist and summarize
        if [ -f "python-security-reports/bandit-report.json" ]; then
          echo "### Python Security (Bandit)" >> security-summary.md
          echo "- Report generated successfully" >> security-summary.md
          echo "" >> security-summary.md
        fi
        
        if [ -f "python-security-reports/safety-report.json" ]; then
          echo "### Dependency Security (Safety)" >> security-summary.md
          echo "- Vulnerability check completed" >> security-summary.md
          echo "" >> security-summary.md
        fi
        
        if [ -f "nodejs-security-reports/npm-audit-report.json" ]; then
          echo "### Node.js Security (npm audit)" >> security-summary.md
          echo "- Frontend dependency audit completed" >> security-summary.md
          echo "" >> security-summary.md
        fi
        
        echo "### Test Results" >> security-summary.md
        echo "- Security feature tests: ${{ needs.security-tests.result }}" >> security-summary.md
        echo "- Python security analysis: ${{ needs.python-security.result }}" >> security-summary.md
        echo "- Secrets scanning: ${{ needs.secrets-scan.result }}" >> security-summary.md
        echo "- Container security: ${{ needs.container-security.result }}" >> security-summary.md
        echo "" >> security-summary.md
        
        echo "## Recommendations" >> security-summary.md
        echo "1. Review all security reports for critical and high-severity issues" >> security-summary.md
        echo "2. Update dependencies with known vulnerabilities" >> security-summary.md
        echo "3. Address any secrets detected in the codebase" >> security-summary.md
        echo "4. Ensure all security tests are passing" >> security-summary.md
        
        cat security-summary.md
    
    - name: Upload Security Summary
      uses: actions/upload-artifact@v3
      with:
        name: security-summary
        path: security-summary.md
        retention-days: 90
    
    - name: Comment PR with Security Summary
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const summary = fs.readFileSync('security-summary.md', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: summary
          });

  # Deployment Security Check
  deployment-security:
    name: Deployment Security Check
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    needs: [python-security, nodejs-security, security-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
    
    - name: Check deployment readiness
      run: |
        echo "Checking deployment security requirements..."
        
        # Check if critical security tests passed
        if [ "${{ needs.security-tests.result }}" != "success" ]; then
          echo "❌ Security tests failed - blocking deployment"
          exit 1
        fi
        
        echo "✅ Security checks passed - deployment approved"
    
    - name: Notify deployment approval
      run: |
        echo "🚀 Deployment security checks completed successfully"
        echo "All security requirements met for production deployment"
