{"name": "byteguardx-frontend", "version": "1.0.0", "description": "ByteGuardX - AI-Powered Vulnerability Scanner Frontend", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "format": "prettier --write \"src/**/*.{js,jsx,css,md}\"", "vercel-build": "vite build", "start": "node start-byteguardx.js", "start:full": "node start-byteguardx.js"}, "dependencies": {"axios": "1.5.0", "dompurify": "3.0.5", "framer-motion": "10.16.4", "js-cookie": "3.0.5", "lucide-react": "0.288.0", "ogl": "^1.0.11", "react": "18.2.0", "react-dom": "18.2.0", "react-dropzone": "14.2.3", "react-helmet-async": "^2.0.5", "react-hot-toast": "2.4.1", "react-router-dom": "6.16.0"}, "devDependencies": {"@types/react": "18.2.15", "@types/react-dom": "18.2.7", "@vitejs/plugin-react": "4.0.3", "autoprefixer": "10.4.16", "eslint": "8.45.0", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-react-refresh": "0.4.3", "postcss": "8.4.31", "prettier": "3.0.3", "tailwindcss": "3.3.5", "vite": "4.4.5"}, "keywords": ["security", "vulnerability-scanner", "react", "offline-first", "byteguardx"], "author": "ByteGuardX Team", "license": "MIT"}