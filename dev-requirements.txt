# ByteGuardX Development Dependencies
# Install with: pip install -r dev-requirements.txt

# Testing Framework
pytest==7.4.3
pytest-cov==4.1.0
pytest-asyncio==0.21.1
pytest-mock==3.12.0
pytest-xdist==3.3.1  # Parallel test execution

# Code Quality & Formatting
black==23.9.1
flake8==6.1.0
isort==5.12.0
mypy==1.7.1
pre-commit==3.5.0

# Security Testing Tools
bandit==1.7.5  # Python security linter
safety==2.3.5  # Dependency vulnerability scanner
semgrep==1.45.0  # Advanced SAST
pip-audit==2.6.1  # Vulnerability scanner

# Documentation
sphinx==7.2.6
sphinx-rtd-theme==1.3.0
mkdocs==1.5.3
mkdocs-material==9.4.8

# Performance Profiling
py-spy==0.3.14
memory-profiler==0.61.0
line-profiler==4.1.1

# Development Tools
ipython==8.17.2
jupyter==1.0.0
notebook==7.0.6

# API Testing
httpx==0.25.2  # Modern HTTP client for testing
responses==0.24.1  # Mock HTTP responses

# Database Development
alembic==1.12.1  # Database migrations
sqlalchemy-utils==0.41.1  # SQLAlchemy utilities

# Debugging
pdb++==0.10.3
icecream==2.1.3

# Build Tools
build==1.0.3
twine==4.0.2
wheel==0.42.0

# License Checking
pip-licenses==4.3.2
licensecheck==2023.1.1

# Dependency Analysis
pipdeptree==2.13.1
pip-tools==7.3.0

# Container Development
docker-compose==1.29.2

# Load Testing
locust==2.17.0

# Mock Services
responses==0.24.1
httpretty==1.1.4

# Git Hooks
pre-commit==3.5.0

# Environment Management
python-dotenv==1.0.0  # Also needed in production

# Optional Heavy ML Dependencies (install separately if needed)
# These are commented out to reduce default installation size
# Uncomment and install only if using AI features

# Core ML (lightweight)
# scikit-learn==1.3.0
# numpy==1.24.3

# Deep Learning (heavy - install only if needed)
# torch==2.1.0+cpu  # CPU-only version
# transformers==4.35.0
# onnxruntime==1.16.0

# Data Analysis (heavy - install only if needed)
# pandas==2.0.3
# matplotlib==3.7.2
# seaborn==0.12.2
# plotly==5.17.0

# Enterprise Features (install only if needed)
# python-saml==1.15.0
# xmlsec==1.3.13
# ldap3==2.9.1

# Message Queue (install only if needed)
# celery==5.3.4
# redis==4.6.0

# Container Support (install only if needed)
# docker==6.1.3
# kubernetes==28.1.0
