version: '3.8'

services:
  # Redis for Celery broker and caching
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes --maxmemory 512mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - byteguardx-network

  # PostgreSQL database with optimizations
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: byteguardx
      POSTGRES_USER: byteguardx
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_change_in_production}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    command: >
      postgres
      -c shared_preload_libraries=pg_stat_statements
      -c max_connections=200
      -c shared_buffers=256MB
      -c effective_cache_size=1GB
      -c maintenance_work_mem=64MB
      -c checkpoint_completion_target=0.9
      -c wal_buffers=16MB
      -c default_statistics_target=100
      -c random_page_cost=1.1
      -c effective_io_concurrency=200
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U byteguardx"]
      interval: 10s
      timeout: 5s
      retries: 3
    networks:
      - byteguardx-network

  # Main API service
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=postgresql://byteguardx:${POSTGRES_PASSWORD:-secure_password_change_in_production}@postgres:5432/byteguardx
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - BYTEGUARDX_MASTER_KEY=${BYTEGUARDX_MASTER_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./reports:/app/reports
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - byteguardx-network
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '1.0'

  # Celery worker for general tasks with autoscaling
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    environment:
      - DATABASE_URL=postgresql://byteguardx:${POSTGRES_PASSWORD:-secure_password_change_in_production}@postgres:5432/byteguardx
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - BYTEGUARDX_MASTER_KEY=${BYTEGUARDX_MASTER_KEY}
      - CELERY_WORKER_AUTOSCALE=8,2
      - CELERY_WORKER_MAX_TASKS_PER_CHILD=1000
      - CELERY_WORKER_PREFETCH_MULTIPLIER=1
      - CELERY_WORKER_CONCURRENCY=4
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./reports:/app/reports
    command: celery -A byteguardx.tasks.celery_app worker --loglevel=info --queues=default,scans,notifications,reports --autoscale=8,2 --max-tasks-per-child=1000
    healthcheck:
      test: ["CMD", "celery", "-A", "byteguardx.tasks.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - byteguardx-network
    deploy:
      replicas: 3
      update_config:
        parallelism: 1
        delay: 10s
        failure_action: rollback
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
      resources:
        limits:
          memory: 2G
          cpus: '2.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Celery worker for ML/AI tasks (with GPU support)
  ml-worker:
    build:
      context: .
      dockerfile: Dockerfile.ml-worker
    environment:
      - DATABASE_URL=postgresql://byteguardx:${POSTGRES_PASSWORD:-secure_password_change_in_production}@postgres:5432/byteguardx
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - BYTEGUARDX_MASTER_KEY=${BYTEGUARDX_MASTER_KEY}
      - CUDA_VISIBLE_DEVICES=0
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
      - ./ml/models:/app/ml/models
    command: celery -A byteguardx.tasks.celery_app worker --loglevel=info --queues=ml_inference --concurrency=2
    # Uncomment for GPU support
    # runtime: nvidia
    # environment:
    #   - NVIDIA_VISIBLE_DEVICES=all
    healthcheck:
      test: ["CMD", "celery", "-A", "byteguardx.tasks.celery_app", "inspect", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - byteguardx-network
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 4G
          cpus: '2.0'

  # Celery beat scheduler
  beat:
    build:
      context: .
      dockerfile: Dockerfile.worker
    environment:
      - DATABASE_URL=postgresql://byteguardx:${POSTGRES_PASSWORD:-secure_password_change_in_production}@postgres:5432/byteguardx
      - REDIS_URL=redis://redis:6379/0
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - JWT_SECRET_KEY=${JWT_SECRET_KEY}
      - BYTEGUARDX_MASTER_KEY=${BYTEGUARDX_MASTER_KEY}
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./data:/app/data
      - ./logs:/app/logs
    command: celery -A byteguardx.tasks.celery_app beat --loglevel=info
    networks:
      - byteguardx-network
    deploy:
      replicas: 1
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # Flower for Celery monitoring
  flower:
    build:
      context: .
      dockerfile: Dockerfile.worker
    ports:
      - "5555:5555"
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
    depends_on:
      - redis
    command: celery -A byteguardx.tasks.celery_app flower --port=5555
    networks:
      - byteguardx-network

  # Nginx load balancer
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
    depends_on:
      - api
    networks:
      - byteguardx-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'

  # Prometheus for monitoring
  prometheus:
    image: prom/prometheus:latest
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
    networks:
      - byteguardx-network

  # Grafana for visualization
  grafana:
    image: grafana/grafana:latest
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources
    networks:
      - byteguardx-network

volumes:
  postgres_data:
  redis_data:
  prometheus_data:
  grafana_data:

networks:
  byteguardx-network:
    driver: bridge
