#!/usr/bin/env python3
"""
ByteGuardX Complete Setup and Run Script
Automatically installs dependencies and runs the entire ByteGuardX application
"""

import os
import sys
import subprocess
import time
import logging
import webbrowser
import threading
from pathlib import Path
from typing import List, Optional
import platform

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ByteGuardXSetup:
    """Complete ByteGuardX setup and launcher"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.python_executable = sys.executable
        self.is_windows = platform.system() == "Windows"
        self.processes = []
        
        logger.info("ByteGuardX Setup initialized")
    
    def check_system_requirements(self) -> bool:
        """Check system requirements"""
        logger.info("🔍 Checking system requirements...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            logger.error("❌ Python 3.8+ required")
            return False
        
        logger.info(f"✅ Python {sys.version.split()[0]} detected")
        
        # Check Node.js
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ Node.js {result.stdout.strip()} detected")
            else:
                logger.error("❌ Node.js not found. Please install Node.js 16+")
                return False
        except FileNotFoundError:
            logger.error("❌ Node.js not found. Please install Node.js 16+")
            return False
        
        # Check npm
        try:
            result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                logger.info(f"✅ npm {result.stdout.strip()} detected")
            else:
                logger.error("❌ npm not found")
                return False
        except FileNotFoundError:
            logger.error("❌ npm not found")
            return False
        
        return True
    
    def install_python_dependencies(self) -> bool:
        """Install Python dependencies"""
        logger.info("📦 Installing Python dependencies...")
        
        try:
            # Upgrade pip first
            subprocess.run([
                self.python_executable, '-m', 'pip', 'install', '--upgrade', 'pip'
            ], check=True)
            
            # Install core requirements
            requirements_file = self.project_root / 'requirements.txt'
            if requirements_file.exists():
                logger.info("Installing from requirements.txt...")
                subprocess.run([
                    self.python_executable, '-m', 'pip', 'install', '-r', str(requirements_file)
                ], check=True)
            
            # Install additional dependencies for full functionality
            additional_deps = [
                'aiofiles==23.2.1',
                'aiohttp==3.9.1', 
                'numpy==1.24.3',
                'pandas==2.0.3',
                'matplotlib==3.7.2',
                'plotly==5.17.0',
                'Jinja2==3.1.2',
                'reportlab==4.0.7',
                'Pillow==10.1.0'
            ]
            
            logger.info("Installing additional dependencies...")
            subprocess.run([
                self.python_executable, '-m', 'pip', 'install'
            ] + additional_deps, check=True)
            
            logger.info("✅ Python dependencies installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install Python dependencies: {e}")
            return False
    
    def install_frontend_dependencies(self) -> bool:
        """Install frontend dependencies"""
        frontend_dir = self.project_root / 'frontend'
        
        if not frontend_dir.exists():
            logger.warning("⚠️ Frontend directory not found, skipping frontend setup")
            return True
        
        logger.info("📦 Installing frontend dependencies...")
        
        try:
            # Install npm dependencies
            subprocess.run(['npm', 'install'], cwd=str(frontend_dir), check=True)
            logger.info("✅ Frontend dependencies installed successfully")
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"❌ Failed to install frontend dependencies: {e}")
            return False
    
    def create_required_directories(self):
        """Create required directories"""
        logger.info("📁 Creating required directories...")
        
        directories = [
            'data',
            'data/audit_logs',
            'data/ml',
            'data/plugins',
            'data/secure',
            'logs',
            'reports',
            'reports/generated',
            'models',
            'models/trained'
        ]
        
        for dir_name in directories:
            dir_path = self.project_root / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
        
        logger.info("✅ Required directories created")
    
    def initialize_database(self) -> bool:
        """Initialize the database"""
        logger.info("🗄️ Initializing database...")
        
        try:
            # Add project root to Python path
            sys.path.insert(0, str(self.project_root))
            
            # Initialize database
            from byteguardx.database.database_manager import DatabaseManager
            db_manager = DatabaseManager()
            db_manager.initialize_database()
            
            logger.info("✅ Database initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            # Create a simple SQLite database as fallback
            try:
                import sqlite3
                db_path = self.project_root / 'byteguardx.db'
                conn = sqlite3.connect(str(db_path))
                conn.execute('''
                    CREATE TABLE IF NOT EXISTS scans (
                        id INTEGER PRIMARY KEY,
                        timestamp TEXT,
                        file_path TEXT,
                        findings TEXT
                    )
                ''')
                conn.commit()
                conn.close()
                logger.info("✅ Fallback SQLite database created")
                return True
            except Exception as e2:
                logger.error(f"❌ Fallback database creation failed: {e2}")
                return False
    
    def start_backend_server(self) -> Optional[subprocess.Popen]:
        """Start the backend API server"""
        logger.info("🚀 Starting ByteGuardX backend server...")
        
        try:
            # Set environment variables
            env = os.environ.copy()
            env['PYTHONPATH'] = str(self.project_root)
            env['FLASK_ENV'] = 'development'
            env['BYTEGUARDX_PORT'] = '5000'
            
            # Start the API server
            api_script = self.project_root / 'byteguardx' / 'api' / 'app.py'
            
            if api_script.exists():
                process = subprocess.Popen([
                    self.python_executable, str(api_script)
                ], env=env, cwd=str(self.project_root))
            else:
                # Fallback to run_byteguardx.py
                process = subprocess.Popen([
                    self.python_executable, 'run_byteguardx.py', '--no-browser'
                ], env=env, cwd=str(self.project_root))
            
            self.processes.append(process)
            
            # Wait for server to start
            if self._wait_for_service('localhost', 5000, timeout=30):
                logger.info("✅ Backend server started on http://localhost:5000")
                return process
            else:
                logger.error("❌ Backend server failed to start")
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to start backend server: {e}")
            return None
    
    def start_frontend_server(self) -> Optional[subprocess.Popen]:
        """Start the frontend development server"""
        frontend_dir = self.project_root / 'frontend'
        
        if not frontend_dir.exists():
            logger.warning("⚠️ Frontend directory not found, skipping frontend startup")
            return None
        
        logger.info("🚀 Starting React frontend server...")
        
        try:
            # Set environment variables
            env = os.environ.copy()
            env['PORT'] = '3000'
            env['REACT_APP_API_URL'] = 'http://localhost:5000'
            env['BROWSER'] = 'none'  # Don't auto-open browser
            
            # Start React development server
            process = subprocess.Popen([
                'npm', 'start'
            ], cwd=str(frontend_dir), env=env)
            
            self.processes.append(process)
            
            # Wait for frontend to start
            if self._wait_for_service('localhost', 3000, timeout=60):
                logger.info("✅ Frontend server started on http://localhost:3000")
                return process
            else:
                logger.error("❌ Frontend server failed to start")
                return None
                
        except Exception as e:
            logger.error(f"❌ Failed to start frontend server: {e}")
            return None
    
    def _wait_for_service(self, host: str, port: int, timeout: int = 30) -> bool:
        """Wait for a service to become available"""
        import socket
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                with socket.create_connection((host, port), timeout=1):
                    return True
            except (socket.error, ConnectionRefusedError):
                time.sleep(1)
        
        return False
    
    def open_browser_interfaces(self):
        """Open browser with ByteGuardX interfaces"""
        logger.info("🌐 Opening browser interfaces...")
        
        # Wait for services to fully start
        time.sleep(3)
        
        try:
            # Open main frontend
            webbrowser.open('http://localhost:3000')
            
            # Open API documentation after a delay
            def open_api_docs():
                time.sleep(2)
                webbrowser.open('http://localhost:5000/health')
            
            threading.Thread(target=open_api_docs, daemon=True).start()
            
        except Exception as e:
            logger.error(f"❌ Failed to open browser: {e}")
    
    def display_success_message(self):
        """Display success message with URLs"""
        print("\n" + "="*70)
        print("🎉 ByteGuardX Successfully Started!")
        print("="*70)
        print("🌐 Frontend Dashboard:  http://localhost:3000")
        print("🔧 API Server:          http://localhost:5000")
        print("📊 Health Check:        http://localhost:5000/health")
        print("📚 API Documentation:   http://localhost:5000/docs")
        print("="*70)
        print("🚀 All services are running!")
        print("📝 Press Ctrl+C to stop all services")
        print("💡 Check the logs above for any issues")
        print("="*70 + "\n")
    
    def cleanup(self):
        """Clean up processes"""
        logger.info("🧹 Cleaning up processes...")
        
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                process.kill()
            except Exception as e:
                logger.error(f"Error cleaning up process: {e}")
    
    def run_complete_setup(self) -> bool:
        """Run complete setup and launch"""
        try:
            print("🛡️ ByteGuardX Complete Setup & Launch")
            print("="*50)
            
            # Check system requirements
            if not self.check_system_requirements():
                return False
            
            # Install dependencies
            if not self.install_python_dependencies():
                return False
            
            if not self.install_frontend_dependencies():
                return False
            
            # Create directories and initialize
            self.create_required_directories()
            
            if not self.initialize_database():
                logger.warning("⚠️ Database initialization failed, continuing with limited functionality")
            
            # Start services
            backend_process = self.start_backend_server()
            if not backend_process:
                return False
            
            frontend_process = self.start_frontend_server()
            # Frontend is optional, continue even if it fails
            
            # Display success message
            self.display_success_message()
            
            # Open browser
            self.open_browser_interfaces()
            
            # Keep running
            try:
                while True:
                    time.sleep(1)
                    # Check if backend process is still running
                    if backend_process.poll() is not None:
                        logger.error("❌ Backend process died")
                        break
            except KeyboardInterrupt:
                logger.info("👋 Shutting down...")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Setup failed: {e}")
            return False
        finally:
            self.cleanup()

def main():
    """Main entry point"""
    setup = ByteGuardXSetup()
    
    try:
        success = setup.run_complete_setup()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("👋 Setup cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
