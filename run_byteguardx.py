#!/usr/bin/env python3
"""
ByteGuardX Complete System Launcher
Single command to run the entire ByteGuardX application (frontend + backend)
"""

import os
import sys
import subprocess
import threading
import time
import signal
import logging
from pathlib import Path
from typing import List, Optional
import asyncio
import webbrowser
from concurrent.futures import ThreadPoolExecutor

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from byteguardx.core.system_architecture import byteguardx_core
from byteguardx.dashboard.interactive_dashboard import dashboard

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class ByteGuardXLauncher:
    """
    Complete ByteGuardX system launcher
    Manages backend, frontend, and all system components
    """
    
    def __init__(self):
        self.processes: List[subprocess.Popen] = []
        self.threads: List[threading.Thread] = []
        self.is_running = False
        self.backend_port = 5000
        self.frontend_port = 3000
        self.dashboard_port = 8080
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
        
        logger.info("ByteGuardX Launcher initialized")
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down...")
        self.shutdown()
        sys.exit(0)
    
    def check_dependencies(self) -> bool:
        """Check if all required dependencies are available"""
        logger.info("Checking system dependencies...")
        
        required_commands = ['python', 'node', 'npm']
        missing_commands = []
        
        for cmd in required_commands:
            if not self._command_exists(cmd):
                missing_commands.append(cmd)
        
        if missing_commands:
            logger.error(f"Missing required commands: {', '.join(missing_commands)}")
            return False
        
        # Check Python packages
        required_packages = [
            'flask', 'flask-socketio', 'asyncio', 'aiohttp',
            'cryptography', 'numpy', 'pandas', 'plotly'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace('-', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"Missing Python packages: {', '.join(missing_packages)}")
            logger.info("Install with: pip install " + " ".join(missing_packages))
            return False
        
        # Check Node.js packages for frontend
        frontend_dir = project_root / "frontend"
        if frontend_dir.exists():
            node_modules = frontend_dir / "node_modules"
            if not node_modules.exists():
                logger.warning("Frontend dependencies not installed")
                logger.info("Run: cd frontend && npm install")
                return False
        
        logger.info("✅ All dependencies satisfied")
        return True
    
    def _command_exists(self, command: str) -> bool:
        """Check if a command exists in PATH"""
        try:
            subprocess.run([command, '--version'], 
                         capture_output=True, 
                         check=True)
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            return False
    
    async def initialize_backend(self) -> bool:
        """Initialize ByteGuardX backend system"""
        logger.info("Initializing ByteGuardX backend...")
        
        try:
            # Initialize core system
            success = await byteguardx_core.initialize_system()
            if not success:
                logger.error("Failed to initialize ByteGuardX core system")
                return False
            
            logger.info("✅ ByteGuardX backend initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Backend initialization failed: {e}")
            return False
    
    def start_backend_api(self) -> bool:
        """Start the Flask API server"""
        logger.info(f"Starting ByteGuardX API server on port {self.backend_port}...")
        
        try:
            # Start Flask API server
            api_script = project_root / "byteguardx" / "api" / "app.py"
            
            if not api_script.exists():
                logger.error(f"API script not found: {api_script}")
                return False
            
            env = os.environ.copy()
            env['FLASK_APP'] = str(api_script)
            env['FLASK_ENV'] = 'production'
            env['BYTEGUARDX_PORT'] = str(self.backend_port)
            
            process = subprocess.Popen([
                sys.executable, '-m', 'flask', 'run',
                '--host', '0.0.0.0',
                '--port', str(self.backend_port)
            ], env=env, cwd=str(project_root))
            
            self.processes.append(process)
            
            # Wait for server to start
            if self._wait_for_service('localhost', self.backend_port, timeout=30):
                logger.info(f"✅ API server started on http://localhost:{self.backend_port}")
                return True
            else:
                logger.error("API server failed to start within timeout")
                return False
                
        except Exception as e:
            logger.error(f"Failed to start API server: {e}")
            return False
    
    def start_frontend(self) -> bool:
        """Start the React frontend"""
        frontend_dir = project_root / "frontend"
        
        if not frontend_dir.exists():
            logger.warning("Frontend directory not found, skipping frontend startup")
            return True
        
        logger.info(f"Starting React frontend on port {self.frontend_port}...")
        
        try:
            # Set environment variables
            env = os.environ.copy()
            env['PORT'] = str(self.frontend_port)
            env['REACT_APP_API_URL'] = f'http://localhost:{self.backend_port}'
            env['BROWSER'] = 'none'  # Don't auto-open browser
            
            # Start React development server
            process = subprocess.Popen([
                'npm', 'start'
            ], cwd=str(frontend_dir), env=env)
            
            self.processes.append(process)
            
            # Wait for frontend to start
            if self._wait_for_service('localhost', self.frontend_port, timeout=60):
                logger.info(f"✅ Frontend started on http://localhost:{self.frontend_port}")
                return True
            else:
                logger.error("Frontend failed to start within timeout")
                return False
                
        except Exception as e:
            logger.error(f"Failed to start frontend: {e}")
            return False
    
    def start_dashboard(self) -> bool:
        """Start the interactive dashboard"""
        logger.info(f"Starting interactive dashboard on port {self.dashboard_port}...")
        
        try:
            # Start dashboard in a separate thread
            def run_dashboard():
                try:
                    dashboard.run(host='127.0.0.1', debug=False)
                except Exception as e:
                    logger.error(f"Dashboard error: {e}")
            
            dashboard_thread = threading.Thread(target=run_dashboard, daemon=True)
            dashboard_thread.start()
            self.threads.append(dashboard_thread)
            
            # Wait for dashboard to start
            if self._wait_for_service('localhost', self.dashboard_port, timeout=30):
                logger.info(f"✅ Dashboard started on http://localhost:{self.dashboard_port}")
                return True
            else:
                logger.error("Dashboard failed to start within timeout")
                return False
                
        except Exception as e:
            logger.error(f"Failed to start dashboard: {e}")
            return False
    
    def _wait_for_service(self, host: str, port: int, timeout: int = 30) -> bool:
        """Wait for a service to become available"""
        import socket
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                with socket.create_connection((host, port), timeout=1):
                    return True
            except (socket.error, ConnectionRefusedError):
                time.sleep(1)
        
        return False
    
    def open_browser(self):
        """Open browser with ByteGuardX interfaces"""
        logger.info("Opening browser...")
        
        # Wait a moment for services to fully start
        time.sleep(2)
        
        try:
            # Open main frontend
            webbrowser.open(f'http://localhost:{self.frontend_port}')
            
            # Open dashboard in a new tab after a delay
            def open_dashboard():
                time.sleep(3)
                webbrowser.open(f'http://localhost:{self.dashboard_port}')
            
            threading.Thread(target=open_dashboard, daemon=True).start()
            
        except Exception as e:
            logger.error(f"Failed to open browser: {e}")
    
    def display_startup_info(self):
        """Display startup information"""
        print("\n" + "="*60)
        print("🛡️  ByteGuardX - AI-Powered Security Scanner")
        print("="*60)
        print(f"📊 Frontend:    http://localhost:{self.frontend_port}")
        print(f"🔧 API Server:  http://localhost:{self.backend_port}")
        print(f"📈 Dashboard:   http://localhost:{self.dashboard_port}")
        print("="*60)
        print("🚀 All services are running!")
        print("📝 Press Ctrl+C to stop all services")
        print("="*60 + "\n")
    
    def monitor_processes(self):
        """Monitor running processes and restart if needed"""
        while self.is_running:
            try:
                # Check if any process has died
                for i, process in enumerate(self.processes):
                    if process.poll() is not None:
                        logger.warning(f"Process {i} has died, return code: {process.returncode}")
                        # Could implement restart logic here
                
                time.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                logger.error(f"Process monitoring error: {e}")
                time.sleep(10)
    
    def shutdown(self):
        """Shutdown all services"""
        logger.info("Shutting down ByteGuardX services...")
        self.is_running = False
        
        # Terminate all processes
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                logger.warning("Force killing process")
                process.kill()
            except Exception as e:
                logger.error(f"Error terminating process: {e}")
        
        # Stop dashboard
        try:
            dashboard.stop()
        except Exception as e:
            logger.error(f"Error stopping dashboard: {e}")
        
        logger.info("✅ All services stopped")
    
    async def run(self, open_browser: bool = True):
        """Run the complete ByteGuardX system"""
        try:
            # Check dependencies
            if not self.check_dependencies():
                logger.error("❌ Dependency check failed")
                return False
            
            # Initialize backend
            if not await self.initialize_backend():
                logger.error("❌ Backend initialization failed")
                return False
            
            self.is_running = True
            
            # Start services
            services = [
                ("API Server", self.start_backend_api),
                ("Frontend", self.start_frontend),
                ("Dashboard", self.start_dashboard)
            ]
            
            for service_name, start_func in services:
                logger.info(f"Starting {service_name}...")
                if not start_func():
                    logger.error(f"❌ Failed to start {service_name}")
                    self.shutdown()
                    return False
            
            # Display startup info
            self.display_startup_info()
            
            # Open browser
            if open_browser:
                self.open_browser()
            
            # Start process monitoring
            monitor_thread = threading.Thread(target=self.monitor_processes, daemon=True)
            monitor_thread.start()
            self.threads.append(monitor_thread)
            
            # Keep main thread alive
            try:
                while self.is_running:
                    time.sleep(1)
            except KeyboardInterrupt:
                logger.info("Received keyboard interrupt")
            
            return True
            
        except Exception as e:
            logger.error(f"System startup failed: {e}")
            return False
        finally:
            self.shutdown()

def main():
    """Main entry point"""
    import argparse
    
    parser = argparse.ArgumentParser(description='ByteGuardX Complete System Launcher')
    parser.add_argument('--no-browser', action='store_true', 
                       help='Do not open browser automatically')
    parser.add_argument('--backend-port', type=int, default=5000,
                       help='Backend API port (default: 5000)')
    parser.add_argument('--frontend-port', type=int, default=3000,
                       help='Frontend port (default: 3000)')
    parser.add_argument('--dashboard-port', type=int, default=8080,
                       help='Dashboard port (default: 8080)')
    
    args = parser.parse_args()
    
    # Create launcher
    launcher = ByteGuardXLauncher()
    launcher.backend_port = args.backend_port
    launcher.frontend_port = args.frontend_port
    launcher.dashboard_port = args.dashboard_port
    
    # Run system
    try:
        success = asyncio.run(launcher.run(open_browser=not args.no_browser))
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        logger.info("Startup cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Startup failed: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
