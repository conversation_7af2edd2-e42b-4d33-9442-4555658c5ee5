#!/usr/bin/env python3
"""
ByteGuardX Simple Launcher
Minimal version that works without heavy ML dependencies
"""

import os
import sys
import logging
import webbrowser
import time
from pathlib import Path
from flask import Flask, render_template_string, request, jsonify
from flask_cors import CORS
import re
import json
from datetime import datetime

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Create Flask app
app = Flask(__name__)
CORS(app)

# Simple vulnerability patterns
VULNERABILITY_PATTERNS = {
    'sql_injection': [
        r'SELECT\s+.*\s+FROM\s+.*\s+WHERE\s+.*\+',
        r'INSERT\s+INTO\s+.*\s+VALUES\s*\(.*\+',
        r'UPDATE\s+.*\s+SET\s+.*\s+WHERE\s+.*\+',
        r'DELETE\s+FROM\s+.*\s+WHERE\s+.*\+',
        r'query\s*=\s*["\'].*["\']\s*\+',
        r'execute\s*\(\s*["\'].*["\']\s*\+',
    ],
    'xss': [
        r'innerHTML\s*=\s*.*\+',
        r'document\.write\s*\(\s*.*\+',
        r'eval\s*\(\s*.*\+',
        r'setTimeout\s*\(\s*["\'].*["\']\s*\+',
        r'setInterval\s*\(\s*["\'].*["\']\s*\+',
    ],
    'command_injection': [
        r'exec\s*\(\s*.*\+',
        r'system\s*\(\s*.*\+',
        r'shell_exec\s*\(\s*.*\+',
        r'passthru\s*\(\s*.*\+',
        r'os\.system\s*\(\s*.*\+',
        r'subprocess\.\w+\s*\(\s*.*\+',
    ],
    'hardcoded_secrets': [
        r'password\s*=\s*["\'][^"\']{8,}["\']',
        r'api_key\s*=\s*["\'][^"\']{20,}["\']',
        r'secret\s*=\s*["\'][^"\']{16,}["\']',
        r'token\s*=\s*["\'][^"\']{20,}["\']',
        r'key\s*=\s*["\'][^"\']{16,}["\']',
    ],
    'path_traversal': [
        r'\.\./',
        r'\.\.\\',
        r'%2e%2e%2f',
        r'%2e%2e\\',
    ]
}

def scan_code(content, file_path="unknown"):
    """Simple pattern-based vulnerability scanning"""
    findings = []
    lines = content.split('\n')
    
    for line_num, line in enumerate(lines, 1):
        for vuln_type, patterns in VULNERABILITY_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    severity = 'high' if vuln_type in ['sql_injection', 'command_injection'] else 'medium'
                    
                    findings.append({
                        'id': f"{vuln_type}_{line_num}",
                        'type': vuln_type,
                        'severity': severity,
                        'title': f"{vuln_type.replace('_', ' ').title()} Vulnerability",
                        'description': f"Potential {vuln_type.replace('_', ' ')} vulnerability detected",
                        'file_path': file_path,
                        'line_start': line_num,
                        'line_end': line_num,
                        'evidence': line.strip(),
                        'confidence': 0.8,
                        'remediation': get_remediation(vuln_type)
                    })
    
    return findings

def get_remediation(vuln_type):
    """Get remediation advice for vulnerability type"""
    remediation_map = {
        'sql_injection': 'Use parameterized queries or prepared statements',
        'xss': 'Sanitize user input and use proper output encoding',
        'command_injection': 'Avoid executing user input as system commands',
        'hardcoded_secrets': 'Store secrets in environment variables or secure vaults',
        'path_traversal': 'Validate and sanitize file paths'
    }
    return remediation_map.get(vuln_type, 'Review and fix the identified issue')

# HTML Template
HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ByteGuardX - AI Security Scanner</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            font-size: 3rem;
            background: linear-gradient(45deg, #00ffff, #0080ff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2rem;
            color: #cccccc;
        }
        
        .upload-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .upload-area {
            border: 2px dashed #00ffff;
            border-radius: 10px;
            padding: 40px;
            margin: 20px 0;
            transition: all 0.3s ease;
        }
        
        .upload-area:hover {
            border-color: #0080ff;
            background: rgba(0, 255, 255, 0.05);
        }
        
        .upload-area.dragover {
            border-color: #0080ff;
            background: rgba(0, 255, 255, 0.1);
        }
        
        .file-input {
            display: none;
        }
        
        .upload-btn {
            background: linear-gradient(45deg, #00ffff, #0080ff);
            color: #000;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .upload-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 255, 255, 0.3);
        }
        
        .results-section {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            display: none;
        }
        
        .finding {
            background: rgba(255, 255, 255, 0.03);
            border-left: 4px solid #ff4444;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .finding.high {
            border-left-color: #ff4444;
        }
        
        .finding.medium {
            border-left-color: #ffaa00;
        }
        
        .finding.low {
            border-left-color: #ffff44;
        }
        
        .finding-title {
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .finding-meta {
            color: #cccccc;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }
        
        .finding-evidence {
            background: rgba(0, 0, 0, 0.3);
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
        }
        
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.1);
            border-left: 4px solid #00ffff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .stats {
            display: flex;
            justify-content: space-around;
            margin-bottom: 30px;
        }
        
        .stat {
            text-align: center;
        }
        
        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #00ffff;
        }
        
        .stat-label {
            color: #cccccc;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛡️ ByteGuardX</h1>
            <p>AI-Powered Security Vulnerability Scanner</p>
        </div>
        
        <div class="upload-section">
            <h2>Upload Code for Security Analysis</h2>
            <div class="upload-area" id="uploadArea">
                <p>📁 Drag and drop your code files here</p>
                <p>or</p>
                <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                    Choose Files
                </button>
                <input type="file" id="fileInput" class="file-input" multiple accept=".py,.js,.java,.go,.php,.rb,.cpp,.c,.cs,.ts">
            </div>
        </div>
        
        <div class="results-section" id="resultsSection">
            <div class="loading" id="loading">
                <div class="spinner"></div>
                <p>Scanning for vulnerabilities...</p>
            </div>
            
            <div id="results" style="display: none;">
                <div class="stats" id="stats"></div>
                <div id="findings"></div>
            </div>
        </div>
    </div>
    
    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const resultsSection = document.getElementById('resultsSection');
        const loading = document.getElementById('loading');
        const results = document.getElementById('results');
        
        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });
        
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });
        
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            handleFiles(files);
        });
        
        fileInput.addEventListener('change', (e) => {
            handleFiles(e.target.files);
        });
        
        async function handleFiles(files) {
            if (files.length === 0) return;
            
            resultsSection.style.display = 'block';
            loading.style.display = 'block';
            results.style.display = 'none';
            
            const allFindings = [];
            
            for (let file of files) {
                const content = await readFile(file);
                const response = await fetch('/scan', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        content: content,
                        file_path: file.name
                    })
                });
                
                const data = await response.json();
                allFindings.push(...data.findings);
            }
            
            displayResults(allFindings);
        }
        
        function readFile(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (e) => resolve(e.target.result);
                reader.onerror = reject;
                reader.readAsText(file);
            });
        }
        
        function displayResults(findings) {
            loading.style.display = 'none';
            results.style.display = 'block';
            
            // Display stats
            const stats = {
                total: findings.length,
                high: findings.filter(f => f.severity === 'high').length,
                medium: findings.filter(f => f.severity === 'medium').length,
                low: findings.filter(f => f.severity === 'low').length
            };
            
            document.getElementById('stats').innerHTML = `
                <div class="stat">
                    <div class="stat-number">${stats.total}</div>
                    <div class="stat-label">Total Issues</div>
                </div>
                <div class="stat">
                    <div class="stat-number">${stats.high}</div>
                    <div class="stat-label">High Severity</div>
                </div>
                <div class="stat">
                    <div class="stat-number">${stats.medium}</div>
                    <div class="stat-label">Medium Severity</div>
                </div>
                <div class="stat">
                    <div class="stat-number">${stats.low}</div>
                    <div class="stat-label">Low Severity</div>
                </div>
            `;
            
            // Display findings
            const findingsHtml = findings.map(finding => `
                <div class="finding ${finding.severity}">
                    <div class="finding-title">${finding.title}</div>
                    <div class="finding-meta">
                        📁 ${finding.file_path} | 📍 Line ${finding.line_start} | 🔥 ${finding.severity.toUpperCase()}
                    </div>
                    <p>${finding.description}</p>
                    <div class="finding-evidence">${finding.evidence}</div>
                    <p><strong>💡 Remediation:</strong> ${finding.remediation}</p>
                </div>
            `).join('');
            
            document.getElementById('findings').innerHTML = findingsHtml || '<p>🎉 No vulnerabilities found!</p>';
        }
    </script>
</body>
</html>
"""

@app.route('/')
def index():
    """Main page"""
    return render_template_string(HTML_TEMPLATE)

@app.route('/scan', methods=['POST'])
def scan_endpoint():
    """Scan endpoint"""
    try:
        data = request.get_json()
        content = data.get('content', '')
        file_path = data.get('file_path', 'unknown')
        
        findings = scan_code(content, file_path)
        
        return jsonify({
            'success': True,
            'findings': findings,
            'scan_id': f"scan_{int(time.time())}",
            'timestamp': datetime.now().isoformat()
        })
    
    except Exception as e:
        logger.error(f"Scan failed: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'ByteGuardX Simple Scanner',
        'version': '1.0.0',
        'timestamp': datetime.now().isoformat()
    })

def main():
    """Main entry point"""
    print("🛡️ ByteGuardX Simple Scanner")
    print("="*50)
    
    # Create required directories
    Path('data').mkdir(exist_ok=True)
    Path('logs').mkdir(exist_ok=True)
    
    logger.info("Starting ByteGuardX Simple Scanner...")
    
    # Open browser
    def open_browser():
        time.sleep(1)
        webbrowser.open('http://localhost:5000')
    
    import threading
    threading.Thread(target=open_browser, daemon=True).start()
    
    print("\n" + "="*60)
    print("🎉 ByteGuardX Simple Scanner Started!")
    print("="*60)
    print("🌐 Web Interface: http://localhost:5000")
    print("📊 Health Check:  http://localhost:5000/health")
    print("="*60)
    print("📝 Press Ctrl+C to stop the application")
    print("="*60 + "\n")
    
    # Start Flask app
    try:
        app.run(host='0.0.0.0', port=5000, debug=False)
    except KeyboardInterrupt:
        logger.info("👋 Shutting down...")

if __name__ == '__main__':
    main()
