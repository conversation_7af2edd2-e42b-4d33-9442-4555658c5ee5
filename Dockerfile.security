# Security-hardened Dockerfile for ByteGuardX
# Multi-stage build with security scanning and minimal attack surface

# Build stage
FROM python:3.9-slim as builder

# Install build dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Create non-root user for build
RUN useradd --create-home --shell /bin/bash builder
USER builder
WORKDIR /home/<USER>

# Copy requirements and install Python dependencies
COPY --chown=builder:builder requirements.txt .
RUN pip install --user --no-cache-dir -r requirements.txt

# Copy application code
COPY --chown=builder:builder . .

# Production stage
FROM python:3.9-slim as production

# Install runtime dependencies only
RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Create non-root user for application
RUN useradd --create-home --shell /bin/bash --uid 1000 byteguardx

# Set up application directory
WORKDIR /app

# Copy Python dependencies from builder
COPY --from=builder /home/<USER>/.local /home/<USER>/.local

# Copy application code
COPY --chown=byteguardx:byteguardx . .

# Create necessary directories
RUN mkdir -p /app/data /app/logs /app/temp \
    && chown -R byteguardx:byteguardx /app

# Set environment variables
ENV PATH=/home/<USER>/.local/bin:$PATH
ENV PYTHONPATH=/app
ENV FLASK_APP=run_server.py
ENV FLASK_ENV=production

# Security hardening
RUN chmod -R 755 /app \
    && chmod 644 /app/requirements.txt \
    && chmod 755 /app/run_server.py

# Switch to non-root user
USER byteguardx

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Expose port
EXPOSE 5000

# Run application
CMD ["python", "run_server.py"]

# Security labels
LABEL security.scan="enabled"
LABEL security.non-root="true"
LABEL security.minimal="true"
