#!/usr/bin/env python3
"""
ByteGuardX Quick Start Script
Minimal setup and launch for ByteGuardX
"""

import os
import sys
import subprocess
import time
import logging
import webbrowser
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def install_basic_dependencies():
    """Install basic dependencies"""
    logger.info("Installing basic dependencies...")
    
    basic_deps = [
        'flask>=2.3.0',
        'flask-cors>=4.0.0',
        'flask-socketio>=5.3.0',
        'requests>=2.31.0',
        'aiofiles>=23.2.1',
        'aiohttp>=3.9.1',
        'cryptography>=41.0.0',
        'pyyaml>=6.0.1',
        'jinja2>=3.1.2',
        'werkzeug>=2.3.0'
    ]
    
    try:
        subprocess.run([
            sys.executable, '-m', 'pip', 'install'
        ] + basic_deps, check=True)
        logger.info("✅ Basic dependencies installed")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Failed to install dependencies: {e}")
        return False

def create_required_directories():
    """Create required directories"""
    logger.info("Creating required directories...")
    
    directories = [
        'data', 'data/audit_logs', 'data/ml', 'data/plugins', 'data/secure',
        'logs', 'reports', 'reports/generated', 'models', 'models/trained'
    ]
    
    for dir_name in directories:
        Path(dir_name).mkdir(parents=True, exist_ok=True)
    
    logger.info("✅ Required directories created")

def start_byteguardx():
    """Start ByteGuardX application"""
    logger.info("🚀 Starting ByteGuardX...")
    
    try:
        # Set environment variables
        env = os.environ.copy()
        env['PYTHONPATH'] = str(Path.cwd())
        env['FLASK_ENV'] = 'development'
        env['BYTEGUARDX_PORT'] = '5000'
        
        # Start the application
        process = subprocess.Popen([
            sys.executable, 'run_byteguardx.py'
        ], env=env)
        
        # Wait a moment for startup
        time.sleep(3)
        
        # Open browser
        try:
            webbrowser.open('http://localhost:5000')
            logger.info("🌐 Browser opened to http://localhost:5000")
        except Exception as e:
            logger.warning(f"Could not open browser: {e}")
        
        print("\n" + "="*60)
        print("🎉 ByteGuardX Started Successfully!")
        print("="*60)
        print("🌐 Web Interface: http://localhost:5000")
        print("📊 Health Check:  http://localhost:5000/health")
        print("📚 API Docs:      http://localhost:5000/docs")
        print("="*60)
        print("📝 Press Ctrl+C to stop the application")
        print("="*60 + "\n")
        
        # Keep running
        try:
            process.wait()
        except KeyboardInterrupt:
            logger.info("👋 Shutting down...")
            process.terminate()
            process.wait()
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Failed to start ByteGuardX: {e}")
        return False

def main():
    """Main entry point"""
    print("🛡️ ByteGuardX Quick Start")
    print("="*40)
    
    # Check Python version
    if sys.version_info < (3, 8):
        logger.error("❌ Python 3.8+ required")
        sys.exit(1)
    
    logger.info(f"✅ Python {sys.version.split()[0]} detected")
    
    # Install dependencies
    if not install_basic_dependencies():
        sys.exit(1)
    
    # Create directories
    create_required_directories()
    
    # Start application
    if not start_byteguardx():
        sys.exit(1)

if __name__ == '__main__':
    main()
