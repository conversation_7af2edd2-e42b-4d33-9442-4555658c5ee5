# ByteGuardX Backend Dockerfile
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY byteguardx/ ./byteguardx/
COPY setup.py .
COPY README.md .

# Install the application
RUN pip install -e .

# Create non-root user
RUN useradd --create-home --shell /bin/bash byteguardx
RUN chown -R byteguardx:byteguardx /app
USER byteguardx

# Expose port
EXPOSE 5000

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Run the application
CMD ["python", "-m", "byteguardx.api.app"]
