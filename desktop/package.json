{"name": "byteguardx-desktop", "version": "1.0.0", "description": "ByteGuardX Desktop Application - AI-Powered Security Scanner", "main": "src/main.js", "author": "ByteGuardX Team", "license": "MIT", "private": true, "homepage": "./", "scripts": {"start": "electron .", "dev": "electron . --dev", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "postinstall": "electron-builder install-app-deps"}, "dependencies": {"electron-updater": "^6.1.7", "electron-store": "^8.1.0", "electron-log": "^5.0.1", "node-fetch": "^3.3.2", "ws": "^8.14.2", "chokidar": "^3.5.3", "mime-types": "^2.1.35", "semver": "^7.5.4"}, "devDependencies": {"electron": "^27.1.3", "electron-builder": "^24.8.1"}, "build": {"appId": "com.byteguardx.desktop", "productName": "ByteGuardX", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "../byteguardx", "to": "byteguardx", "filter": ["**/*", "!**/__pycache__", "!**/*.pyc"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}], "icon": "assets/icon.ico", "publisherName": "ByteGuardX Team", "verifyUpdateCodeSignature": false}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}], "icon": "assets/icon.icns", "category": "public.app-category.developer-tools", "hardenedRuntime": true, "entitlements": "assets/entitlements.mac.plist", "entitlementsInherit": "assets/entitlements.mac.plist"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icon.png", "category": "Development"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "ByteGuardX"}, "publish": {"provider": "github", "owner": "byteguardx", "repo": "byteguardx-desktop"}}}