const { app, BrowserWindow, Menu, ipcMain, dialog, shell, protocol } = require('electron');
const { autoUpdater } = require('electron-updater');
const Store = require('electron-store');
const log = require('electron-log');
const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');
const chokidar = require('chokidar');

// Configure logging
log.transports.file.level = 'info';
autoUpdater.logger = log;

// Initialize store
const store = new Store({
  defaults: {
    windowBounds: { width: 1400, height: 900 },
    apiEndpoint: 'http://localhost:5000',
    theme: 'dark',
    autoScan: true,
    notifications: true
  }
});

class ByteGuardXApp {
  constructor() {
    this.mainWindow = null;
    this.backendProcess = null;
    this.fileWatcher = null;
    this.isQuitting = false;
    
    this.setupApp();
  }

  setupApp() {
    // Handle app events
    app.whenReady().then(() => {
      this.createMainWindow();
      this.setupMenu();
      this.setupIPC();
      this.startBackend();
      this.setupAutoUpdater();
      
      app.on('activate', () => {
        if (BrowserWindow.getAllWindows().length === 0) {
          this.createMainWindow();
        }
      });
    });

    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        this.cleanup();
        app.quit();
      }
    });

    app.on('before-quit', () => {
      this.isQuitting = true;
      this.cleanup();
    });

    // Security: Prevent new window creation
    app.on('web-contents-created', (event, contents) => {
      contents.on('new-window', (event, navigationUrl) => {
        event.preventDefault();
        shell.openExternal(navigationUrl);
      });
    });
  }

  createMainWindow() {
    const bounds = store.get('windowBounds');
    
    this.mainWindow = new BrowserWindow({
      width: bounds.width,
      height: bounds.height,
      minWidth: 1200,
      minHeight: 800,
      icon: path.join(__dirname, '../assets/icon.png'),
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        enableRemoteModule: false,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true
      },
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'default',
      show: false
    });

    // Load the app
    if (process.env.NODE_ENV === 'development') {
      this.mainWindow.loadURL('http://localhost:3000');
      this.mainWindow.webContents.openDevTools();
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../build/index.html'));
    }

    // Window events
    this.mainWindow.once('ready-to-show', () => {
      this.mainWindow.show();
      
      // Check for updates
      if (!process.env.NODE_ENV === 'development') {
        autoUpdater.checkForUpdatesAndNotify();
      }
    });

    this.mainWindow.on('closed', () => {
      this.mainWindow = null;
    });

    this.mainWindow.on('resize', () => {
      store.set('windowBounds', this.mainWindow.getBounds());
    });

    // Handle external links
    this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
      shell.openExternal(url);
      return { action: 'deny' };
    });
  }

  setupMenu() {
    const template = [
      {
        label: 'File',
        submenu: [
          {
            label: 'Scan File...',
            accelerator: 'CmdOrCtrl+O',
            click: () => this.openFileDialog()
          },
          {
            label: 'Scan Folder...',
            accelerator: 'CmdOrCtrl+Shift+O',
            click: () => this.openFolderDialog()
          },
          { type: 'separator' },
          {
            label: 'Export Report...',
            accelerator: 'CmdOrCtrl+E',
            click: () => this.exportReport()
          },
          { type: 'separator' },
          {
            label: 'Preferences...',
            accelerator: 'CmdOrCtrl+,',
            click: () => this.openPreferences()
          },
          { type: 'separator' },
          {
            label: 'Quit',
            accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
            click: () => {
              this.isQuitting = true;
              app.quit();
            }
          }
        ]
      },
      {
        label: 'Scan',
        submenu: [
          {
            label: 'Start Scan',
            accelerator: 'CmdOrCtrl+R',
            click: () => this.startScan()
          },
          {
            label: 'Stop Scan',
            accelerator: 'CmdOrCtrl+.',
            click: () => this.stopScan()
          },
          { type: 'separator' },
          {
            label: 'Enable Auto-Scan',
            type: 'checkbox',
            checked: store.get('autoScan'),
            click: (item) => {
              store.set('autoScan', item.checked);
              this.toggleAutoScan(item.checked);
            }
          }
        ]
      },
      {
        label: 'View',
        submenu: [
          {
            label: 'Dashboard',
            accelerator: 'CmdOrCtrl+1',
            click: () => this.navigateTo('dashboard')
          },
          {
            label: 'Scanner',
            accelerator: 'CmdOrCtrl+2',
            click: () => this.navigateTo('scanner')
          },
          {
            label: 'Reports',
            accelerator: 'CmdOrCtrl+3',
            click: () => this.navigateTo('reports')
          },
          {
            label: 'Plugins',
            accelerator: 'CmdOrCtrl+4',
            click: () => this.navigateTo('plugins')
          },
          { type: 'separator' },
          {
            label: 'Reload',
            accelerator: 'CmdOrCtrl+R',
            click: () => this.mainWindow.reload()
          },
          {
            label: 'Force Reload',
            accelerator: 'CmdOrCtrl+Shift+R',
            click: () => this.mainWindow.webContents.reloadIgnoringCache()
          },
          {
            label: 'Toggle Developer Tools',
            accelerator: process.platform === 'darwin' ? 'Alt+Cmd+I' : 'Ctrl+Shift+I',
            click: () => this.mainWindow.webContents.toggleDevTools()
          }
        ]
      },
      {
        label: 'Window',
        submenu: [
          {
            label: 'Minimize',
            accelerator: 'CmdOrCtrl+M',
            click: () => this.mainWindow.minimize()
          },
          {
            label: 'Close',
            accelerator: 'CmdOrCtrl+W',
            click: () => this.mainWindow.close()
          }
        ]
      },
      {
        label: 'Help',
        submenu: [
          {
            label: 'About ByteGuardX',
            click: () => this.showAbout()
          },
          {
            label: 'Documentation',
            click: () => shell.openExternal('https://docs.byteguardx.com')
          },
          {
            label: 'Report Issue',
            click: () => shell.openExternal('https://github.com/byteguardx/issues')
          },
          { type: 'separator' },
          {
            label: 'Check for Updates',
            click: () => autoUpdater.checkForUpdatesAndNotify()
          }
        ]
      }
    ];

    // macOS specific menu adjustments
    if (process.platform === 'darwin') {
      template.unshift({
        label: app.getName(),
        submenu: [
          {
            label: 'About ' + app.getName(),
            click: () => this.showAbout()
          },
          { type: 'separator' },
          {
            label: 'Preferences...',
            accelerator: 'Cmd+,',
            click: () => this.openPreferences()
          },
          { type: 'separator' },
          {
            label: 'Services',
            submenu: []
          },
          { type: 'separator' },
          {
            label: 'Hide ' + app.getName(),
            accelerator: 'Cmd+H',
            click: () => app.hide()
          },
          {
            label: 'Hide Others',
            accelerator: 'Cmd+Alt+H',
            click: () => app.hideOthers()
          },
          {
            label: 'Show All',
            click: () => app.showAll()
          },
          { type: 'separator' },
          {
            label: 'Quit',
            accelerator: 'Cmd+Q',
            click: () => {
              this.isQuitting = true;
              app.quit();
            }
          }
        ]
      });
    }

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  setupIPC() {
    // File operations
    ipcMain.handle('select-file', async () => {
      const result = await dialog.showOpenDialog(this.mainWindow, {
        properties: ['openFile'],
        filters: [
          { name: 'Code Files', extensions: ['py', 'js', 'ts', 'java', 'go', 'rs', 'cpp', 'c', 'cs'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      });
      
      return result.canceled ? null : result.filePaths[0];
    });

    ipcMain.handle('select-folder', async () => {
      const result = await dialog.showOpenDialog(this.mainWindow, {
        properties: ['openDirectory']
      });
      
      return result.canceled ? null : result.filePaths[0];
    });

    ipcMain.handle('read-file', async (event, filePath) => {
      try {
        const content = fs.readFileSync(filePath, 'utf8');
        return { success: true, content };
      } catch (error) {
        return { success: false, error: error.message };
      }
    });

    // Settings
    ipcMain.handle('get-settings', () => {
      return store.store;
    });

    ipcMain.handle('set-setting', (event, key, value) => {
      store.set(key, value);
      return true;
    });

    // System info
    ipcMain.handle('get-system-info', () => {
      return {
        platform: process.platform,
        arch: process.arch,
        version: app.getVersion(),
        electronVersion: process.versions.electron,
        nodeVersion: process.versions.node
      };
    });

    // Notifications
    ipcMain.handle('show-notification', (event, title, body) => {
      if (store.get('notifications')) {
        new Notification(title, { body });
      }
    });
  }

  startBackend() {
    if (this.backendProcess) {
      return;
    }

    try {
      const pythonPath = process.platform === 'win32' ? 'python' : 'python3';
      const backendScript = path.join(__dirname, '../../run_byteguardx.py');
      
      this.backendProcess = spawn(pythonPath, [backendScript, '--no-browser'], {
        stdio: 'pipe',
        cwd: path.join(__dirname, '../..')
      });

      this.backendProcess.stdout.on('data', (data) => {
        log.info('Backend:', data.toString());
      });

      this.backendProcess.stderr.on('data', (data) => {
        log.error('Backend Error:', data.toString());
      });

      this.backendProcess.on('close', (code) => {
        log.info(`Backend process exited with code ${code}`);
        this.backendProcess = null;
      });

      log.info('Backend process started');
    } catch (error) {
      log.error('Failed to start backend:', error);
    }
  }

  setupAutoUpdater() {
    autoUpdater.on('checking-for-update', () => {
      log.info('Checking for update...');
    });

    autoUpdater.on('update-available', (info) => {
      log.info('Update available:', info);
    });

    autoUpdater.on('update-not-available', (info) => {
      log.info('Update not available:', info);
    });

    autoUpdater.on('error', (err) => {
      log.error('Error in auto-updater:', err);
    });

    autoUpdater.on('download-progress', (progressObj) => {
      let log_message = "Download speed: " + progressObj.bytesPerSecond;
      log_message = log_message + ' - Downloaded ' + progressObj.percent + '%';
      log_message = log_message + ' (' + progressObj.transferred + "/" + progressObj.total + ')';
      log.info(log_message);
    });

    autoUpdater.on('update-downloaded', (info) => {
      log.info('Update downloaded:', info);
      autoUpdater.quitAndInstall();
    });
  }

  toggleAutoScan(enabled) {
    if (enabled) {
      // Start file watcher
      const watchPath = store.get('watchPath') || process.cwd();
      this.fileWatcher = chokidar.watch(watchPath, {
        ignored: /(^|[\/\\])\../, // ignore dotfiles
        persistent: true
      });

      this.fileWatcher.on('change', (path) => {
        if (this.mainWindow) {
          this.mainWindow.webContents.send('file-changed', path);
        }
      });
    } else {
      // Stop file watcher
      if (this.fileWatcher) {
        this.fileWatcher.close();
        this.fileWatcher = null;
      }
    }
  }

  cleanup() {
    if (this.backendProcess) {
      this.backendProcess.kill();
      this.backendProcess = null;
    }

    if (this.fileWatcher) {
      this.fileWatcher.close();
      this.fileWatcher = null;
    }
  }

  // Menu action handlers
  openFileDialog() {
    if (this.mainWindow) {
      this.mainWindow.webContents.send('open-file-dialog');
    }
  }

  openFolderDialog() {
    if (this.mainWindow) {
      this.mainWindow.webContents.send('open-folder-dialog');
    }
  }

  exportReport() {
    if (this.mainWindow) {
      this.mainWindow.webContents.send('export-report');
    }
  }

  openPreferences() {
    if (this.mainWindow) {
      this.mainWindow.webContents.send('open-preferences');
    }
  }

  startScan() {
    if (this.mainWindow) {
      this.mainWindow.webContents.send('start-scan');
    }
  }

  stopScan() {
    if (this.mainWindow) {
      this.mainWindow.webContents.send('stop-scan');
    }
  }

  navigateTo(route) {
    if (this.mainWindow) {
      this.mainWindow.webContents.send('navigate-to', route);
    }
  }

  showAbout() {
    dialog.showMessageBox(this.mainWindow, {
      type: 'info',
      title: 'About ByteGuardX',
      message: 'ByteGuardX',
      detail: `Version: ${app.getVersion()}\nAI-Powered Security Scanner\n\n© 2024 ByteGuardX Team`,
      buttons: ['OK']
    });
  }
}

// Create app instance
new ByteGuardXApp();
