{"manifest_version": 3, "name": "ByteGuardX Security Scanner", "version": "1.0.0", "description": "AI-powered security vulnerability scanner for web applications and code repositories", "author": "ByteGuardX Team", "homepage_url": "https://byteguardx.com", "permissions": ["activeTab", "storage", "scripting", "tabs", "contextMenus", "notifications", "background"], "host_permissions": ["https://github.com/*", "https://gitlab.com/*", "https://bitbucket.org/*", "http://localhost:*", "https://*/api/*"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["https://github.com/*", "https://gitlab.com/*", "https://bitbucket.org/*"], "js": ["content.js"], "css": ["content.css"], "run_at": "document_end"}], "action": {"default_popup": "popup.html", "default_title": "ByteGuardX Security Scanner", "default_icon": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}}, "icons": {"16": "icons/icon16.png", "32": "icons/icon32.png", "48": "icons/icon48.png", "128": "icons/icon128.png"}, "options_page": "options.html", "web_accessible_resources": [{"resources": ["icons/*", "styles/*", "scripts/injected.js"], "matches": ["<all_urls>"]}], "content_security_policy": {"extension_pages": "script-src 'self'; object-src 'self'; style-src 'self' 'unsafe-inline';"}}