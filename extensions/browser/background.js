/**
 * ByteGuardX Browser Extension Background Script
 * Handles API communication, context menus, and background scanning
 */

class ByteGuardXBackground {
  constructor() {
    this.apiEndpoint = 'http://localhost:5000';
    this.scanQueue = new Map();
    this.isScanning = false;
    
    this.initialize();
  }

  initialize() {
    // Load settings
    this.loadSettings();
    
    // Setup context menus
    this.setupContextMenus();
    
    // Setup message listeners
    this.setupMessageListeners();
    
    // Setup alarm for periodic scans
    this.setupPeriodicScans();
    
    console.log('ByteGuardX Background Script initialized');
  }

  async loadSettings() {
    try {
      const settings = await chrome.storage.sync.get({
        apiEndpoint: 'http://localhost:5000',
        autoScan: true,
        scanInterval: 30,
        notificationsEnabled: true,
        minimumSeverity: 'medium'
      });
      
      this.apiEndpoint = settings.apiEndpoint;
      this.autoScan = settings.autoScan;
      this.scanInterval = settings.scanInterval;
      this.notificationsEnabled = settings.notificationsEnabled;
      this.minimumSeverity = settings.minimumSeverity;
      
    } catch (error) {
      console.error('Failed to load settings:', error);
    }
  }

  setupContextMenus() {
    chrome.contextMenus.removeAll(() => {
      // Main menu
      chrome.contextMenus.create({
        id: 'byteguardx-main',
        title: 'ByteGuardX Security Scanner',
        contexts: ['page', 'selection']
      });

      // Scan current page
      chrome.contextMenus.create({
        id: 'scan-page',
        parentId: 'byteguardx-main',
        title: 'Scan Current Page',
        contexts: ['page']
      });

      // Scan selected code
      chrome.contextMenus.create({
        id: 'scan-selection',
        parentId: 'byteguardx-main',
        title: 'Scan Selected Code',
        contexts: ['selection']
      });

      // Quick scan repository (for GitHub/GitLab)
      chrome.contextMenus.create({
        id: 'scan-repository',
        parentId: 'byteguardx-main',
        title: 'Scan Repository',
        contexts: ['page'],
        documentUrlPatterns: [
          'https://github.com/*',
          'https://gitlab.com/*',
          'https://bitbucket.org/*'
        ]
      });

      // View dashboard
      chrome.contextMenus.create({
        id: 'view-dashboard',
        parentId: 'byteguardx-main',
        title: 'View Security Dashboard',
        contexts: ['page']
      });
    });

    // Context menu click handler
    chrome.contextMenus.onClicked.addListener((info, tab) => {
      this.handleContextMenuClick(info, tab);
    });
  }

  setupMessageListeners() {
    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      this.handleMessage(message, sender, sendResponse);
      return true; // Keep message channel open for async response
    });
  }

  setupPeriodicScans() {
    // Clear existing alarms
    chrome.alarms.clear('periodicScan');
    
    if (this.autoScan) {
      // Create periodic scan alarm
      chrome.alarms.create('periodicScan', {
        delayInMinutes: this.scanInterval,
        periodInMinutes: this.scanInterval
      });
    }

    // Alarm listener
    chrome.alarms.onAlarm.addListener((alarm) => {
      if (alarm.name === 'periodicScan') {
        this.performPeriodicScan();
      }
    });
  }

  async handleContextMenuClick(info, tab) {
    try {
      switch (info.menuItemId) {
        case 'scan-page':
          await this.scanCurrentPage(tab);
          break;
          
        case 'scan-selection':
          await this.scanSelectedCode(info.selectionText, tab);
          break;
          
        case 'scan-repository':
          await this.scanRepository(tab);
          break;
          
        case 'view-dashboard':
          await this.openDashboard();
          break;
      }
    } catch (error) {
      console.error('Context menu action failed:', error);
      this.showNotification('Error', 'Failed to perform action: ' + error.message);
    }
  }

  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case 'SCAN_CODE':
          const result = await this.scanCode(message.code, message.language);
          sendResponse({ success: true, result });
          break;
          
        case 'GET_SCAN_STATUS':
          const status = await this.getScanStatus(message.scanId);
          sendResponse({ success: true, status });
          break;
          
        case 'GET_SETTINGS':
          await this.loadSettings();
          sendResponse({
            success: true,
            settings: {
              apiEndpoint: this.apiEndpoint,
              autoScan: this.autoScan,
              notificationsEnabled: this.notificationsEnabled
            }
          });
          break;
          
        case 'UPDATE_SETTINGS':
          await this.updateSettings(message.settings);
          sendResponse({ success: true });
          break;
          
        case 'HEALTH_CHECK':
          const health = await this.healthCheck();
          sendResponse({ success: true, health });
          break;
          
        default:
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Message handling failed:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  async scanCurrentPage(tab) {
    try {
      // Inject content script to extract page content
      const results = await chrome.scripting.executeScript({
        target: { tabId: tab.id },
        function: this.extractPageContent
      });

      if (results && results[0] && results[0].result) {
        const content = results[0].result;
        const scanResult = await this.scanCode(content.code, content.language);
        
        // Show results
        await this.showScanResults(scanResult, tab);
      }
    } catch (error) {
      console.error('Page scan failed:', error);
      this.showNotification('Scan Failed', 'Unable to scan current page');
    }
  }

  async scanSelectedCode(selectedText, tab) {
    if (!selectedText || selectedText.trim().length === 0) {
      this.showNotification('No Selection', 'Please select code to scan');
      return;
    }

    try {
      const language = await this.detectLanguage(selectedText, tab.url);
      const scanResult = await this.scanCode(selectedText, language);
      
      await this.showScanResults(scanResult, tab);
    } catch (error) {
      console.error('Selection scan failed:', error);
      this.showNotification('Scan Failed', 'Unable to scan selected code');
    }
  }

  async scanRepository(tab) {
    try {
      // Extract repository information
      const repoInfo = this.extractRepositoryInfo(tab.url);
      
      if (!repoInfo) {
        this.showNotification('Invalid Repository', 'Unable to identify repository');
        return;
      }

      // Show scanning notification
      this.showNotification('Repository Scan', 'Starting repository scan...');
      
      // This would integrate with GitHub API to fetch repository files
      // For now, we'll scan the current page
      await this.scanCurrentPage(tab);
      
    } catch (error) {
      console.error('Repository scan failed:', error);
      this.showNotification('Scan Failed', 'Unable to scan repository');
    }
  }

  async scanCode(code, language = 'unknown') {
    try {
      const response = await fetch(`${this.apiEndpoint}/api/v1/scan`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: code,
          language: language,
          file_path: 'browser-scan',
          scan_options: {
            minimum_severity: this.minimumSeverity
          }
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`);
      }

      const result = await response.json();
      
      // Store scan result
      await chrome.storage.local.set({
        [`scan_${result.scan_id}`]: {
          ...result,
          timestamp: Date.now()
        }
      });

      // Show notification if vulnerabilities found
      if (result.total_findings > 0 && this.notificationsEnabled) {
        this.showNotification(
          'Vulnerabilities Found',
          `Found ${result.total_findings} security issues (${result.critical_findings} critical)`
        );
      }

      return result;
      
    } catch (error) {
      console.error('Code scan failed:', error);
      throw error;
    }
  }

  async healthCheck() {
    try {
      const response = await fetch(`${this.apiEndpoint}/health`);
      return response.ok;
    } catch (error) {
      return false;
    }
  }

  async showScanResults(scanResult, tab) {
    // Send results to content script for display
    try {
      await chrome.tabs.sendMessage(tab.id, {
        type: 'SHOW_SCAN_RESULTS',
        results: scanResult
      });
    } catch (error) {
      // If content script not available, open popup
      chrome.action.openPopup();
    }
  }

  async openDashboard() {
    const dashboardUrl = `${this.apiEndpoint.replace(':5000', ':8080')}`;
    await chrome.tabs.create({ url: dashboardUrl });
  }

  async updateSettings(newSettings) {
    await chrome.storage.sync.set(newSettings);
    await this.loadSettings();
    
    // Restart periodic scans if needed
    this.setupPeriodicScans();
  }

  showNotification(title, message) {
    if (this.notificationsEnabled) {
      chrome.notifications.create({
        type: 'basic',
        iconUrl: 'icons/icon48.png',
        title: title,
        message: message
      });
    }
  }

  extractRepositoryInfo(url) {
    const patterns = {
      github: /github\.com\/([^\/]+)\/([^\/]+)/,
      gitlab: /gitlab\.com\/([^\/]+)\/([^\/]+)/,
      bitbucket: /bitbucket\.org\/([^\/]+)\/([^\/]+)/
    };

    for (const [platform, pattern] of Object.entries(patterns)) {
      const match = url.match(pattern);
      if (match) {
        return {
          platform,
          owner: match[1],
          repo: match[2]
        };
      }
    }

    return null;
  }

  detectLanguage(code, url) {
    // Simple language detection based on URL and code patterns
    if (url.includes('.py') || code.includes('def ') || code.includes('import ')) {
      return 'python';
    } else if (url.includes('.js') || code.includes('function ') || code.includes('const ')) {
      return 'javascript';
    } else if (url.includes('.java') || code.includes('public class ')) {
      return 'java';
    } else if (url.includes('.go') || code.includes('func ') || code.includes('package ')) {
      return 'go';
    }
    
    return 'unknown';
  }

  // Function to be injected into page
  extractPageContent() {
    const codeElements = document.querySelectorAll('pre, code, .highlight, .blob-code');
    let extractedCode = '';
    let language = 'unknown';

    // Extract code from various elements
    codeElements.forEach(element => {
      if (element.textContent && element.textContent.trim().length > 0) {
        extractedCode += element.textContent + '\n';
      }
    });

    // Try to detect language from page context
    const languageIndicators = document.querySelectorAll('[data-language], .language-*, .lang-*');
    if (languageIndicators.length > 0) {
      const firstIndicator = languageIndicators[0];
      language = firstIndicator.dataset.language || 
                firstIndicator.className.match(/language-(\w+)/)?.[1] ||
                firstIndicator.className.match(/lang-(\w+)/)?.[1] ||
                'unknown';
    }

    return {
      code: extractedCode || document.body.textContent.substring(0, 10000), // Fallback to page text
      language: language,
      url: window.location.href
    };
  }

  async performPeriodicScan() {
    if (this.isScanning) {
      return; // Already scanning
    }

    try {
      this.isScanning = true;
      
      // Get active tab
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tab = tabs[0];
        
        // Only scan code-related pages
        if (this.isCodePage(tab.url)) {
          await this.scanCurrentPage(tab);
        }
      }
    } catch (error) {
      console.error('Periodic scan failed:', error);
    } finally {
      this.isScanning = false;
    }
  }

  isCodePage(url) {
    const codePatterns = [
      /github\.com.*\/blob\//,
      /gitlab\.com.*\/blob\//,
      /bitbucket\.org.*\/src\//,
      /\.py$/, /\.js$/, /\.ts$/, /\.java$/, /\.go$/, /\.rs$/
    ];

    return codePatterns.some(pattern => pattern.test(url));
  }
}

// Initialize background script
new ByteGuardXBackground();
