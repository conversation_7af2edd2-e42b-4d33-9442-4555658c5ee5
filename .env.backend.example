# ByteGuardX Backend Environment Configuration
# Copy this file to .env and update the values for your environment

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================

# Application Environment (development, staging, production)
ENVIRONMENT=development

# Debug mode (true/false) - NEVER enable in production
DEBUG=false

# Application secret key - MUST be changed in production
SECRET_KEY=your-super-secret-key-change-this-in-production

# Application host and port
HOST=0.0.0.0
PORT=5000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================

# Database URL - supports SQLite, PostgreSQL, MySQL
# SQLite (default for development)
DATABASE_URL=sqlite:///data/byteguardx.db

# PostgreSQL (recommended for production)
# DATABASE_URL=postgresql://username:password@localhost:5432/byteguardx

# Database connection pool settings
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================

# JWT Configuration
JWT_SECRET_KEY=your-jwt-secret-key-change-this
JWT_ACCESS_TOKEN_EXPIRES=3600  # 1 hour in seconds
JWT_REFRESH_TOKEN_EXPIRES=2592000  # 30 days in seconds

# Master encryption key for data at rest (AES-256)
BYTEGUARDX_MASTER_KEY=your-base64-encoded-master-key-32-bytes

# Two-Factor Authentication
ENABLE_2FA=true
TOTP_ISSUER_NAME=ByteGuardX

# Rate Limiting
ENABLE_RATE_LIMITING=true
AUTH_RATE_LIMIT=5
AUTH_RATE_WINDOW=300
API_RATE_LIMIT=100
SCAN_RATE_LIMIT=10

# Audit Logging
ENABLE_AUDIT_LOGGING=true
AUDIT_LOG_DIRECTORY=data/audit_logs
ENABLE_LOG_REDACTION=true

# =============================================================================
# FILE PROCESSING SECURITY
# =============================================================================

# File upload limits
MAX_FILE_SIZE=104857600        # 100MB
MAX_ARCHIVE_SIZE=524288000     # 500MB
MAX_FILES_IN_ARCHIVE=1000
SCAN_TIMEOUT=300  # 5 minutes

# Allowed MIME types (comma-separated)
ALLOWED_MIME_TYPES=text/plain,text/x-python,text/javascript,application/json,application/zip

# Blocked file extensions (comma-separated)
BLOCKED_EXTENSIONS=.exe,.bat,.cmd,.com,.scr,.pif,.vbs,.jar

# =============================================================================
# AI/ML CONFIGURATION
# =============================================================================

# AI/ML API Keys
OPENAI_API_KEY=your-openai-api-key-here
HUGGINGFACE_API_KEY=your-huggingface-api-key-here

# ML settings
ENABLE_AI_SCANNING=true
ML_CONFIDENCE_THRESHOLD=0.5
ENABLE_ML_FALLBACK=true
ML_MODEL_TIMEOUT=30

# =============================================================================
# EXTERNAL INTEGRATIONS
# =============================================================================

# GitHub integration
GITHUB_TOKEN=your-github-token-here
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Redis (optional, for caching and sessions)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_SSL=false

# Email configuration
SMTP_HOST=
SMTP_PORT=587
SMTP_USERNAME=
SMTP_PASSWORD=
SMTP_USE_TLS=true
EMAIL_FROM=<EMAIL>

# Scheduled Scan Configuration
SCHEDULER_ENABLED=true
SCHEDULER_DB_PATH=data/jobs.db
SCHEDULER_WORKER_COUNT=4

# Plugin Configuration
PLUGIN_INSTALL_DIR=plugins/installed
PLUGIN_CACHE_DIR=plugins/cache
PLUGIN_MARKETPLACE_URL=https://api.byteguardx.com/plugins

# Deployment Configuration
DEPLOYMENT_MODE=production
DEPLOYMENT_WIZARD_ENABLED=false

# =============================================================================
# CORS AND FRONTEND
# =============================================================================

# Allowed origins for CORS
ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Frontend URL
FRONTEND_URL=http://localhost:3000

# =============================================================================
# MONITORING AND HEALTH
# =============================================================================

# Health monitoring
ENABLE_HEALTH_MONITORING=true
HEALTH_CHECK_INTERVAL=60
HEALTH_RETENTION_HOURS=24

# System resource thresholds
CPU_WARNING_THRESHOLD=70.0
CPU_CRITICAL_THRESHOLD=90.0
MEMORY_WARNING_THRESHOLD=80.0
MEMORY_CRITICAL_THRESHOLD=95.0
DISK_WARNING_THRESHOLD=85.0
DISK_CRITICAL_THRESHOLD=95.0

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================

# Log settings
LOG_LEVEL=INFO
LOG_FILE=data/logs/byteguardx.log
LOG_MAX_SIZE=10485760    # 10MB
LOG_BACKUP_COUNT=5

# Structured logging
ENABLE_STRUCTURED_LOGGING=true
LOG_OUTPUT_FORMAT=json   # json, text

# =============================================================================
# PLUGIN SYSTEM
# =============================================================================

# Plugin settings
ENABLE_PLUGINS=true
PLUGINS_DIRECTORY=data/plugins
PLUGIN_REGISTRY_FILE=data/plugins/registry.json
PLUGIN_VALIDATION_STRICT=true

# =============================================================================
# BACKUP AND RECOVERY
# =============================================================================

# Backup configuration
ENABLE_AUTOMATED_BACKUPS=true
BACKUP_DIRECTORY=data/backups
BACKUP_RETENTION_DAYS=30
BACKUP_ENCRYPTION_KEY=your-backup-encryption-key

# =============================================================================
# COMPLIANCE SETTINGS
# =============================================================================

# Compliance features
ENABLE_GDPR_COMPLIANCE=true
ENABLE_CCPA_COMPLIANCE=true
DATA_RETENTION_DAYS=2555  # 7 years default

# =============================================================================
# PERFORMANCE SETTINGS
# =============================================================================

# Caching
ENABLE_CACHING=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# Background tasks
ENABLE_BACKGROUND_TASKS=true
TASK_QUEUE_SIZE=100
TASK_WORKER_COUNT=4

# =============================================================================
# SECURITY HEADERS
# =============================================================================

# Content Security Policy
CSP_DEFAULT_SRC='self'
CSP_SCRIPT_SRC='self' 'unsafe-inline' 'unsafe-eval'
CSP_STYLE_SRC='self' 'unsafe-inline'

# Security headers
ENABLE_HSTS=true
HSTS_MAX_AGE=31536000
ENABLE_CONTENT_TYPE_OPTIONS=true
ENABLE_FRAME_OPTIONS=true
FRAME_OPTIONS=DENY

# =============================================================================
# FEATURE FLAGS
# =============================================================================

# Feature toggles
ENABLE_ADVANCED_SCANNING=true
ENABLE_REAL_TIME_SCANNING=true
ENABLE_BATCH_PROCESSING=true
ENABLE_API_VERSIONING=true
ENABLE_METRICS_COLLECTION=true

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================

# Development only (do not use in production)
ENABLE_DEBUG_TOOLBAR=false
ENABLE_PROFILING=false
ENABLE_MOCK_SERVICES=false

# Testing
TEST_DATABASE_URL=sqlite:///data/test_byteguardx.db
ENABLE_TEST_MODE=false

# =============================================================================
# DEPLOYMENT CONFIGURATION
# =============================================================================

# Deployment info
DEPLOYMENT_ENVIRONMENT=development
DEPLOYMENT_VERSION=1.0.0
DEPLOYMENT_BUILD_ID=

# Container settings
CONTAINER_MEMORY_LIMIT=2g
CONTAINER_CPU_LIMIT=1.0

# =============================================================================
# NOTES AND SECURITY REMINDERS
# =============================================================================

# IMPORTANT SECURITY NOTES:
# 1. Change ALL default keys and secrets before production deployment
# 2. Use strong, unique passwords and encryption keys
# 3. Enable HTTPS/TLS in production environments
# 4. Regularly rotate secrets and encryption keys
# 5. Monitor logs for security events and anomalies
# 6. Keep dependencies updated and scan for vulnerabilities
# 7. Implement proper backup and disaster recovery procedures
# 8. Conduct regular security audits and penetration testing
# 9. Follow principle of least privilege for all access controls
# 10. Ensure proper network security and firewall configuration
