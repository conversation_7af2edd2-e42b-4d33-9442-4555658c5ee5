version: '3.8'

services:
  # ByteGuardX Backend API
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "5000:5000"
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=${SECRET_KEY:-change-me-in-production}
      - JWT_SECRET_KEY=${JWT_SECRET_KEY:-change-me-in-production}
      - ALLOWED_ORIGINS=http://localhost:3000,https://byteguardx.com
    volumes:
      - ./byteguardx:/app/byteguardx:ro
      - ./byteguardx/offline_db:/app/byteguardx/offline_db:ro
      - uploads:/tmp/byteguardx_uploads
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # ByteGuardX Frontend
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=http://localhost:5000
    depends_on:
      - backend
    restart: unless-stopped

  # Nginx Reverse Proxy (Optional)
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    depends_on:
      - backend
      - frontend
    restart: unless-stopped
    profiles:
      - production

volumes:
  uploads:
    driver: local

networks:
  default:
    name: byteguardx-network
