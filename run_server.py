#!/usr/bin/env python3
"""
ByteGuardX Development Server Startup Script
Runs the security-enhanced Flask application with proper configuration
"""

import os
import sys
import logging
import atexit
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Import security modules
try:
    from byteguardx.security.config_validator import validate_startup_security
    from byteguardx.security.secrets_manager import secrets_manager
    from byteguardx.security.refresh_token_manager import refresh_token_manager
    from byteguardx.database.schema_validator import schema_validator
    SECURITY_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️  Security modules not available: {e}")
    SECURITY_MODULES_AVAILABLE = False

# Set environment variables for development
os.environ.setdefault('FLASK_ENV', 'development')
os.environ.setdefault('FLASK_DEBUG', '1')
os.environ.setdefault('JWT_SECRET_KEY', 'dev-secret-key-change-in-production')
os.environ.setdefault('ALLOWED_ORIGINS', 'http://localhost:3000,http://127.0.0.1:3000')
os.environ.setdefault('ENABLE_2FA', 'False')  # Disable 2FA for development

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def cleanup_on_exit():
    """Cleanup function called on server shutdown"""
    if SECURITY_MODULES_AVAILABLE:
        try:
            # Cleanup development keys
            secrets_manager.cleanup_development_keys()
            print("🧹 Security cleanup completed")
        except Exception as e:
            print(f"⚠️  Cleanup warning: {e}")

def main():
    """Start the ByteGuardX development server with security validations"""
    try:
        print("🚀 Starting ByteGuardX Development Server...")
        print("=" * 50)

        # Perform security validations if modules are available
        if SECURITY_MODULES_AVAILABLE:
            print("🔐 Performing security validations...")

            # Validate startup security configuration
            try:
                validate_startup_security()
                print("✅ Security configuration validated")
            except Exception as e:
                print(f"⚠️  Security validation warning: {e}")

            # Setup cleanup handlers
            atexit.register(cleanup_on_exit)

            # Clean up expired tokens
            try:
                refresh_token_manager.cleanup_expired_tokens()
                print("✅ Token cleanup completed")
            except Exception as e:
                print(f"⚠️  Token cleanup warning: {e}")

            # Validate database schema
            try:
                is_valid, issues = schema_validator.validate_schema_on_startup()
                if is_valid:
                    print("✅ Database schema validation passed")
                else:
                    print("⚠️  Database schema issues detected:")
                    for issue in issues:
                        print(f"    - {issue}")

                    # Offer to apply migrations
                    if any("Pending migrations" in issue for issue in issues):
                        print("🔧 Applying pending migrations...")
                        success, results = schema_validator.apply_pending_migrations()
                        if success:
                            for result in results:
                                print(f"    ✅ {result}")
                        else:
                            for result in results:
                                print(f"    ❌ {result}")
            except Exception as e:
                print(f"⚠️  Schema validation warning: {e}")
        else:
            print("⚠️  Running without enhanced security modules")

        # Check if we can import the app
        try:
            from byteguardx.api.security_enhanced_app import create_security_enhanced_app
        except ImportError as e:
            print(f"❌ Import Error: {e}")
            print("Trying to use the basic Flask app instead...")

            # Fallback to basic app
            from flask import Flask, jsonify

            def create_basic_app():
                app = Flask(__name__)

                @app.route('/health')
                def health():
                    return jsonify({'status': 'ok', 'message': 'ByteGuardX API is running'})

                @app.route('/api/auth/login', methods=['POST'])
                def login():
                    return jsonify({'error': 'Authentication system not fully configured'}), 501

                return app

            create_security_enhanced_app = create_basic_app
        
        # Create app with development config
        app = create_security_enhanced_app({
            'DEBUG': True,
            'TESTING': False,
            'SECRET_KEY': os.environ.get('JWT_SECRET_KEY'),
            'ENABLE_2FA': os.environ.get('ENABLE_2FA', 'False').lower() == 'true',
            'DATABASE_URL': 'sqlite:///byteguardx_dev.db',
            'CORS_ORIGINS': os.environ.get('ALLOWED_ORIGINS', '').split(',')
        })
        
        print("✅ Flask app created successfully")
        print("🔐 Security features enabled:")
        print("   - JWT Authentication")
        print("   - Rate Limiting")
        print("   - Audit Logging")
        print("   - CORS Protection")
        print("   - Input Validation")
        print("   - Secure Cookies")
        
        if os.environ.get('ENABLE_2FA', 'False').lower() == 'true':
            print("   - Two-Factor Authentication")
        
        print("\n📡 Server Configuration:")
        print(f"   - Host: 0.0.0.0")
        print(f"   - Port: 5000")
        print(f"   - Debug: {app.config.get('DEBUG', False)}")
        print(f"   - Environment: {os.environ.get('FLASK_ENV', 'development')}")
        
        print("\n🌐 Available Endpoints:")
        print("   Authentication:")
        print("     POST /api/auth/register  - User registration")
        print("     POST /api/auth/login     - User login")
        print("     POST /api/auth/logout    - User logout")
        print("     POST /api/auth/refresh   - Token refresh")
        print("     GET  /api/auth/verify    - Token verification")
        print("   Health:")
        print("     GET  /health             - Health check")
        
        print("\n🧪 Test the API:")
        print("   python test_auth.py")
        
        print("\n" + "=" * 50)
        print("🎯 Server starting on http://localhost:5000")
        print("Press Ctrl+C to stop the server")
        print("=" * 50)
        
        # Start the development server
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=True,
            use_reloader=True,
            threaded=True
        )
        
    except ImportError as e:
        print(f"❌ Import Error: {e}")
        print("Make sure all dependencies are installed:")
        print("pip install -r requirements.txt")
        sys.exit(1)
        
    except Exception as e:
        print(f"❌ Failed to start server: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
