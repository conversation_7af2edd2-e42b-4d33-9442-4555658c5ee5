{"name": "byteguardx-desktop", "version": "1.0.0", "description": "ByteGuardX Desktop - AI-Powered Security Scanner", "main": "src/main.js", "homepage": "./", "scripts": {"start": "electron .", "dev": "concurrently \"npm run start-backend\" \"npm run start-frontend\" \"wait-on http://localhost:3000 && electron .\"", "start-backend": "cd .. && python run.py", "start-frontend": "cd .. && npm run dev", "build": "npm run build:frontend && npm run build:electron", "build:frontend": "cd .. && npm run build", "build:electron": "electron-builder", "build:linux": "electron-builder --linux", "build:win32": "electron-builder --win", "build:darwin": "electron-builder --mac", "dist": "npm run build && electron-builder --publish=never", "pack": "electron-builder --dir", "postinstall": "electron-builder install-app-deps", "test": "jest", "lint": "eslint src/ --ext .js"}, "build": {"appId": "com.byteguardx.desktop", "productName": "ByteGuardX", "directories": {"output": "dist"}, "files": ["src/**/*", "assets/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "../dist", "to": "app", "filter": ["**/*"]}], "linux": {"target": [{"target": "AppImage", "arch": ["x64", "arm64"]}, {"target": "deb", "arch": ["x64", "arm64"]}, {"target": "rpm", "arch": ["x64", "arm64"]}], "category": "Development", "icon": "assets/icon.png"}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icon.ico"}, "mac": {"target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "category": "public.app-category.developer-tools", "icon": "assets/icon.icns"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "keywords": ["security", "vulnerability-scanner", "electron", "desktop", "byteguardx"], "author": "ByteGuardX Team", "license": "MIT", "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4", "concurrently": "^8.2.2", "wait-on": "^7.2.0", "jest": "^29.7.0", "eslint": "^8.52.0"}, "dependencies": {"electron-updater": "^6.1.4", "electron-store": "^8.1.0", "axios": "^1.5.0", "express": "^4.18.2", "cors": "^2.8.5"}}