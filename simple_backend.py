#!/usr/bin/env python3
"""
Simple ByteGuardX Backend Server
Provides basic API endpoints for the frontend
"""

from flask import Flask, jsonify, request, render_template_string
from flask_cors import CORS
import time
import re
from datetime import datetime

app = Flask(__name__)
CORS(app)

# Simple vulnerability patterns
VULNERABILITY_PATTERNS = {
    'sql_injection': [
        r'SELECT\s+.*\s+FROM\s+.*\s+WHERE\s+.*\+',
        r'INSERT\s+INTO\s+.*\s+VALUES\s*\(.*\+',
        r'UPDATE\s+.*\s+SET\s+.*\s+WHERE\s+.*\+',
        r'DELETE\s+FROM\s+.*\s+WHERE\s+.*\+',
        r'query\s*=\s*["\'].*["\']\s*\+',
        r'execute\s*\(\s*["\'].*["\']\s*\+',
    ],
    'xss': [
        r'innerHTML\s*=\s*.*\+',
        r'document\.write\s*\(\s*.*\+',
        r'eval\s*\(\s*.*\+',
        r'setTimeout\s*\(\s*["\'].*["\']\s*\+',
        r'setInterval\s*\(\s*["\'].*["\']\s*\+',
    ],
    'command_injection': [
        r'exec\s*\(\s*.*\+',
        r'system\s*\(\s*.*\+',
        r'shell_exec\s*\(\s*.*\+',
        r'passthru\s*\(\s*.*\+',
        r'os\.system\s*\(\s*.*\+',
        r'subprocess\.\w+\s*\(\s*.*\+',
    ],
    'hardcoded_secrets': [
        r'password\s*=\s*["\'][^"\']{8,}["\']',
        r'api_key\s*=\s*["\'][^"\']{20,}["\']',
        r'secret\s*=\s*["\'][^"\']{16,}["\']',
        r'token\s*=\s*["\'][^"\']{20,}["\']',
        r'key\s*=\s*["\'][^"\']{16,}["\']',
    ],
    'path_traversal': [
        r'\.\./',
        r'\.\.\\',
        r'%2e%2e%2f',
        r'%2e%2e\\',
    ]
}

def scan_code(content, file_path="unknown"):
    """Simple pattern-based vulnerability scanning"""
    findings = []
    lines = content.split('\n')
    
    for line_num, line in enumerate(lines, 1):
        for vuln_type, patterns in VULNERABILITY_PATTERNS.items():
            for pattern in patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    severity = 'high' if vuln_type in ['sql_injection', 'command_injection'] else 'medium'
                    
                    findings.append({
                        'id': f"{vuln_type}_{line_num}",
                        'type': vuln_type.replace('_', ' ').title(),
                        'severity': severity,
                        'title': f"{vuln_type.replace('_', ' ').title()} Vulnerability",
                        'description': f"Potential {vuln_type.replace('_', ' ')} vulnerability detected",
                        'file_path': file_path,
                        'line_start': line_num,
                        'line_end': line_num,
                        'evidence': line.strip(),
                        'confidence': 0.8,
                        'remediation': get_remediation(vuln_type)
                    })
    
    return findings

def get_remediation(vuln_type):
    """Get remediation advice for vulnerability type"""
    remediation_map = {
        'sql_injection': 'Use parameterized queries or prepared statements',
        'xss': 'Sanitize user input and use proper output encoding',
        'command_injection': 'Avoid executing user input as system commands',
        'hardcoded_secrets': 'Store secrets in environment variables or secure vaults',
        'path_traversal': 'Validate and sanitize file paths'
    }
    return remediation_map.get(vuln_type, 'Review and fix the identified issue')

@app.route('/')
def index():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'ByteGuardX Backend',
        'version': '1.0.0',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/health')
def health():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'ByteGuardX Backend',
        'version': '1.0.0',
        'timestamp': datetime.now().isoformat()
    })

@app.route('/api/scan', methods=['POST'])
def scan_endpoint():
    """Scan endpoint for code analysis"""
    try:
        data = request.get_json()
        
        if not data:
            return jsonify({
                'success': False,
                'error': 'No data provided'
            }), 400
        
        content = data.get('content', '')
        file_path = data.get('file_path', 'unknown')
        
        if not content:
            return jsonify({
                'success': False,
                'error': 'No content provided'
            }), 400
        
        # Simulate processing time
        time.sleep(1)
        
        findings = scan_code(content, file_path)
        
        return jsonify({
            'success': True,
            'findings': findings,
            'scan_id': f"scan_{int(time.time())}",
            'timestamp': datetime.now().isoformat(),
            'stats': {
                'total_findings': len(findings),
                'high_severity': len([f for f in findings if f['severity'] == 'high']),
                'medium_severity': len([f for f in findings if f['severity'] == 'medium']),
                'low_severity': len([f for f in findings if f['severity'] == 'low'])
            }
        })
    
    except Exception as e:
        print(f"Scan error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/upload', methods=['POST'])
def upload_endpoint():
    """File upload endpoint"""
    try:
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file provided'
            }), 400
        
        file = request.files['file']
        
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400
        
        # Read file content
        content = file.read().decode('utf-8', errors='ignore')
        
        # Scan the content
        findings = scan_code(content, file.filename)
        
        return jsonify({
            'success': True,
            'filename': file.filename,
            'findings': findings,
            'scan_id': f"scan_{int(time.time())}",
            'timestamp': datetime.now().isoformat()
        })
    
    except Exception as e:
        print(f"Upload error: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/stats')
def stats_endpoint():
    """Get system statistics"""
    return jsonify({
        'success': True,
        'stats': {
            'total_scans': 42,
            'vulnerabilities_found': 156,
            'files_processed': 89,
            'uptime': '2 days, 14 hours',
            'last_scan': datetime.now().isoformat()
        }
    })

if __name__ == '__main__':
    print("🛡️ ByteGuardX Backend Server")
    print("=" * 50)
    print("🌐 Server: http://localhost:5000")
    print("📊 Health: http://localhost:5000/health")
    print("🔍 API:    http://localhost:5000/api/scan")
    print("=" * 50)
    print("📝 Press Ctrl+C to stop the server")
    print("=" * 50 + "\n")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
