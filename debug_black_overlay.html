<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ByteGuardX Black Overlay Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
            color: #333;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .code {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            border: 1px solid #e9ecef;
        }
    </style>
</head>
<body>
    <h1>🛡️ ByteGuardX Black Overlay Debug Tool</h1>
    
    <div class="debug-section">
        <h2>🔍 Current Status</h2>
        <div id="status-container">
            <div class="info">Checking ByteGuardX application status...</div>
        </div>
        <button onclick="checkStatus()">🔄 Refresh Status</button>
        <button onclick="openByteguardx()">🌐 Open ByteGuardX</button>
    </div>

    <div class="debug-section">
        <h2>🛠️ Quick Fixes</h2>
        <p>If you're seeing a black overlay, try these solutions:</p>
        
        <h3>1. Browser Cache Issues</h3>
        <button onclick="clearCacheInstructions()">📋 Show Cache Clear Instructions</button>
        <div id="cache-instructions" style="display: none;" class="code">
            <strong>Clear Browser Cache:</strong><br>
            • Chrome/Edge: Ctrl+Shift+R (hard refresh)<br>
            • Firefox: Ctrl+F5<br>
            • Safari: Cmd+Shift+R<br>
            <br>
            <strong>Or clear all cache:</strong><br>
            • Chrome: Settings → Privacy → Clear browsing data<br>
            • Firefox: Settings → Privacy → Clear Data<br>
        </div>

        <h3>2. JavaScript Console Errors</h3>
        <button onclick="showConsoleInstructions()">📋 Check Console Errors</button>
        <div id="console-instructions" style="display: none;" class="code">
            <strong>Check Browser Console:</strong><br>
            1. Press F12 to open Developer Tools<br>
            2. Click on "Console" tab<br>
            3. Look for red error messages<br>
            4. Take a screenshot and share any errors you see<br>
        </div>

        <h3>3. Modal/Overlay Issues</h3>
        <button onclick="showModalFix()">📋 Fix Stuck Modals</button>
        <div id="modal-fix" style="display: none;" class="code">
            <strong>Fix Stuck Modals/Overlays:</strong><br>
            1. Press Escape key multiple times<br>
            2. Click outside any visible content<br>
            3. Try refreshing the page (Ctrl+R)<br>
            4. If still stuck, try incognito mode<br>
        </div>

        <h3>4. CSS Theme Issues</h3>
        <button onclick="showThemeFix()">📋 Fix Theme Issues</button>
        <div id="theme-fix" style="display: none;" class="code">
            <strong>Theme Override Issues:</strong><br>
            The ForceBlackTheme component has been temporarily disabled.<br>
            If you still see issues:<br>
            1. Clear browser cache completely<br>
            2. Try incognito/private browsing mode<br>
            3. Check if any browser extensions are interfering<br>
        </div>
    </div>

    <div class="debug-section">
        <h2>🔧 Technical Information</h2>
        <div id="tech-info">
            <p><strong>Frontend URL:</strong> <a href="http://localhost:3000" target="_blank">http://localhost:3000</a></p>
            <p><strong>Backend URL:</strong> <a href="http://localhost:5000/api/health" target="_blank">http://localhost:5000/api/health</a></p>
            <p><strong>Expected Behavior:</strong> Black background with white text and cyan accents</p>
        </div>
    </div>

    <div class="debug-section">
        <h2>📞 Need Help?</h2>
        <p>If the black overlay persists:</p>
        <ol>
            <li>Take a screenshot of what you're seeing</li>
            <li>Check browser console for errors (F12 → Console)</li>
            <li>Try the quick fixes above</li>
            <li>Report the specific browser and version you're using</li>
        </ol>
    </div>

    <script>
        function checkStatus() {
            const container = document.getElementById('status-container');
            container.innerHTML = '<div class="info">Checking servers...</div>';
            
            // Check frontend
            fetch('http://localhost:3000')
                .then(response => {
                    if (response.ok) {
                        container.innerHTML += '<div class="success">✅ Frontend server is running (http://localhost:3000)</div>';
                    } else {
                        container.innerHTML += '<div class="error">❌ Frontend server returned error</div>';
                    }
                })
                .catch(() => {
                    container.innerHTML += '<div class="error">❌ Frontend server is not accessible</div>';
                });

            // Check backend
            fetch('http://localhost:5000/api/health')
                .then(response => response.json())
                .then(data => {
                    container.innerHTML += `<div class="success">✅ Backend server is running (${data.service || 'Unknown'})</div>`;
                })
                .catch(() => {
                    container.innerHTML += '<div class="error">❌ Backend server is not accessible</div>';
                });
        }

        function openByteguardx() {
            window.open('http://localhost:3000', '_blank');
        }

        function clearCacheInstructions() {
            const div = document.getElementById('cache-instructions');
            div.style.display = div.style.display === 'none' ? 'block' : 'none';
        }

        function showConsoleInstructions() {
            const div = document.getElementById('console-instructions');
            div.style.display = div.style.display === 'none' ? 'block' : 'none';
        }

        function showModalFix() {
            const div = document.getElementById('modal-fix');
            div.style.display = div.style.display === 'none' ? 'block' : 'none';
        }

        function showThemeFix() {
            const div = document.getElementById('theme-fix');
            div.style.display = div.style.display === 'none' ? 'block' : 'none';
        }

        // Auto-check status on load
        window.onload = function() {
            setTimeout(checkStatus, 1000);
        };
    </script>
</body>
</html>
