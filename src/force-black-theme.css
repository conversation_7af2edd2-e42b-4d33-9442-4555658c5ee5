/* FORCE BLACK THEME - NUCLEAR OPTION */
/* This file overrides EVERYTHING to ensure pure black background */

/* Universal selector with maximum specificity */
html, html *, html *::before, html *::after {
  background-color: #000000 !important;
  background-image: none !important;
  background: #000000 !important;
  color: #ffffff !important;
}

/* Target specific elements that might have backgrounds */
body, #root, .app, .main, .container, .wrapper, .content {
  background-color: #000000 !important;
  background-image: none !important;
  background: #000000 !important;
}

/* Override all possible background classes */
[class*="bg-"], [class*="background"] {
  background-color: #000000 !important;
  background-image: none !important;
  background: #000000 !important;
}

/* Override inline styles */
[style*="background"] {
  background-color: #000000 !important;
  background-image: none !important;
  background: #000000 !important;
}

/* Force all divs and containers */
div, section, main, article, aside, header, footer, nav, 
span, p, h1, h2, h3, h4, h5, h6, ul, li, ol, dl, dt, dd,
table, thead, tbody, tr, td, th, form, fieldset, legend,
button, input, textarea, select, option {
  background-color: #000000 !important;
  background-image: none !important;
  background: #000000 !important;
  color: #ffffff !important;
}

/* Override any gradient backgrounds */
.bg-gradient-to-r, .bg-gradient-to-l, .bg-gradient-to-t, .bg-gradient-to-b,
.bg-gradient-to-tr, .bg-gradient-to-tl, .bg-gradient-to-br, .bg-gradient-to-bl,
.gradient, [class*="gradient"] {
  background: #000000 !important;
  background-image: none !important;
  background-color: #000000 !important;
}

/* Override all gray backgrounds */
.bg-gray-50, .bg-gray-100, .bg-gray-200, .bg-gray-300, .bg-gray-400,
.bg-gray-500, .bg-gray-600, .bg-gray-700, .bg-gray-800, .bg-gray-900,
.bg-slate-50, .bg-slate-100, .bg-slate-200, .bg-slate-300, .bg-slate-400,
.bg-slate-500, .bg-slate-600, .bg-slate-700, .bg-slate-800, .bg-slate-900,
.bg-zinc-50, .bg-zinc-100, .bg-zinc-200, .bg-zinc-300, .bg-zinc-400,
.bg-zinc-500, .bg-zinc-600, .bg-zinc-700, .bg-zinc-800, .bg-zinc-900,
.bg-neutral-50, .bg-neutral-100, .bg-neutral-200, .bg-neutral-300, .bg-neutral-400,
.bg-neutral-500, .bg-neutral-600, .bg-neutral-700, .bg-neutral-800, .bg-neutral-900 {
  background-color: #000000 !important;
  background: #000000 !important;
}

/* Hover effects - only cyan allowed */
*:hover {
  color: #00bcd4 !important;
  border-color: #00bcd4 !important;
  background-color: #000000 !important;
}

/* Ensure buttons are black with white text */
button, .btn, .button, input[type="button"], input[type="submit"] {
  background-color: #000000 !important;
  background: #000000 !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

button:hover, .btn:hover, .button:hover, 
input[type="button"]:hover, input[type="submit"]:hover {
  background-color: #000000 !important;
  color: #00bcd4 !important;
  border-color: #00bcd4 !important;
}

/* Form elements */
input, textarea, select {
  background-color: #000000 !important;
  background: #000000 !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

input:focus, textarea:focus, select:focus {
  background-color: #000000 !important;
  border-color: #00bcd4 !important;
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2) !important;
}

/* Cards and panels */
.card, .panel, .glass-card, .glass-panel {
  background-color: #000000 !important;
  background: #000000 !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Navigation */
.nav, .navbar, .navigation, .sidebar {
  background-color: #000000 !important;
  background: #000000 !important;
}

/* Modals and overlays */
.modal, .overlay, .backdrop, .dialog {
  background-color: rgba(0, 0, 0, 0.9) !important;
}

.modal-content, .dialog-content {
  background-color: #000000 !important;
  background: #000000 !important;
}

/* Tables */
table, thead, tbody, tr, td, th {
  background-color: #000000 !important;
  background: #000000 !important;
  color: #ffffff !important;
}

/* Lists */
ul, ol, li {
  background-color: #000000 !important;
  background: #000000 !important;
  color: #ffffff !important;
}

/* Text elements */
p, span, label, a, strong, em, small {
  color: #ffffff !important;
  background-color: transparent !important;
}

a:hover {
  color: #00bcd4 !important;
}

/* SVG icons - keep them visible */
svg {
  background: transparent !important;
  color: inherit !important;
  fill: currentColor !important;
}

/* Scrollbars */
::-webkit-scrollbar {
  background: #000000 !important;
}

::-webkit-scrollbar-track {
  background: #000000 !important;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2) !important;
}

::-webkit-scrollbar-thumb:hover {
  background: #00bcd4 !important;
}

/* Final catch-all rule */
* {
  background-color: #000000 !important;
  background-image: none !important;
}

/* Exception for SVG elements */
svg, svg * {
  background-color: transparent !important;
}
