<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auth & Scan Test - ByteGuardX</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #0f172a;
            color: #e2e8f0;
        }
        .container {
            background: #1e293b;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
        }
        h1 {
            color: #00bcd4;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #00bcd4;
            border-bottom: 2px solid #00bcd4;
            padding-bottom: 10px;
        }
        button {
            background: #00bcd4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background: #0891b2;
        }
        button:disabled {
            background: #475569;
            cursor: not-allowed;
        }
        .response {
            background: #0f172a;
            padding: 15px;
            border-radius: 6px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 11px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #475569;
        }
        .success {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }
        .error {
            border-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        .warning {
            border-color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
        }
        input[type="file"] {
            margin: 10px 0;
            padding: 8px;
            background: #334155;
            color: #e2e8f0;
            border: 1px solid #475569;
            border-radius: 4px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background: #10b981; }
        .status-error { background: #ef4444; }
        .status-warning { background: #f59e0b; }
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        @media (max-width: 768px) {
            .grid { grid-template-columns: 1fr; }
        }
    </style>
</head>
<body>
    <h1>🛡️ ByteGuardX Auth & Scan Test</h1>
    
    <div class="grid">
        <!-- Authentication Tests -->
        <div class="container">
            <h2>🔐 Authentication Tests</h2>
            
            <div>
                <h3>Auth Status Check</h3>
                <button onclick="testAuthStatus()">Check Auth Status</button>
                <div id="auth-status">
                    <span class="status-indicator status-warning"></span>
                    Not tested yet
                </div>
                <div id="auth-response" class="response" style="display: none;"></div>
            </div>
            
            <div>
                <h3>Auth Endpoints</h3>
                <button onclick="testAuthEndpoints()">Test All Auth Endpoints</button>
                <div id="auth-endpoints-response" class="response" style="display: none;"></div>
            </div>
        </div>

        <!-- Scan Tests -->
        <div class="container">
            <h2>🔍 File Scanning Tests</h2>
            
            <div>
                <h3>Scan Endpoints</h3>
                <button onclick="testScanEndpoints()">Test Scan Endpoints</button>
                <div id="scan-endpoints-status">
                    <span class="status-indicator status-warning"></span>
                    Not tested yet
                </div>
                <div id="scan-endpoints-response" class="response" style="display: none;"></div>
            </div>
            
            <div>
                <h3>File Upload Test</h3>
                <input type="file" id="test-file" accept="*/*">
                <br>
                <button onclick="testFileUpload()">Test File Upload</button>
                <div id="upload-response" class="response" style="display: none;"></div>
            </div>
        </div>
    </div>

    <!-- Overall Status -->
    <div class="container">
        <h2>📊 Overall System Status</h2>
        <div id="system-status">
            <div>🔧 Backend API: <span id="backend-status">Checking...</span></div>
            <div>🌐 Frontend Proxy: <span id="frontend-status">Checking...</span></div>
            <div>🔐 Authentication: <span id="auth-system-status">Checking...</span></div>
            <div>🔍 File Scanning: <span id="scan-system-status">Checking...</span></div>
        </div>
    </div>

    <script>
        // Utility functions
        function log(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response ${type}`;
            element.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
        }

        function clearLog(elementId) {
            const element = document.getElementById(elementId);
            element.textContent = '';
            element.className = 'response';
        }

        function updateStatus(elementId, status, message) {
            const element = document.getElementById(elementId);
            const indicator = element.querySelector('.status-indicator');
            
            if (indicator) {
                indicator.className = `status-indicator status-${status}`;
            }
            
            const textNode = element.childNodes[element.childNodes.length - 1];
            if (textNode && textNode.nodeType === Node.TEXT_NODE) {
                textNode.textContent = message;
            } else {
                element.appendChild(document.createTextNode(message));
            }
        }

        // Test authentication status
        async function testAuthStatus() {
            clearLog('auth-response');
            log('auth-response', 'Testing authentication status...');
            
            try {
                const response = await fetch('/api/auth/verify', {
                    method: 'GET',
                    credentials: 'include'
                });
                
                const data = await response.json();
                
                log('auth-response', `Status: ${response.status} ${response.statusText}`);
                log('auth-response', `Response: ${JSON.stringify(data, null, 2)}`);
                
                if (response.ok && data.development_mode) {
                    updateStatus('auth-status', 'ok', 'Development mode - Working correctly');
                    log('auth-response', '✅ Auth endpoint working correctly in development mode', 'success');
                } else if (data.valid) {
                    updateStatus('auth-status', 'ok', 'User authenticated');
                    log('auth-response', '✅ User is authenticated', 'success');
                } else {
                    updateStatus('auth-status', 'warning', 'User not authenticated (expected)');
                    log('auth-response', '⚠️ User not authenticated (this is normal)', 'warning');
                }
                
            } catch (error) {
                updateStatus('auth-status', 'error', 'Auth check failed');
                log('auth-response', `❌ Error: ${error.message}`, 'error');
            }
        }

        // Test all auth endpoints
        async function testAuthEndpoints() {
            clearLog('auth-endpoints-response');
            log('auth-endpoints-response', 'Testing authentication endpoints...');
            
            const endpoints = [
                { path: '/api/auth/verify', method: 'GET' },
                { path: '/api/csrf-token', method: 'GET' },
                { path: '/api/auth/register', method: 'POST' },
                { path: '/api/auth/login', method: 'POST' }
            ];
            
            for (const endpoint of endpoints) {
                try {
                    log('auth-endpoints-response', `\nTesting ${endpoint.method} ${endpoint.path}...`);
                    
                    const options = {
                        method: endpoint.method,
                        credentials: 'include'
                    };
                    
                    if (endpoint.method === 'POST') {
                        options.headers = { 'Content-Type': 'application/json' };
                        options.body = JSON.stringify({ test: 'data' });
                    }
                    
                    const response = await fetch(endpoint.path, options);
                    const responseText = await response.text();
                    
                    log('auth-endpoints-response', `  Status: ${response.status}`);
                    
                    if (response.status !== 404) {
                        try {
                            const data = JSON.parse(responseText);
                            log('auth-endpoints-response', `  Response: ${JSON.stringify(data).substring(0, 100)}...`);
                        } catch {
                            log('auth-endpoints-response', `  Response: ${responseText.substring(0, 100)}...`);
                        }
                    }
                    
                } catch (error) {
                    log('auth-endpoints-response', `  Error: ${error.message}`);
                }
            }
            
            log('auth-endpoints-response', '\n✅ Auth endpoints test completed', 'success');
        }

        // Test scan endpoints
        async function testScanEndpoints() {
            clearLog('scan-endpoints-response');
            log('scan-endpoints-response', 'Testing scan endpoints...');
            
            const endpoints = [
                '/api/scan',
                '/scan/upload',
                '/api/scan/upload',
                '/api/scan/directory'
            ];
            
            let workingEndpoints = 0;
            
            for (const endpoint of endpoints) {
                try {
                    log('scan-endpoints-response', `\nTesting ${endpoint}...`);
                    
                    const response = await fetch(endpoint, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ test: 'data' }),
                        credentials: 'include'
                    });
                    
                    log('scan-endpoints-response', `  Status: ${response.status}`);
                    
                    if (response.status !== 404) {
                        workingEndpoints++;
                        const responseText = await response.text();
                        try {
                            const data = JSON.parse(responseText);
                            log('scan-endpoints-response', `  Response: ${JSON.stringify(data).substring(0, 100)}...`);
                        } catch {
                            log('scan-endpoints-response', `  Response: ${responseText.substring(0, 100)}...`);
                        }
                    }
                    
                } catch (error) {
                    log('scan-endpoints-response', `  Error: ${error.message}`);
                }
            }
            
            if (workingEndpoints > 0) {
                updateStatus('scan-endpoints-status', 'ok', `${workingEndpoints} scan endpoints available`);
                log('scan-endpoints-response', `\n✅ Found ${workingEndpoints} working scan endpoints`, 'success');
            } else {
                updateStatus('scan-endpoints-status', 'error', 'No scan endpoints found');
                log('scan-endpoints-response', '\n❌ No working scan endpoints found', 'error');
            }
        }

        // Test file upload
        async function testFileUpload() {
            clearLog('upload-response');
            
            const fileInput = document.getElementById('test-file');
            if (!fileInput.files[0]) {
                log('upload-response', '❌ Please select a file first', 'error');
                return;
            }
            
            log('upload-response', 'Testing file upload to scan endpoints...');
            
            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            formData.append('files', fileInput.files[0]); // Some endpoints expect 'files'
            
            const endpoints = ['/api/scan', '/scan/upload', '/api/scan/upload'];
            
            for (const endpoint of endpoints) {
                try {
                    log('upload-response', `\nTesting upload to ${endpoint}...`);
                    
                    const response = await fetch(endpoint, {
                        method: 'POST',
                        body: formData,
                        credentials: 'include'
                    });
                    
                    log('upload-response', `  Status: ${response.status}`);
                    
                    if (response.ok) {
                        const data = await response.json();
                        log('upload-response', `  ✅ Upload successful!`);
                        log('upload-response', `  Scan ID: ${data.scan_id}`);
                        log('upload-response', `  Files processed: ${data.files?.length || 1}`);
                        log('upload-response', '  Upload test PASSED', 'success');
                        return; // Stop testing other endpoints if one works
                    } else {
                        const responseText = await response.text();
                        log('upload-response', `  Response: ${responseText.substring(0, 200)}...`);
                    }
                    
                } catch (error) {
                    log('upload-response', `  Error: ${error.message}`);
                }
            }
            
            log('upload-response', '\n❌ All upload endpoints failed', 'error');
        }

        // Initialize tests on page load
        window.addEventListener('load', async () => {
            // Test backend connectivity
            try {
                const response = await fetch('/api/v1/health');
                if (response.ok) {
                    document.getElementById('backend-status').innerHTML = '<span class="status-indicator status-ok"></span>Online';
                } else {
                    document.getElementById('backend-status').innerHTML = '<span class="status-indicator status-warning"></span>Issues detected';
                }
            } catch (error) {
                document.getElementById('backend-status').innerHTML = '<span class="status-indicator status-error"></span>Offline';
            }
            
            // Auto-run auth test
            setTimeout(testAuthStatus, 1000);
            
            // Auto-run scan endpoints test
            setTimeout(testScanEndpoints, 2000);
        });
    </script>
</body>
</html>
