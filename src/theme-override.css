/* ByteGuardX Black/White/Cyan Theme Override */

/* AGGRESSIVE GLOBAL OVERRIDES - FORCE PURE BLACK EVERYWHERE */
*, *::before, *::after {
  box-sizing: border-box;
  background-color: #000000 !important;
  color: #ffffff !important;
}

html, body, #root {
  background-color: #000000 !important;
  color: #ffffff !important;
  background-image: none !important;
  background-gradient: none !important;
}

/* Force all divs, sections, and containers to be black */
div, section, main, article, aside, header, footer, nav {
  background-color: #000000 !important;
  background-image: none !important;
  background: #000000 !important;
}

/* FORCE REMOVE ALL GRADIENTS AND BACKGROUNDS */
.bg-gradient-to-br,
.bg-gradient-to-r,
.bg-gradient-to-l,
.bg-gradient-to-t,
.bg-gradient-to-b,
.bg-gradient-radial,
.gradient,
[class*="gradient"],
[style*="gradient"],
[class*="bg-gray"],
[class*="bg-slate"],
[class*="bg-zinc"],
[class*="bg-neutral"],
[class*="bg-stone"] {
  background: #000000 !important;
  background-image: none !important;
  background-color: #000000 !important;
}

/* Force override any inline styles */
[style*="background"] {
  background: #000000 !important;
  background-color: #000000 !important;
  background-image: none !important;
}

/* Ensure all text is white by default */
p, span, div, h1, h2, h3, h4, h5, h6, label {
  color: #ffffff !important;
}

/* Override any gray text to white */
.text-gray-300,
.text-gray-400,
.text-gray-500,
.text-gray-600 {
  color: #ffffff !important;
}

/* Keep only cyan for accents */
.text-cyan-400,
.text-cyan-500 {
  color: #00bcd4 !important;
}

/* Button hover effects - cyan only */
button:hover,
.btn:hover,
.button:hover {
  color: #00bcd4 !important;
  border-color: #00bcd4 !important;
}

/* Link hover effects */
a:hover {
  color: #00bcd4 !important;
}

/* Card hover effects */
.glass-card:hover,
.card:hover,
.hover-card:hover {
  border-color: #00bcd4 !important;
  box-shadow: 0 4px 20px rgba(0, 188, 212, 0.2) !important;
}

/* Input focus effects */
input:focus,
textarea:focus,
select:focus {
  border-color: #00bcd4 !important;
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2) !important;
}

/* Navigation active states */
.nav-link.active,
.nav-item.active {
  color: #00bcd4 !important;
}

/* Tab active states */
.tab.active,
.tab-active {
  color: #00bcd4 !important;
  border-color: #00bcd4 !important;
}

/* Progress bars */
.progress-bar {
  background-color: #00bcd4 !important;
}

/* Badges and chips */
.badge,
.chip {
  background-color: #000000 !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.badge:hover,
.chip:hover {
  border-color: #00bcd4 !important;
}

/* Modal and overlay backgrounds */
.modal,
.overlay,
.backdrop {
  background-color: rgba(0, 0, 0, 0.9) !important;
}

.modal-content,
.dialog-content {
  background-color: #000000 !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Dropdown menus */
.dropdown-menu,
.menu,
.popover {
  background-color: #000000 !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.dropdown-item:hover,
.menu-item:hover {
  background-color: rgba(0, 188, 212, 0.1) !important;
  color: #00bcd4 !important;
}

/* Table styles */
table {
  background-color: #000000 !important;
}

th, td {
  color: #ffffff !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

tr:hover {
  background-color: rgba(0, 188, 212, 0.1) !important;
}

/* Form elements */
.form-control,
.form-input,
.form-select {
  background-color: #000000 !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.form-control:focus,
.form-input:focus,
.form-select:focus {
  border-color: #00bcd4 !important;
  box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2) !important;
}

/* Checkbox and radio buttons */
input[type="checkbox"],
input[type="radio"] {
  accent-color: #00bcd4 !important;
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #000000;
}

::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #00bcd4;
}

/* Loading spinners */
.spinner,
.loading {
  border-color: rgba(255, 255, 255, 0.2) !important;
  border-top-color: #00bcd4 !important;
}

/* Alert and notification styles */
.alert,
.notification {
  background-color: #000000 !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  color: #ffffff !important;
}

.alert-success {
  border-color: #00bcd4 !important;
}

.alert-error {
  border-color: #ff4444 !important;
}

/* Tooltip styles */
.tooltip {
  background-color: #000000 !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Code blocks */
pre, code {
  background-color: #000000 !important;
  color: #ffffff !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
}

/* Syntax highlighting overrides */
.hljs {
  background: #000000 !important;
  color: #ffffff !important;
}

/* Remove any remaining colored backgrounds */
.bg-gray-50,
.bg-gray-100,
.bg-gray-200,
.bg-gray-300,
.bg-gray-400,
.bg-gray-500,
.bg-gray-600,
.bg-gray-700,
.bg-gray-800,
.bg-gray-900 {
  background-color: #000000 !important;
}

/* Ensure sidebar and header are pure black */
.sidebar,
.header,
.navbar {
  background-color: #000000 !important;
}

/* Override any remaining blue/purple accents with cyan */
.text-blue-400,
.text-blue-500,
.text-purple-400,
.text-purple-500,
.text-indigo-400,
.text-indigo-500 {
  color: #00bcd4 !important;
}

/* Interactive element hover states */
.interactive:hover,
.clickable:hover,
.selectable:hover {
  color: #00bcd4 !important;
  border-color: #00bcd4 !important;
}

/* Final catch-all for any missed elements */
[class*="bg-"]:not([class*="bg-black"]):not([class*="bg-transparent"]) {
  background-color: #000000 !important;
}

[class*="text-"]:not([class*="text-white"]):not([class*="text-cyan"]) {
  color: #ffffff !important;
}

/* NUCLEAR OPTION - FORCE EVERYTHING TO BE BLACK */
body * {
  background-color: #000000 !important;
  background-image: none !important;
  background: #000000 !important;
}

/* Only allow white text and cyan on hover */
body *:not(svg):not(path):not(circle):not(rect):not(line) {
  color: #ffffff !important;
}

body *:hover:not(svg):not(path):not(circle):not(rect):not(line) {
  color: #00bcd4 !important;
  border-color: #00bcd4 !important;
}

/* Ensure SVG icons remain visible */
svg, svg * {
  background: transparent !important;
  color: inherit !important;
  fill: currentColor !important;
}

/* Force override any remaining Tailwind classes */
.bg-gray-50, .bg-gray-100, .bg-gray-200, .bg-gray-300, .bg-gray-400,
.bg-gray-500, .bg-gray-600, .bg-gray-700, .bg-gray-800, .bg-gray-900,
.bg-slate-50, .bg-slate-100, .bg-slate-200, .bg-slate-300, .bg-slate-400,
.bg-slate-500, .bg-slate-600, .bg-slate-700, .bg-slate-800, .bg-slate-900,
.bg-zinc-50, .bg-zinc-100, .bg-zinc-200, .bg-zinc-300, .bg-zinc-400,
.bg-zinc-500, .bg-zinc-600, .bg-zinc-700, .bg-zinc-800, .bg-zinc-900,
.bg-neutral-50, .bg-neutral-100, .bg-neutral-200, .bg-neutral-300, .bg-neutral-400,
.bg-neutral-500, .bg-neutral-600, .bg-neutral-700, .bg-neutral-800, .bg-neutral-900,
.bg-stone-50, .bg-stone-100, .bg-stone-200, .bg-stone-300, .bg-stone-400,
.bg-stone-500, .bg-stone-600, .bg-stone-700, .bg-stone-800, .bg-stone-900 {
  background-color: #000000 !important;
  background: #000000 !important;
}

/* Override any backdrop or overlay elements */
.backdrop, .overlay, .modal-backdrop, .drawer-backdrop {
  background-color: rgba(0, 0, 0, 0.9) !important;
}

/* Ensure all containers are black */
.container, .wrapper, .content, .main, .app {
  background-color: #000000 !important;
  background: #000000 !important;
}
