<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signup Test - ByteGuardX</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #0f172a;
            color: #e2e8f0;
        }
        .container {
            background: #1e293b;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #00bcd4;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin: 15px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #e2e8f0;
            font-weight: 500;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 1px solid #475569;
            border-radius: 6px;
            background: #334155;
            color: #e2e8f0;
            box-sizing: border-box;
            font-size: 14px;
        }
        input:focus {
            outline: none;
            border-color: #00bcd4;
            box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2);
        }
        button {
            width: 100%;
            background: #00bcd4;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            margin-top: 10px;
        }
        button:hover {
            background: #0891b2;
        }
        button:disabled {
            background: #475569;
            cursor: not-allowed;
        }
        .response {
            background: #0f172a;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #475569;
        }
        .success {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }
        .error {
            border-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        .info {
            background: #334155;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #00bcd4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ ByteGuardX Signup Test</h1>
        
        <div class="info">
            <strong>Test Status:</strong> CSRF protection is disabled in development mode.<br>
            <strong>Backend:</strong> http://localhost:5000<br>
            <strong>Frontend:</strong> http://localhost:3000
        </div>

        <form id="signup-form">
            <div class="form-group">
                <label for="username">Username:</label>
                <input type="text" id="username" name="username" value="testuser123" required>
            </div>
            
            <div class="form-group">
                <label for="email">Email:</label>
                <input type="email" id="email" name="email" value="<EMAIL>" required>
            </div>
            
            <div class="form-group">
                <label for="password">Password:</label>
                <input type="password" id="password" name="password" value="SecurePass123!" required>
            </div>
            
            <div class="form-group">
                <label for="confirm_password">Confirm Password:</label>
                <input type="password" id="confirm_password" name="confirm_password" value="SecurePass123!" required>
            </div>
            
            <button type="submit" id="submit-btn">Create Account</button>
        </form>

        <div id="response" class="response" style="display: none;"></div>
    </div>

    <script>
        document.getElementById('signup-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submit-btn');
            const responseDiv = document.getElementById('response');
            
            // Disable button and show loading
            submitBtn.disabled = true;
            submitBtn.textContent = 'Creating Account...';
            responseDiv.style.display = 'block';
            responseDiv.className = 'response';
            responseDiv.textContent = 'Submitting registration request...';
            
            // Get form data
            const formData = new FormData(e.target);
            
            // Convert to JSON
            const userData = {
                username: formData.get('username'),
                email: formData.get('email'),
                password: formData.get('password'),
                confirm_password: formData.get('confirm_password')
            };
            
            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    credentials: 'include',
                    body: JSON.stringify(userData)
                });
                
                const responseText = await response.text();
                let responseData;
                
                try {
                    responseData = JSON.parse(responseText);
                } catch {
                    responseData = { message: responseText };
                }
                
                // Display response
                responseDiv.textContent = `Status: ${response.status} ${response.statusText}\n\n`;
                responseDiv.textContent += `Response:\n${JSON.stringify(responseData, null, 2)}`;
                
                if (response.ok) {
                    responseDiv.className = 'response success';
                    responseDiv.textContent = `✅ SUCCESS!\n\n${responseDiv.textContent}`;
                    
                    // Clear form on success
                    setTimeout(() => {
                        e.target.reset();
                        document.getElementById('username').value = 'testuser' + Math.floor(Math.random() * 1000);
                        document.getElementById('email').value = 'testuser' + Math.floor(Math.random() * 1000) + '@example.com';
                    }, 2000);
                } else {
                    responseDiv.className = 'response error';
                    responseDiv.textContent = `❌ ERROR!\n\n${responseDiv.textContent}`;
                    
                    // Check for specific CSRF error
                    if (responseText.includes('CSRF') || responseText.includes('csrf')) {
                        responseDiv.textContent += '\n\n🛡️ CSRF Protection Issue:\nThis should not happen in development mode. Check backend logs.';
                    }
                }
                
            } catch (error) {
                responseDiv.className = 'response error';
                responseDiv.textContent = `❌ NETWORK ERROR!\n\nError: ${error.message}\n\nThis usually means:\n1. Backend server is not running\n2. CORS issues\n3. Network connectivity problems`;
            } finally {
                // Re-enable button
                submitBtn.disabled = false;
                submitBtn.textContent = 'Create Account';
            }
        });

        // Test backend connectivity on page load
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('/api/v1/health');
                if (response.ok) {
                    console.log('✅ Backend connectivity test passed');
                } else {
                    console.warn('⚠️ Backend responded with status:', response.status);
                }
            } catch (error) {
                console.error('❌ Backend connectivity test failed:', error);
            }
        });
    </script>
</body>
</html>
