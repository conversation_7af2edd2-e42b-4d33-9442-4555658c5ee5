import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { 
  Map, 
  Calendar, 
  Filter,
  TrendingUp,
  AlertTriangle,
  FileText,
  Folder,
  MoreHorizontal
} from 'lucide-react';

const VulnerabilityHeatmap = ({ data }) => {
  const [heatmapData, setHeatmapData] = useState(null);
  const [viewMode, setViewMode] = useState('files'); // 'files' or 'time'
  const [selectedSeverity, setSelectedSeverity] = useState('all');

  useEffect(() => {
    generateHeatmapData();
  }, [data, viewMode, selectedSeverity]);

  const generateHeatmapData = () => {
    // Generate mock heatmap data based on the view mode
    if (viewMode === 'files') {
      const fileData = [
        { path: '/src/components/Auth.jsx', vulnerabilities: 8, severity: 'high', lastScan: '2024-01-15' },
        { path: '/src/utils/api.js', vulnerabilities: 12, severity: 'critical', lastScan: '2024-01-15' },
        { path: '/src/pages/Login.jsx', vulnerabilities: 3, severity: 'medium', lastScan: '2024-01-14' },
        { path: '/api/routes/user.js', vulnerabilities: 15, severity: 'critical', lastScan: '2024-01-15' },
        { path: '/src/components/Dashboard.jsx', vulnerabilities: 2, severity: 'low', lastScan: '2024-01-15' },
        { path: '/config/database.js', vulnerabilities: 6, severity: 'high', lastScan: '2024-01-14' },
        { path: '/src/utils/validation.js', vulnerabilities: 4, severity: 'medium', lastScan: '2024-01-15' },
        { path: '/api/middleware/auth.js', vulnerabilities: 9, severity: 'high', lastScan: '2024-01-14' },
        { path: '/src/components/FileUpload.jsx', vulnerabilities: 7, severity: 'high', lastScan: '2024-01-15' },
        { path: '/src/services/encryption.js', vulnerabilities: 11, severity: 'critical', lastScan: '2024-01-13' },
        { path: '/src/pages/Settings.jsx', vulnerabilities: 1, severity: 'low', lastScan: '2024-01-15' },
        { path: '/api/routes/admin.js', vulnerabilities: 13, severity: 'critical', lastScan: '2024-01-14' }
      ];

      // Filter by severity if selected
      const filteredData = selectedSeverity === 'all' 
        ? fileData 
        : fileData.filter(item => item.severity === selectedSeverity);

      setHeatmapData({
        type: 'files',
        items: filteredData.sort((a, b) => b.vulnerabilities - a.vulnerabilities)
      });
    } else {
      // Time-based heatmap (last 30 days)
      const timeData = [];
      const today = new Date();
      
      for (let i = 29; i >= 0; i--) {
        const date = new Date(today);
        date.setDate(date.getDate() - i);
        
        const dayOfWeek = date.getDay();
        const isWeekend = dayOfWeek === 0 || dayOfWeek === 6;
        
        // Generate realistic scan activity (less on weekends)
        const baseActivity = isWeekend ? 0.2 : 0.8;
        const randomFactor = Math.random() * 0.5 + 0.5;
        const activity = Math.floor(baseActivity * randomFactor * 20);
        
        timeData.push({
          date: date.toISOString().split('T')[0],
          day: date.getDate(),
          month: date.getMonth(),
          weekday: dayOfWeek,
          vulnerabilities: activity,
          scans: Math.floor(activity * 0.3) + 1
        });
      }

      setHeatmapData({
        type: 'time',
        items: timeData
      });
    }
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'critical': return 'bg-red-500 text-white';
      case 'high': return 'bg-orange-500 text-white';
      case 'medium': return 'bg-yellow-500 text-black';
      case 'low': return 'bg-blue-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };

  const getIntensityColor = (value, max) => {
    const intensity = value / max;
    if (intensity === 0) return 'bg-gray-100';
    if (intensity <= 0.2) return 'bg-green-200';
    if (intensity <= 0.4) return 'bg-yellow-200';
    if (intensity <= 0.6) return 'bg-orange-200';
    if (intensity <= 0.8) return 'bg-red-200';
    return 'bg-red-400';
  };

  const maxVulnerabilities = useMemo(() => {
    if (!heatmapData) return 0;
    return Math.max(...heatmapData.items.map(item => item.vulnerabilities));
  }, [heatmapData]);

  if (!heatmapData) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Map className="h-5 w-5 mr-2" />
            Vulnerability Heatmap
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-cyan-500"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Map className="h-5 w-5 mr-2" />
            Vulnerability Heatmap
          </div>
          <div className="flex items-center space-x-2">
            {/* View Mode Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              <button
                onClick={() => setViewMode('files')}
                className={`px-3 py-1 text-xs rounded-md transition-colors ${
                  viewMode === 'files'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600'
                }`}
              >
                <FileText className="h-3 w-3 mr-1 inline" />
                Files
              </button>
              <button
                onClick={() => setViewMode('time')}
                className={`px-3 py-1 text-xs rounded-md transition-colors ${
                  viewMode === 'time'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-600'
                }`}
              >
                <Calendar className="h-3 w-3 mr-1 inline" />
                Timeline
              </button>
            </div>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {/* Filters */}
        {viewMode === 'files' && (
          <div className="flex items-center space-x-2 mb-4">
            <Filter className="h-4 w-4 text-gray-500" />
            <span className="text-sm text-gray-600">Filter by severity:</span>
            {['all', 'critical', 'high', 'medium', 'low'].map((severity) => (
              <button
                key={severity}
                onClick={() => setSelectedSeverity(severity)}
                className={`px-2 py-1 text-xs rounded-md transition-colors ${
                  selectedSeverity === severity
                    ? 'bg-cyan-100 text-cyan-700'
                    : 'text-gray-600 hover:bg-gray-100'
                }`}
              >
                {severity.charAt(0).toUpperCase() + severity.slice(1)}
              </button>
            ))}
          </div>
        )}

        {/* Heatmap Content */}
        {viewMode === 'files' ? (
          <div className="space-y-2">
            {heatmapData.items.slice(0, 10).map((file, index) => (
              <div
                key={file.path}
                className="flex items-center justify-between p-3 rounded-lg border hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center space-x-3 flex-1 min-w-0">
                  <div className={`w-4 h-4 rounded ${getIntensityColor(file.vulnerabilities, maxVulnerabilities)}`}></div>
                  <div className="flex items-center space-x-2 min-w-0">
                    <FileText className="h-4 w-4 text-gray-400 flex-shrink-0" />
                    <span className="text-sm font-mono truncate" title={file.path}>
                      {file.path}
                    </span>
                  </div>
                </div>
                <div className="flex items-center space-x-2 flex-shrink-0">
                  <Badge variant="outline" className="text-xs">
                    {file.vulnerabilities} issues
                  </Badge>
                  <Badge className={`text-xs ${getSeverityColor(file.severity)}`}>
                    {file.severity}
                  </Badge>
                  <span className="text-xs text-gray-500">{file.lastScan}</span>
                </div>
              </div>
            ))}
            
            {heatmapData.items.length > 10 && (
              <div className="text-center pt-2">
                <Button variant="outline" size="sm">
                  <MoreHorizontal className="h-4 w-4 mr-1" />
                  Show {heatmapData.items.length - 10} more files
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1 text-xs">
              {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
                <div key={day} className="text-center text-gray-500 font-medium p-1">
                  {day}
                </div>
              ))}
              
              {heatmapData.items.map((day, index) => (
                <div
                  key={day.date}
                  className={`aspect-square rounded text-center flex items-center justify-center text-xs font-medium cursor-pointer transition-all hover:scale-110 ${
                    getIntensityColor(day.vulnerabilities, maxVulnerabilities)
                  }`}
                  title={`${day.date}: ${day.vulnerabilities} vulnerabilities found in ${day.scans} scans`}
                >
                  {day.day}
                </div>
              ))}
            </div>

            {/* Legend */}
            <div className="flex items-center justify-between text-xs text-gray-600">
              <span>Less</span>
              <div className="flex space-x-1">
                <div className="w-3 h-3 bg-gray-100 rounded"></div>
                <div className="w-3 h-3 bg-green-200 rounded"></div>
                <div className="w-3 h-3 bg-yellow-200 rounded"></div>
                <div className="w-3 h-3 bg-orange-200 rounded"></div>
                <div className="w-3 h-3 bg-red-200 rounded"></div>
                <div className="w-3 h-3 bg-red-400 rounded"></div>
              </div>
              <span>More</span>
            </div>

            {/* Summary Stats */}
            <div className="grid grid-cols-3 gap-4 pt-4 border-t">
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">
                  {heatmapData.items.reduce((sum, day) => sum + day.vulnerabilities, 0)}
                </div>
                <div className="text-xs text-gray-600">Total Vulnerabilities</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">
                  {heatmapData.items.reduce((sum, day) => sum + day.scans, 0)}
                </div>
                <div className="text-xs text-gray-600">Total Scans</div>
              </div>
              <div className="text-center">
                <div className="text-lg font-bold text-gray-900">
                  {Math.round(heatmapData.items.reduce((sum, day) => sum + day.vulnerabilities, 0) / 30)}
                </div>
                <div className="text-xs text-gray-600">Daily Average</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default VulnerabilityHeatmap;
