import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home } from 'lucide-react'
import { Link } from 'react-router-dom'

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error) {
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    // Generate unique error ID for tracking
    const errorId = `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    this.setState({
      error: error,
      errorInfo: errorInfo,
      errorId: errorId
    })

    // Enhanced error logging
    const errorDetails = {
      errorId,
      message: error.message,
      stack: error.stack,
      componentStack: errorInfo.componentStack,
      timestamp: new Date().toISOString(),
      userAgent: navigator.userAgent,
      url: window.location.href
    }

    // Log error to console with better formatting
    console.error('ErrorBoundary caught an error:')
    console.error('Error ID:', errorDetails.errorId)
    console.error('Message:', errorDetails.message)
    console.error('Stack:', errorDetails.stack)
    console.error('Component Stack:', errorDetails.componentStack)

    // Report error to monitoring service (if available)
    if (window.reportError && typeof window.reportError === 'function') {
      try {
        window.reportError(errorDetails)
      } catch (reportingError) {
        console.error('Failed to report error:', reportingError)
      }
    }
  }

  handleReload = () => {
    window.location.reload()
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-black text-white flex items-center justify-center px-4">
          <div className="max-w-md w-full text-center">
            <div className="mb-8">
              <AlertTriangle className="h-16 w-16 text-red-400 mx-auto mb-4" />
              <h1 className="text-2xl font-bold text-white mb-2">
                Something went wrong
              </h1>
              <p className="text-gray-400">
                An unexpected error occurred. Please try refreshing the page or go back to the home page.
              </p>
            </div>

            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <button
                  onClick={this.handleReload}
                  className="btn-primary"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Reload Page
                </button>
                
                <Link to="/" className="btn-secondary">
                  <Home className="h-4 w-4 mr-2" />
                  Go Home
                </Link>
              </div>
            </div>

            {/* Error details in development */}
            {import.meta.env.DEV && this.state.error && (
              <details className="mt-8 text-left">
                <summary className="text-sm text-gray-400 cursor-pointer mb-2">
                  Error Details (Development)
                </summary>
                <div className="bg-gray-900 border border-gray-700 rounded-lg p-4 text-xs">
                  <div className="text-red-400 font-mono mb-2">
                    {this.state.error.toString()}
                  </div>
                  <div className="text-gray-400 font-mono whitespace-pre-wrap">
                    {this.state.errorInfo.componentStack}
                  </div>
                </div>
              </details>
            )}
          </div>
        </div>
      )
    }

    return this.props.children
  }
}

export default ErrorBoundary
