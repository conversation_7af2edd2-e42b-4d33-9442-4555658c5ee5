import React from 'react'
import { motion } from 'framer-motion'
import { Loader2 } from 'lucide-react'

const Button = ({ 
  children, 
  variant = 'primary',
  size = 'md',
  loading = false,
  disabled = false,
  icon: Icon,
  iconPosition = 'left',
  fullWidth = false,
  glow = false,
  className = '',
  onClick,
  ...props 
}) => {
  const baseClasses = `
    inline-flex items-center justify-center font-medium rounded-2xl
    transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-black
    disabled:opacity-50 disabled:cursor-not-allowed backdrop-filter backdrop-blur-sm
    ${fullWidth ? 'w-full' : ''}
  `

  const variants = {
    primary: `
      bg-gradient-to-r from-cyan-500/80 to-blue-600/80 text-white border border-cyan-400/30
      hover:from-cyan-400/90 hover:to-blue-500/90 hover:border-cyan-300/50 hover:shadow-lg hover:shadow-cyan-500/25
      focus:ring-cyan-500 active:scale-95
    `,
    secondary: `
      glass-panel border border-white/20 text-white
      hover:border-cyan-400/50 hover:bg-white/10 hover:shadow-lg hover:text-cyan-400
      focus:ring-cyan-500 active:scale-95
    `,
    ghost: `
      text-gray-300 hover:text-cyan-400 hover:bg-white/5 border border-transparent
      hover:border-cyan-400/20 focus:ring-cyan-500 active:scale-95
    `,
    danger: `
      bg-gradient-to-r from-red-500/80 to-red-600/80 text-white border border-red-400/30
      hover:from-red-400/90 hover:to-red-500/90 hover:border-red-300/50 hover:shadow-lg hover:shadow-red-500/25
      focus:ring-red-500 active:scale-95
    `,
    success: `
      bg-gradient-to-r from-green-500/80 to-green-600/80 text-white border border-green-400/30
      hover:from-green-400/90 hover:to-green-500/90 hover:border-green-300/50 hover:shadow-lg hover:shadow-green-500/25
      focus:ring-green-500 active:scale-95
    `
  }

  const sizes = {
    sm: 'px-3 py-2 text-sm gap-2',
    md: 'px-6 py-3 text-base gap-2',
    lg: 'px-8 py-4 text-lg gap-3',
    xl: 'px-10 py-5 text-xl gap-4'
  }

  const glowClass = glow ? 'hover-glow' : ''
  const isDisabled = disabled || loading

  return (
    <motion.button
      className={`${baseClasses} ${variants[variant]} ${sizes[size]} ${glowClass} ${className}`}
      onClick={onClick}
      disabled={isDisabled}
      whileHover={!isDisabled ? { scale: 1.02 } : {}}
      whileTap={!isDisabled ? { scale: 0.98 } : {}}
      {...props}
    >
      {loading ? (
        <Loader2 className="h-4 w-4 animate-spin" />
      ) : (
        <>
          {Icon && iconPosition === 'left' && <Icon className="h-4 w-4" />}
          {children}
          {Icon && iconPosition === 'right' && <Icon className="h-4 w-4" />}
        </>
      )}
    </motion.button>
  )
}

export default Button
