<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>File Scanning Test - ByteGuardX</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #0f172a;
            color: #e2e8f0;
        }
        .container {
            background: #1e293b;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #00bcd4;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #475569;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .upload-area:hover {
            border-color: #00bcd4;
            background: rgba(0, 188, 212, 0.05);
        }
        .upload-area.dragover {
            border-color: #00bcd4;
            background: rgba(0, 188, 212, 0.1);
        }
        input[type="file"] {
            display: none;
        }
        button {
            background: #00bcd4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        button:hover {
            background: #0891b2;
        }
        button:disabled {
            background: #475569;
            cursor: not-allowed;
        }
        .response {
            background: #0f172a;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #475569;
        }
        .success {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }
        .error {
            border-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        .info {
            background: #334155;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 20px;
            border-left: 4px solid #00bcd4;
        }
        .file-info {
            background: #334155;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
        }
        .progress {
            width: 100%;
            height: 20px;
            background: #334155;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #00bcd4, #0891b2);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 ByteGuardX File Scanning Test</h1>
        
        <div class="info">
            <strong>Test Status:</strong> File scanning endpoint is available at /api/scan<br>
            <strong>Max File Size:</strong> 10MB<br>
            <strong>Supported Files:</strong> All file types (development mode)
        </div>

        <div class="upload-area" id="upload-area" onclick="document.getElementById('file-input').click()">
            <div id="upload-text">
                <h3>📁 Drop files here or click to browse</h3>
                <p>Select a file to scan for vulnerabilities</p>
            </div>
            <input type="file" id="file-input" accept="*/*">
        </div>

        <div id="file-info" class="file-info" style="display: none;">
            <h4>Selected File:</h4>
            <div id="file-details"></div>
        </div>

        <div class="progress" id="progress" style="display: none;">
            <div class="progress-bar" id="progress-bar"></div>
        </div>

        <div style="text-align: center;">
            <button id="scan-btn" onclick="scanFile()" disabled>🔍 Scan File</button>
            <button onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div id="response" class="response" style="display: none;"></div>
    </div>

    <script>
        let selectedFile = null;

        // File input change handler
        document.getElementById('file-input').addEventListener('change', function(e) {
            handleFileSelect(e.target.files[0]);
        });

        // Drag and drop handlers
        const uploadArea = document.getElementById('upload-area');
        
        uploadArea.addEventListener('dragover', function(e) {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', function(e) {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            handleFileSelect(e.dataTransfer.files[0]);
        });

        function handleFileSelect(file) {
            if (!file) return;

            selectedFile = file;
            
            // Show file info
            const fileInfo = document.getElementById('file-info');
            const fileDetails = document.getElementById('file-details');
            
            fileDetails.innerHTML = `
                <strong>Name:</strong> ${file.name}<br>
                <strong>Size:</strong> ${formatFileSize(file.size)}<br>
                <strong>Type:</strong> ${file.type || 'Unknown'}<br>
                <strong>Last Modified:</strong> ${new Date(file.lastModified).toLocaleString()}
            `;
            
            fileInfo.style.display = 'block';
            
            // Enable scan button
            document.getElementById('scan-btn').disabled = false;
            
            // Update upload area text
            document.getElementById('upload-text').innerHTML = `
                <h3>✅ File Selected: ${file.name}</h3>
                <p>Click "Scan File" to start vulnerability analysis</p>
            `;
        }

        async function scanFile() {
            if (!selectedFile) {
                alert('Please select a file first');
                return;
            }

            const scanBtn = document.getElementById('scan-btn');
            const responseDiv = document.getElementById('response');
            const progress = document.getElementById('progress');
            const progressBar = document.getElementById('progress-bar');
            
            // Disable button and show progress
            scanBtn.disabled = true;
            scanBtn.textContent = '🔍 Scanning...';
            progress.style.display = 'block';
            responseDiv.style.display = 'block';
            responseDiv.className = 'response';
            responseDiv.textContent = 'Uploading file and starting scan...';

            // Animate progress bar
            let progressValue = 0;
            const progressInterval = setInterval(() => {
                progressValue += Math.random() * 10;
                if (progressValue > 90) progressValue = 90;
                progressBar.style.width = progressValue + '%';
            }, 200);

            try {
                // Create form data
                const formData = new FormData();
                formData.append('file', selectedFile);

                // Send scan request
                const response = await fetch('/api/scan', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include'
                });

                // Complete progress
                clearInterval(progressInterval);
                progressBar.style.width = '100%';

                const responseText = await response.text();
                let responseData;

                try {
                    responseData = JSON.parse(responseText);
                } catch {
                    responseData = { message: responseText };
                }

                // Display results
                responseDiv.textContent = `Status: ${response.status} ${response.statusText}\n\n`;
                responseDiv.textContent += `Response:\n${JSON.stringify(responseData, null, 2)}`;

                if (response.ok) {
                    responseDiv.className = 'response success';
                    responseDiv.textContent = `✅ SCAN COMPLETED!\n\n${responseDiv.textContent}`;
                    
                    // Show scan summary if available
                    if (responseData.summary) {
                        const summary = responseData.summary;
                        responseDiv.textContent += `\n\n📊 SCAN SUMMARY:\n`;
                        responseDiv.textContent += `Total Findings: ${summary.total_findings}\n`;
                        responseDiv.textContent += `High Severity: ${summary.high_severity}\n`;
                        responseDiv.textContent += `Medium Severity: ${summary.medium_severity}\n`;
                        responseDiv.textContent += `Low Severity: ${summary.low_severity}\n`;
                        responseDiv.textContent += `Scan Duration: ${summary.scan_duration}`;
                    }
                } else {
                    responseDiv.className = 'response error';
                    responseDiv.textContent = `❌ SCAN FAILED!\n\n${responseDiv.textContent}`;
                }

            } catch (error) {
                clearInterval(progressInterval);
                responseDiv.className = 'response error';
                responseDiv.textContent = `❌ NETWORK ERROR!\n\nError: ${error.message}\n\nThis usually means:\n1. Backend server is not running\n2. Scan endpoint is not available\n3. Network connectivity problems\n4. File upload issues`;
            } finally {
                // Re-enable button and hide progress
                scanBtn.disabled = false;
                scanBtn.textContent = '🔍 Scan File';
                setTimeout(() => {
                    progress.style.display = 'none';
                    progressBar.style.width = '0%';
                }, 2000);
            }
        }

        function clearResults() {
            document.getElementById('response').style.display = 'none';
            document.getElementById('file-info').style.display = 'none';
            document.getElementById('progress').style.display = 'none';
            document.getElementById('scan-btn').disabled = true;
            document.getElementById('file-input').value = '';
            selectedFile = null;
            
            document.getElementById('upload-text').innerHTML = `
                <h3>📁 Drop files here or click to browse</h3>
                <p>Select a file to scan for vulnerabilities</p>
            `;
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // Test backend connectivity on page load
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('/api/v1/health');
                if (response.ok) {
                    console.log('✅ Backend connectivity test passed');
                } else {
                    console.warn('⚠️ Backend responded with status:', response.status);
                }
            } catch (error) {
                console.error('❌ Backend connectivity test failed:', error);
            }
        });
    </script>
</body>
</html>
