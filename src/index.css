/* ALL @import statements MUST come first */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
/* TEMPORARILY DISABLED AGGRESSIVE THEME FILES TO FIX BLACK OVERLAY */
/* @import './theme-override.css'; */
/* @import './force-black-theme.css'; */

/* Tailwind directives */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* BASIC BLACK THEME (NON-AGGRESSIVE) */
* {
  box-sizing: border-box;
}

html, body {
  background: transparent;
  color: #ffffff;
  margin: 0;
  padding: 0;
}

#root {
  background: transparent;
  min-height: 100vh;
}

/* Global Background Override - Remove all backgrounds to let <PERSON><PERSON><PERSON> show through */
*, *::before, *::after {
  background-color: transparent !important;
  background-image: none !important;
  background: transparent !important;
}

/* Exception for specific UI elements that need backgrounds */
.bg-black, .bg-gray-900, .bg-gray-800, .bg-gray-700, .bg-white, .bg-cyan-500, .bg-blue-500 {
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* Ensure text remains visible */
body, #root {
  color: #ffffff !important;
}

/* Basic element styling (non-aggressive) */
body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

/* Custom Authentication Styles */
@layer components {
  /* Pure Black Background with White/Cyan Theme */
  .glass-card {
    @apply rounded-2xl p-6 shadow-2xl transition-all duration-300;
    background: #000000;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #ffffff;
  }

  .glass-card:hover {
    border-color: #00bcd4;
    box-shadow: 0 8px 32px rgba(0, 188, 212, 0.2);
  }

  .glass-panel {
    background: #000000;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #ffffff;
    transition: all 0.3s ease;
  }

  .glass-panel:hover {
    border-color: #00bcd4;
  }

  .glass-nav {
    background: #000000;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #ffffff;
  }

  /* Enhanced Black Theme Variants */
  .glass-card-elevated {
    @apply glass-card shadow-2xl;
    background: #000000;
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  .glass-card-minimal {
    @apply rounded-xl p-4 transition-all duration-300;
    background: #000000;
    border: 1px solid rgba(255, 255, 255, 0.08);
    color: #ffffff;
  }

  .glass-card-minimal:hover {
    border-color: #00bcd4;
  }

  .glass-card-interactive {
    @apply glass-card cursor-pointer transition-all duration-300;
  }

  .glass-card-interactive:hover {
    background: #000000;
    border-color: #00bcd4;
    box-shadow: 0 16px 64px rgba(0, 188, 212, 0.3);
    transform: translateY(-2px);
  }

  /* Input styles - Black/White/Cyan Theme */
  .input {
    @apply w-full px-4 py-3 border rounded-lg
           text-white placeholder-gray-400 focus:ring-2 focus:ring-cyan-400
           focus:border-transparent transition-all duration-200;
    background: #000000;
    border-color: rgba(255, 255, 255, 0.2);
  }

  .input:hover {
    border-color: rgba(255, 255, 255, 0.4);
  }

  .input:focus {
    @apply shadow-lg;
    background: #000000;
    border-color: #00bcd4;
    box-shadow: 0 0 0 2px rgba(0, 188, 212, 0.2);
  }

  /* Button styles - Black/White/Cyan Theme */
  .btn-primary {
    @apply px-6 py-3 font-semibold rounded-lg shadow-lg
           focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black
           transition-all duration-200 transform hover:scale-105;
    background: #000000;
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .btn-primary:hover {
    background: #00bcd4;
    color: #000000;
    border-color: #00bcd4;
    box-shadow: 0 8px 32px rgba(0, 188, 212, 0.3);
  }

  .btn-secondary {
    @apply px-6 py-3 font-semibold rounded-lg
           focus:ring-2 focus:ring-cyan-400 focus:ring-offset-2 focus:ring-offset-black
           transition-all duration-200;
    background: #000000;
    color: #ffffff;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .btn-secondary:hover {
    border-color: #00bcd4;
    color: #00bcd4;
  }

  /* Enhanced Gradient text - Cyan Theme */
  .gradient-text {
    @apply bg-gradient-to-r from-cyan-400 to-cyan-200 bg-clip-text text-transparent;
  }

  /* Additional Black/White/Cyan Theme Styles */
  .text-gradient {
    background: linear-gradient(135deg, #00bcd4, #ffffff);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Navigation and Menu Styles */
  .nav-link {
    @apply text-white transition-all duration-300 hover:text-cyan-400;
  }

  /* Card hover effects */
  .hover-card {
    @apply transition-all duration-300 cursor-pointer;
    background: #000000;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .hover-card:hover {
    border-color: #00bcd4;
    box-shadow: 0 4px 20px rgba(0, 188, 212, 0.2);
    transform: translateY(-2px);
  }

  /* Table styles */
  .table-dark {
    background: #000000;
    color: #ffffff;
  }

  .table-dark th,
  .table-dark td {
    border-color: rgba(255, 255, 255, 0.1);
  }

  .table-dark tr:hover {
    background-color: rgba(0, 188, 212, 0.1);
  }

  /* Modal and overlay styles */
  .modal-overlay {
    background: rgba(0, 0, 0, 0.8);
  }

  .modal-content {
    background: #000000;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #ffffff;
  }

  /* Scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #000000;
  }

  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #00bcd4;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-cyan-400 via-blue-400 to-purple-400 bg-clip-text text-transparent;
  }

  /* Enhanced Animation Classes */
  .hover-lift {
    @apply transition-all duration-300 ease-out;
  }

  .hover-lift:hover {
    transform: translateY(-4px);
  }

  /* Quantum Glassmorphism Effects */
  .quantum-glass {
    background: linear-gradient(135deg,
      rgba(255, 255, 255, 0.12) 0%,
      rgba(255, 255, 255, 0.05) 50%,
      rgba(255, 255, 255, 0.08) 100%);
    backdrop-filter: blur(20px) saturate(180%);
    border: 2px solid transparent;
    border-radius: 1rem;
    position: relative;
    overflow: hidden;
  }

  .quantum-glass::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(135deg,
      rgba(6, 182, 212, 0.6) 0%,
      rgba(59, 130, 246, 0.4) 50%,
      rgba(147, 51, 234, 0.6) 100%);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .quantum-glass:hover::before {
    opacity: 1;
  }

  /* Advanced Micro-Animations */
  .pulse-glow {
    animation: pulseGlow 2s ease-in-out infinite;
  }

  @keyframes pulseGlow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(6, 182, 212, 0.6);
    }
  }

  .shimmer {
    background: linear-gradient(90deg,
      transparent 0%,
      rgba(6, 182, 212, 0.4) 50%,
      transparent 100%);
    background-size: 200% 100%;
    animation: shimmer 3s linear infinite;
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  .float-animation {
    animation: float 4s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  /* Advanced Accessibility Features */
  .high-contrast {
    filter: contrast(150%) brightness(120%);
  }

  .large-text {
    font-size: 120% !important;
  }

  .reduced-motion * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  /* Keyboard Navigation Enhancement */
  .keyboard-navigation *:focus {
    outline: 3px solid #06B6D4 !important;
    outline-offset: 2px !important;
    border-radius: 4px;
  }

  /* Color Blind Support */
  [data-colorblind="protanopia"] {
    filter: url(#protanopia);
  }

  [data-colorblind="deuteranopia"] {
    filter: url(#deuteranopia);
  }

  [data-colorblind="tritanopia"] {
    filter: url(#tritanopia);
  }

  /* Screen Reader Only Content */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Spatial Design Elements */
  .spatial-container {
    perspective: 1000px;
    transform-style: preserve-3d;
  }

  .spatial-card {
    transform-style: preserve-3d;
    transition: transform 0.3s ease;
  }

  .spatial-card:hover {
    transform: rotateY(5deg) rotateX(5deg) translateZ(20px);
  }

  /* Advanced Gradient Backgrounds */
  .cyber-gradient {
    background: linear-gradient(135deg,
      #0F172A 0%,
      #1E293B 25%,
      #0F172A 50%,
      #1E293B 75%,
      #0F172A 100%);
    background-size: 400% 400%;
    animation: gradientShift 8s ease infinite;
  }

  @keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  /* Threat Level Indicators */
  .threat-low {
    @apply border-green-400/30 bg-green-500/10 text-green-400;
  }

  .threat-medium {
    @apply border-yellow-400/30 bg-yellow-500/10 text-yellow-400;
  }

  .threat-high {
    @apply border-red-400/30 bg-red-500/10 text-red-400;
  }

  .threat-critical {
    @apply border-purple-400/30 bg-purple-500/10 text-purple-400;
  }

  .hover-glow {
    @apply transition-all duration-300;
  }

  .hover-glow:hover {
    box-shadow: 0 0 20px rgba(6, 182, 212, 0.3);
  }

  .hover-scale {
    @apply transition-transform duration-300 ease-out;
  }

  .hover-scale:hover {
    transform: scale(1.02);
  }

  /* Loading Animations */
  .pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  .bounce-slow {
    animation: bounce 2s infinite;
  }

  .float {
    animation: float 3s ease-in-out infinite;
  }

  @keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  .scan-line {
    position: relative;
    overflow: hidden;
  }

  .scan-line::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(6, 182, 212, 0.4), transparent);
    animation: scan 2s linear infinite;
  }

  @keyframes scan {
    0% { left: -100%; }
    100% { left: 100%; }
  }

  /* Hover effects */
  .hover-lift {
    @apply transition-transform duration-200 hover:scale-105;
  }

  .hover-glow {
    @apply transition-shadow duration-200 hover:shadow-cyan-500/25 hover:shadow-2xl;
  }

  /* Toggle switch */
  .toggle {
    @apply relative inline-flex h-6 w-11 items-center rounded-full
           bg-gray-600 transition-colors focus:outline-none focus:ring-2
           focus:ring-cyan-500 focus:ring-offset-2 focus:ring-offset-gray-900;
  }

  .toggle:checked {
    @apply bg-cyan-500;
  }

  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-cyan-500;
  }

  /* Error states */
  .error-input {
    @apply border-red-500 focus:ring-red-400;
  }

  .error-text {
    @apply text-red-400 text-sm mt-1;
  }

  /* Success states */
  .success-input {
    @apply border-green-500 focus:ring-green-400;
  }

  .success-text {
    @apply text-green-400 text-sm mt-1;
  }

  /* Card hover effects */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-xl;
  }

  .card-hover:hover {
    background: rgba(255, 255, 255, 0.15);
    border-color: rgba(255, 255, 255, 0.3);
    box-shadow: 0 25px 50px -12px rgba(6, 182, 212, 0.1);
  }

  /* Notification styles */
  .notification-success {
    @apply text-green-400;
    background: rgba(34, 197, 94, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.3);
  }

  .notification-error {
    @apply text-red-400;
    background: rgba(239, 68, 68, 0.2);
    border: 1px solid rgba(239, 68, 68, 0.3);
  }

  .notification-warning {
    @apply text-yellow-400;
    background: rgba(234, 179, 8, 0.2);
    border: 1px solid rgba(234, 179, 8, 0.3);
  }

  .notification-info {
    @apply text-blue-400;
    background: rgba(59, 130, 246, 0.2);
    border: 1px solid rgba(59, 130, 246, 0.3);
  }
}

@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-black text-white font-sans;
    font-family: 'Inter', 'Poppins', system-ui, -apple-system, sans-serif;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
    background: linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%);
    background-attachment: fixed;
  }

  * {
    border-color: rgba(255, 255, 255, 0.15);
  }

  /* Custom scrollbar with glassmorphism */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
  }

  ::-webkit-scrollbar-thumb {
    @apply rounded-full;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(34, 211, 238, 0.3);
  }

  /* Selection with glassmorphism */
  ::selection {
    background: rgba(34, 211, 238, 0.3);
    backdrop-filter: blur(10px);
  }
}

@layer components {
  /* Glassmorphism Base Classes */
  .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .glass-card {
    @apply glass rounded-2xl p-6;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .glass-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  }

  .glass-panel {
    @apply glass rounded-xl p-4;
  }

  .glass-nav {
    @apply glass rounded-2xl;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px);
  }

  /* Button variants with glassmorphism */
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-2xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-black;
    backdrop-filter: blur(10px);
  }

  .btn-primary {
    @apply btn text-white;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.9);
  }

  .btn-primary:hover {
    @apply text-cyan-400;
    background: rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 255, 255, 0.2);
    transform: scale(1.02);
  }

  .btn-secondary {
    @apply btn text-gray-300;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .btn-secondary:hover {
    @apply text-cyan-400;
    background: rgba(0, 255, 255, 0.05);
    border-color: rgba(0, 255, 255, 0.2);
  }

  .btn-ghost {
    @apply btn text-gray-300;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .btn-ghost:hover {
    @apply text-cyan-400;
    background: rgba(0, 255, 255, 0.05);
    border-color: rgba(0, 255, 255, 0.2);
  }
  
  /* Input styles with glassmorphism */
  .input {
    @apply w-full px-4 py-3 text-white placeholder-gray-400 focus:outline-none transition-all duration-300 rounded-2xl;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .input:focus {
    background: rgba(0, 255, 255, 0.05);
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow: 0 0 20px rgba(0, 255, 255, 0.1);
  }

  /* Enhanced Card styles with true glassmorphism */
  .card {
    @apply glass-card;
  }

  .card-hover {
    @apply glass-card;
  }

  .card-hover:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-4px);
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.5);
  }

  /* Enhanced Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #ffffff 0%, #00ffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .gradient-primary {
    background: linear-gradient(135deg, #00ffff 0%, #ffffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
  
  /* Loading spinner */
  .spinner {
    @apply animate-spin rounded-full border-2 border-gray-700 border-t-primary-500;
  }
  
  /* Severity indicators */
  .severity-critical {
    @apply text-red-400 bg-red-500 bg-opacity-10 border-red-500 border-opacity-20;
  }
  
  .severity-high {
    @apply text-orange-400 bg-orange-500 bg-opacity-10 border-orange-500 border-opacity-20;
  }
  
  .severity-medium {
    @apply text-yellow-400 bg-yellow-500 bg-opacity-10 border-yellow-500 border-opacity-20;
  }
  
  .severity-low {
    @apply text-green-400 bg-green-500 bg-opacity-10 border-green-500 border-opacity-20;
  }
  
  /* Code block */
  .code-block {
    @apply bg-gray-950 border border-gray-800 rounded-lg p-4 font-mono text-sm overflow-x-auto;
  }
  
  /* Navigation with glassmorphism */
  .nav-link {
    @apply px-4 py-2 text-gray-300 rounded-xl transition-all duration-300;
    background: transparent;
    border: 1px solid transparent;
  }

  .nav-link:hover {
    @apply text-cyan-400;
    background: rgba(0, 255, 255, 0.05);
    border-color: rgba(0, 255, 255, 0.2);
    backdrop-filter: blur(10px);
  }

  .nav-link-active {
    @apply text-cyan-400;
    background: rgba(0, 255, 255, 0.1);
    border: 1px solid rgba(0, 255, 255, 0.3);
    backdrop-filter: blur(10px);
  }
  
  /* Table styles */
  .table {
    @apply w-full border-collapse;
  }
  
  .table th {
    @apply px-4 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider border-b border-gray-800;
  }
  
  .table td {
    @apply px-4 py-3 text-sm text-gray-300 border-b border-gray-800;
  }
  
  .table tr:hover {
    @apply bg-gray-900 bg-opacity-50;
  }
  
  /* Progress bar */
  .progress-bar {
    @apply w-full bg-gray-800 rounded-full h-2 overflow-hidden;
  }
  
  .progress-fill {
    @apply h-full bg-gradient-to-r from-primary-500 to-primary-400 transition-all duration-300 ease-out;
  }
  
  /* Badge */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-500 bg-opacity-10 text-primary-400 border border-primary-500 border-opacity-20;
  }
  
  .badge-gray {
    @apply badge bg-gray-500 bg-opacity-10 text-gray-400 border border-gray-500 border-opacity-20;
  }
}

@layer utilities {
  /* Glassmorphism Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-slide-up {
    animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-slide-down {
    animation: slideDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .animate-glass-appear {
    animation: glassAppear 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Text utilities */
  .text-balance {
    text-wrap: balance;
  }

  /* Layout utilities */
  .safe-area-inset {
    padding-left: env(safe-area-inset-left);
    padding-right: env(safe-area-inset-right);
  }

  /* Enhanced Focus utilities with glassmorphism */
  .focus-ring {
    @apply focus:outline-none transition-all duration-300;
  }

  .focus-ring:focus {
    box-shadow: 0 0 0 2px rgba(0, 255, 255, 0.3);
    background: rgba(0, 255, 255, 0.05);
  }

  /* Enhanced Hover effects */
  .hover-lift {
    @apply transition-all duration-300;
  }

  .hover-lift:hover {
    transform: translateY(-4px) scale(1.02);
  }

  .hover-glow {
    @apply transition-all duration-300;
  }

  .hover-glow:hover {
    box-shadow: 0 8px 32px rgba(0, 255, 255, 0.2);
  }

  /* Glassmorphism specific utilities */
  .glass-blur-sm {
    backdrop-filter: blur(8px);
  }

  .glass-blur-md {
    backdrop-filter: blur(16px);
  }

  .glass-blur-lg {
    backdrop-filter: blur(24px);
  }

  .glass-border {
    border: 1px solid rgba(255, 255, 255, 0.15);
  }

  .glass-shadow {
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .glass-shadow-lg {
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  }
}

/* Enhanced Keyframes for glassmorphism */
@keyframes fadeIn {
  from {
    opacity: 0;
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    backdrop-filter: blur(20px);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    backdrop-filter: blur(20px);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
    backdrop-filter: blur(0px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
    backdrop-filter: blur(20px);
  }
}

@keyframes glassAppear {
  from {
    opacity: 0;
    transform: scale(0.95);
    backdrop-filter: blur(0px);
    background: rgba(255, 255, 255, 0);
  }
  to {
    opacity: 1;
    transform: scale(1);
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.05);
  }
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .glass-card {
    @apply p-4 rounded-xl;
    background: rgba(255, 255, 255, 0.08);
  }

  .glass-panel {
    background: rgba(255, 255, 255, 0.06);
  }
}

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: rgba(6, 182, 212, 0.5);
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: rgba(6, 182, 212, 0.7);
}

/* Status Indicators */
.status-online {
  @apply w-2 h-2 bg-green-400 rounded-full animate-pulse;
}

.status-offline {
  @apply w-2 h-2 bg-gray-500 rounded-full;
}

.status-warning {
  @apply w-2 h-2 bg-yellow-400 rounded-full animate-pulse;
}

.status-error {
  @apply w-2 h-2 bg-red-400 rounded-full animate-pulse;
}
