<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Complete Scan Workflow Test - ByteGuardX</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #0f172a;
            color: #e2e8f0;
        }
        .container {
            background: #1e293b;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
        }
        h1 {
            color: #00bcd4;
            text-align: center;
            margin-bottom: 30px;
        }
        h2 {
            color: #00bcd4;
            border-bottom: 2px solid #00bcd4;
            padding-bottom: 10px;
        }
        button {
            background: #00bcd4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
        }
        button:hover {
            background: #0891b2;
        }
        button:disabled {
            background: #475569;
            cursor: not-allowed;
        }
        .response {
            background: #0f172a;
            padding: 15px;
            border-radius: 6px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 11px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #475569;
        }
        .success {
            border-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }
        .error {
            border-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        .warning {
            border-color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
        }
        input[type="file"] {
            margin: 10px 0;
            padding: 8px;
            background: #334155;
            color: #e2e8f0;
            border: 1px solid #475569;
            border-radius: 4px;
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            background: #334155;
            border-radius: 8px;
            border-left: 4px solid #00bcd4;
        }
        .step-number {
            display: inline-block;
            background: #00bcd4;
            color: white;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            text-align: center;
            line-height: 24px;
            margin-right: 10px;
            font-weight: bold;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-ok { background: #10b981; }
        .status-error { background: #ef4444; }
        .status-warning { background: #f59e0b; }
        .status-pending { background: #6b7280; }
    </style>
</head>
<body>
    <h1>🔍 Complete Scan Workflow Test</h1>
    
    <div class="container">
        <h2>📋 Test Workflow</h2>
        <p>This test simulates the complete file scanning workflow that the frontend uses:</p>
        
        <div class="step">
            <span class="step-number">1</span>
            <strong>Upload File</strong> - Upload file to get scan ID
            <div id="step1-status">
                <span class="status-indicator status-pending"></span>
                Not started
            </div>
        </div>
        
        <div class="step">
            <span class="step-number">2</span>
            <strong>Comprehensive Scan</strong> - Run all scan types using scan ID
            <div id="step2-status">
                <span class="status-indicator status-pending"></span>
                Waiting for step 1
            </div>
        </div>
        
        <div class="step">
            <span class="step-number">3</span>
            <strong>Individual Scans</strong> - Test secrets, dependencies, AI patterns
            <div id="step3-status">
                <span class="status-indicator status-pending"></span>
                Waiting for step 2
            </div>
        </div>
        
        <div class="step">
            <span class="step-number">4</span>
            <strong>Results Retrieval</strong> - Get scan results by ID
            <div id="step4-status">
                <span class="status-indicator status-pending"></span>
                Waiting for step 3
            </div>
        </div>
    </div>

    <div class="container">
        <h2>🧪 Test Controls</h2>
        
        <div>
            <input type="file" id="test-file" accept="*/*">
            <br>
            <button onclick="runCompleteWorkflow()" id="run-btn">🚀 Run Complete Workflow</button>
            <button onclick="clearLogs()">🗑️ Clear Logs</button>
        </div>
        
        <div id="workflow-response" class="response" style="display: none;"></div>
    </div>

    <script>
        let currentScanId = null;

        function updateStepStatus(stepId, status, message) {
            const element = document.getElementById(stepId);
            const indicator = element.querySelector('.status-indicator');
            indicator.className = `status-indicator status-${status}`;
            
            const textNode = element.childNodes[element.childNodes.length - 1];
            if (textNode && textNode.nodeType === Node.TEXT_NODE) {
                textNode.textContent = message;
            }
        }

        function log(message, type = 'info') {
            const responseDiv = document.getElementById('workflow-response');
            responseDiv.style.display = 'block';
            responseDiv.className = `response ${type}`;
            responseDiv.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            responseDiv.scrollTop = responseDiv.scrollHeight;
        }

        function clearLogs() {
            const responseDiv = document.getElementById('workflow-response');
            responseDiv.textContent = '';
            responseDiv.className = 'response';
            
            // Reset all step statuses
            updateStepStatus('step1-status', 'pending', 'Not started');
            updateStepStatus('step2-status', 'pending', 'Waiting for step 1');
            updateStepStatus('step3-status', 'pending', 'Waiting for step 2');
            updateStepStatus('step4-status', 'pending', 'Waiting for step 3');
            
            currentScanId = null;
        }

        async function runCompleteWorkflow() {
            const fileInput = document.getElementById('test-file');
            const runBtn = document.getElementById('run-btn');
            
            if (!fileInput.files[0]) {
                log('❌ Please select a file first', 'error');
                return;
            }
            
            runBtn.disabled = true;
            runBtn.textContent = '🔄 Running...';
            clearLogs();
            
            try {
                // Step 1: Upload File
                log('🚀 Starting complete scan workflow...');
                log('\n📤 STEP 1: Uploading file...');
                updateStepStatus('step1-status', 'warning', 'Uploading...');
                
                const formData = new FormData();
                formData.append('file', fileInput.files[0]);
                
                const uploadResponse = await fetch('/scan/upload', {
                    method: 'POST',
                    body: formData,
                    credentials: 'include'
                });
                
                if (!uploadResponse.ok) {
                    throw new Error(`Upload failed: ${uploadResponse.status}`);
                }
                
                const uploadData = await uploadResponse.json();
                currentScanId = uploadData.scan_id;
                
                log(`✅ File uploaded successfully!`);
                log(`📋 Scan ID: ${currentScanId}`);
                log(`📊 Upload response: ${JSON.stringify(uploadData, null, 2)}`);
                updateStepStatus('step1-status', 'ok', 'Upload successful');
                
                // Step 2: Comprehensive Scan
                log('\n🔍 STEP 2: Running comprehensive scan...');
                updateStepStatus('step2-status', 'warning', 'Scanning...');
                
                const scanAllResponse = await fetch('/scan/all', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ scan_id: currentScanId }),
                    credentials: 'include'
                });
                
                if (!scanAllResponse.ok) {
                    throw new Error(`Comprehensive scan failed: ${scanAllResponse.status}`);
                }
                
                const scanAllData = await scanAllResponse.json();
                
                log(`✅ Comprehensive scan completed!`);
                log(`🔍 Enhanced findings: ${scanAllData.enhanced_findings?.length || 0}`);
                log(`📊 Comprehensive scan response: ${JSON.stringify(scanAllData, null, 2)}`);
                updateStepStatus('step2-status', 'ok', 'Comprehensive scan complete');
                
                // Step 3: Individual Scans
                log('\n🎯 STEP 3: Running individual scans...');
                updateStepStatus('step3-status', 'warning', 'Running individual scans...');
                
                const scanTypes = [
                    { endpoint: '/scan/secrets', name: 'Secrets Scan' },
                    { endpoint: '/scan/dependencies', name: 'Dependencies Scan' },
                    { endpoint: '/scan/ai-patterns', name: 'AI Patterns Scan' }
                ];
                
                for (const scanType of scanTypes) {
                    log(`\n  🔍 Running ${scanType.name}...`);
                    
                    const response = await fetch(scanType.endpoint, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ scan_id: currentScanId }),
                        credentials: 'include'
                    });
                    
                    if (response.ok) {
                        const data = await response.json();
                        log(`  ✅ ${scanType.name} completed - ${data.findings?.length || 0} findings`);
                    } else {
                        log(`  ❌ ${scanType.name} failed: ${response.status}`);
                    }
                }
                
                updateStepStatus('step3-status', 'ok', 'Individual scans complete');
                
                // Step 4: Results Retrieval
                log('\n📊 STEP 4: Retrieving scan results...');
                updateStepStatus('step4-status', 'warning', 'Retrieving results...');
                
                const resultsResponse = await fetch(`/scan/results/${currentScanId}`, {
                    method: 'GET',
                    credentials: 'include'
                });
                
                if (resultsResponse.ok) {
                    const resultsData = await resultsResponse.json();
                    log(`✅ Results retrieved successfully!`);
                    log(`📊 Final results: ${JSON.stringify(resultsData, null, 2)}`);
                    updateStepStatus('step4-status', 'ok', 'Results retrieved');
                } else {
                    log(`❌ Results retrieval failed: ${resultsResponse.status}`);
                    updateStepStatus('step4-status', 'error', 'Results retrieval failed');
                }
                
                log('\n🎉 WORKFLOW COMPLETED SUCCESSFULLY!');
                log(`\n📋 Summary:`);
                log(`   • File uploaded: ${fileInput.files[0].name}`);
                log(`   • Scan ID: ${currentScanId}`);
                log(`   • All scan types completed`);
                log(`   • Results retrieved successfully`);
                
                document.getElementById('workflow-response').className = 'response success';
                
            } catch (error) {
                log(`\n❌ WORKFLOW FAILED: ${error.message}`, 'error');
                
                // Update failed step status
                if (!currentScanId) {
                    updateStepStatus('step1-status', 'error', 'Upload failed');
                } else {
                    updateStepStatus('step2-status', 'error', 'Scan failed');
                }
                
                document.getElementById('workflow-response').className = 'response error';
            } finally {
                runBtn.disabled = false;
                runBtn.textContent = '🚀 Run Complete Workflow';
            }
        }

        // Initialize
        window.addEventListener('load', () => {
            log('🛡️ ByteGuardX Complete Scan Workflow Test Ready');
            log('📝 Select a file and click "Run Complete Workflow" to test the full scanning process');
        });
    </script>
</body>
</html>
