<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSRF Token Test - ByteGuardX</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #0f172a;
            color: #e2e8f0;
        }
        .container {
            background: #1e293b;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
        }
        h1 {
            color: #00bcd4;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            background: #334155;
            border-radius: 8px;
            border-left: 4px solid #00bcd4;
        }
        .status {
            display: inline-block;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.success { background: #10b981; color: white; }
        .status.error { background: #ef4444; color: white; }
        .status.loading { background: #f59e0b; color: white; }
        button {
            background: #00bcd4;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0891b2; }
        .response {
            background: #0f172a;
            padding: 10px;
            border-radius: 4px;
            margin-top: 10px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #e2e8f0;
        }
        input, textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #475569;
            border-radius: 4px;
            background: #1e293b;
            color: #e2e8f0;
            box-sizing: border-box;
        }
        input:focus, textarea:focus {
            outline: none;
            border-color: #00bcd4;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ CSRF Token Test</h1>
        
        <div class="test-section">
            <h3>CSRF Token Status <span id="token-status" class="status loading">Checking...</span></h3>
            <p>Current CSRF token: <code id="current-token">Loading...</code></p>
            <button onclick="fetchCSRFToken()">Fetch New Token</button>
            <button onclick="checkTokenFromCookie()">Check Cookie Token</button>
            <div id="token-response" class="response"></div>
        </div>

        <div class="test-section">
            <h3>Test Signup Form</h3>
            <form id="signup-form">
                <div class="form-group">
                    <label for="username">Username:</label>
                    <input type="text" id="username" name="username" value="testuser" required>
                </div>
                <div class="form-group">
                    <label for="email">Email:</label>
                    <input type="email" id="email" name="email" value="<EMAIL>" required>
                </div>
                <div class="form-group">
                    <label for="password">Password:</label>
                    <input type="password" id="password" name="password" value="testpass123" required>
                </div>
                <button type="submit">Test Signup</button>
            </form>
            <div id="signup-response" class="response"></div>
        </div>

        <div class="test-section">
            <h3>Manual API Test</h3>
            <button onclick="testAPIWithToken()">Test API with Token</button>
            <button onclick="testAPIWithoutToken()">Test API without Token</button>
            <div id="api-response" class="response"></div>
        </div>
    </div>

    <script>
        let currentToken = null;

        // Utility function to get cookie value
        function getCookie(name) {
            const cookies = document.cookie.split(';');
            for (let cookie of cookies) {
                const [cookieName, cookieValue] = cookie.trim().split('=');
                if (cookieName === name) {
                    return decodeURIComponent(cookieValue);
                }
            }
            return null;
        }

        // Fetch CSRF token from server
        async function fetchCSRFToken() {
            const statusEl = document.getElementById('token-status');
            const tokenEl = document.getElementById('current-token');
            const responseEl = document.getElementById('token-response');
            
            statusEl.textContent = 'Fetching...';
            statusEl.className = 'status loading';
            
            try {
                const response = await fetch('/api/csrf-token', {
                    method: 'GET',
                    credentials: 'include',
                    headers: {
                        'Accept': 'application/json',
                    }
                });
                
                if (response.ok) {
                    const data = await response.json();
                    currentToken = data.csrf_token;
                    
                    statusEl.textContent = 'Success';
                    statusEl.className = 'status success';
                    tokenEl.textContent = currentToken;
                    responseEl.textContent = `Token fetched successfully!\nLength: ${currentToken.length}\nCookie set: ${getCookie('csrf_token') ? 'Yes' : 'No'}`;
                } else {
                    const errorData = await response.text();
                    statusEl.textContent = 'Error';
                    statusEl.className = 'status error';
                    responseEl.textContent = `Error ${response.status}: ${errorData}`;
                }
            } catch (error) {
                statusEl.textContent = 'Error';
                statusEl.className = 'status error';
                responseEl.textContent = `Network error: ${error.message}`;
            }
        }

        // Check token from cookie
        function checkTokenFromCookie() {
            const responseEl = document.getElementById('token-response');
            const cookieToken = getCookie('csrf_token');
            
            if (cookieToken) {
                responseEl.textContent = `Cookie token found!\nToken: ${cookieToken}\nLength: ${cookieToken.length}\nMatches current: ${cookieToken === currentToken ? 'Yes' : 'No'}`;
            } else {
                responseEl.textContent = 'No CSRF token found in cookies';
            }
        }

        // Test signup form
        document.getElementById('signup-form').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const responseEl = document.getElementById('signup-response');
            responseEl.textContent = 'Submitting...';
            
            // Get form data
            const formData = new FormData(e.target);
            
            // Add CSRF token
            if (currentToken) {
                formData.append('csrf_token', currentToken);
            }
            
            try {
                const response = await fetch('/api/auth/register', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'X-CSRFToken': currentToken || '',
                        'X-CSRF-Token': currentToken || ''
                    },
                    body: formData
                });
                
                const responseData = await response.text();
                responseEl.textContent = `Response ${response.status}:\n${responseData}`;
                
                if (response.status === 200 || response.status === 201) {
                    responseEl.style.color = '#10b981';
                } else if (response.status === 400 && responseData.includes('CSRF')) {
                    responseEl.style.color = '#ef4444';
                    responseEl.textContent += '\n\nCSRF validation failed! Try fetching a new token first.';
                } else {
                    responseEl.style.color = '#f59e0b';
                }
            } catch (error) {
                responseEl.textContent = `Network error: ${error.message}`;
                responseEl.style.color = '#ef4444';
            }
        });

        // Test API with token
        async function testAPIWithToken() {
            const responseEl = document.getElementById('api-response');
            responseEl.textContent = 'Testing with token...';
            
            if (!currentToken) {
                responseEl.textContent = 'No token available. Fetch token first.';
                return;
            }
            
            try {
                const response = await fetch('/api/v1/health', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': currentToken,
                        'X-CSRF-Token': currentToken
                    },
                    body: JSON.stringify({ test: 'data', csrf_token: currentToken })
                });
                
                const data = await response.text();
                responseEl.textContent = `With Token - Status ${response.status}:\n${data}`;
            } catch (error) {
                responseEl.textContent = `With Token - Error: ${error.message}`;
            }
        }

        // Test API without token
        async function testAPIWithoutToken() {
            const responseEl = document.getElementById('api-response');
            responseEl.textContent = 'Testing without token...';
            
            try {
                const response = await fetch('/api/v1/health', {
                    method: 'POST',
                    credentials: 'include',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ test: 'data' })
                });
                
                const data = await response.text();
                responseEl.textContent = `Without Token - Status ${response.status}:\n${data}`;
            } catch (error) {
                responseEl.textContent = `Without Token - Error: ${error.message}`;
            }
        }

        // Auto-fetch token on page load
        window.onload = function() {
            setTimeout(fetchCSRFToken, 500);
        };
    </script>
</body>
</html>
