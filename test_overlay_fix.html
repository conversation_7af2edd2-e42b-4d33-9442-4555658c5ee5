<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ByteGuardX Overlay Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .status-box {
            padding: 20px;
            margin: 15px 0;
            border-radius: 10px;
            border-left: 5px solid;
        }
        .success {
            background: rgba(40, 167, 69, 0.2);
            border-color: #28a745;
        }
        .error {
            background: rgba(220, 53, 69, 0.2);
            border-color: #dc3545;
        }
        .warning {
            background: rgba(255, 193, 7, 0.2);
            border-color: #ffc107;
            color: #212529;
        }
        .info {
            background: rgba(23, 162, 184, 0.2);
            border-color: #17a2b8;
        }
        button {
            background: linear-gradient(45deg, #00bcd4, #0097a7);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0, 188, 212, 0.4);
        }
        .test-result {
            margin: 20px 0;
            padding: 15px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.05);
        }
        .iframe-container {
            margin: 20px 0;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }
        iframe {
            width: 100%;
            height: 400px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ ByteGuardX Black Overlay Fix Test</h1>
        
        <div class="status-box info">
            <h3>✅ Fixes Applied:</h3>
            <ul>
                <li>Disabled ForceBlackTheme React component</li>
                <li>Removed aggressive CSS imports (theme-override.css, force-black-theme.css)</li>
                <li>Removed !important CSS rules causing overlay</li>
                <li>Restarted frontend server with clean state</li>
            </ul>
        </div>

        <div class="test-result">
            <h3>🧪 Test Your ByteGuardX Application:</h3>
            <p>Click the button below to open ByteGuardX in a new tab:</p>
            <button onclick="openByteguardx()">🌐 Open ByteGuardX (New Tab)</button>
            <button onclick="openByteguardxIncognito()">🕵️ Open in Incognito Mode</button>
        </div>

        <div class="test-result">
            <h3>🔍 Quick Preview (Embedded):</h3>
            <p>This shows ByteGuardX embedded below. If you see content (not just black), the overlay is fixed:</p>
            <div class="iframe-container">
                <iframe src="http://localhost:3000" title="ByteGuardX Preview"></iframe>
            </div>
        </div>

        <div class="test-result">
            <h3>🛠️ If You Still See Black Overlay:</h3>
            <div class="status-box warning">
                <strong>Browser Cache Issue:</strong>
                <ol>
                    <li>Press <code>Ctrl+Shift+R</code> (hard refresh)</li>
                    <li>Or clear all browser data for localhost</li>
                    <li>Try incognito/private browsing mode</li>
                </ol>
            </div>
            
            <div class="status-box error">
                <strong>Persistent Overlay:</strong>
                <ol>
                    <li>Press <code>F12</code> to open Developer Tools</li>
                    <li>Go to Console tab - look for errors</li>
                    <li>Go to Elements tab - look for overlay elements</li>
                    <li>Try pressing <code>Escape</code> key multiple times</li>
                </ol>
            </div>
        </div>

        <div class="test-result">
            <h3>📊 Server Status Check:</h3>
            <button onclick="checkServers()">🔄 Check Server Status</button>
            <div id="server-status"></div>
        </div>

        <div class="status-box success">
            <h3>✅ Expected Result:</h3>
            <p>You should now see:</p>
            <ul>
                <li>ByteGuardX interface with black background</li>
                <li>White text and cyan accents</li>
                <li>Working navigation and buttons</li>
                <li>NO black overlay covering everything</li>
            </ul>
        </div>
    </div>

    <script>
        function openByteguardx() {
            window.open('http://localhost:3000', '_blank');
        }

        function openByteguardxIncognito() {
            alert('To open in incognito mode:\n\n1. Copy this URL: http://localhost:3000\n2. Open a new incognito/private window\n3. Paste the URL and press Enter');
        }

        function checkServers() {
            const statusDiv = document.getElementById('server-status');
            statusDiv.innerHTML = '<p>Checking servers...</p>';
            
            let results = '';
            
            // Check frontend
            fetch('http://localhost:3000')
                .then(response => {
                    if (response.ok) {
                        results += '<div class="status-box success">✅ Frontend: Running on http://localhost:3000</div>';
                    } else {
                        results += '<div class="status-box error">❌ Frontend: Error response</div>';
                    }
                    statusDiv.innerHTML = results;
                })
                .catch(() => {
                    results += '<div class="status-box error">❌ Frontend: Not accessible</div>';
                    statusDiv.innerHTML = results;
                });

            // Check backend
            fetch('http://localhost:5000/api/health')
                .then(response => response.json())
                .then(data => {
                    results += `<div class="status-box success">✅ Backend: ${data.service || 'Running'} on http://localhost:5000</div>`;
                    statusDiv.innerHTML = results;
                })
                .catch(() => {
                    results += '<div class="status-box error">❌ Backend: Not accessible</div>';
                    statusDiv.innerHTML = results;
                });
        }

        // Auto-check servers on load
        window.onload = function() {
            setTimeout(checkServers, 1000);
        };
    </script>
</body>
</html>
