{"name": "byteguardx-mobile", "version": "1.0.0", "description": "ByteGuardX Mobile App - Security Scanner on the go", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios", "test": "jest", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "type-check": "tsc --noEmit"}, "dependencies": {"expo": "~49.0.0", "expo-status-bar": "~1.6.0", "react": "18.2.0", "react-native": "0.72.6", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@react-navigation/bottom-tabs": "^6.5.8", "react-native-gesture-handler": "~2.12.0", "react-native-reanimated": "~3.3.0", "expo-document-picker": "~11.5.4", "expo-file-system": "~15.4.4", "expo-sharing": "~11.5.0", "expo-notifications": "~0.20.1", "expo-secure-store": "~12.3.1", "expo-camera": "~13.4.4", "expo-barcode-scanner": "~12.5.3", "axios": "^1.5.0", "react-native-paper": "^5.10.6", "react-native-vector-icons": "^10.0.0", "react-native-chart-kit": "^6.12.0", "react-native-svg": "13.9.0", "react-native-async-storage": "^1.19.3", "react-native-keychain": "^8.1.3", "react-native-biometrics": "^3.0.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.44.0", "eslint-config-expo": "^7.0.0", "jest": "^29.2.1", "typescript": "^5.1.3"}, "keywords": ["security", "mobile", "scanner", "react-native", "expo"], "author": "ByteGuardX Team", "license": "MIT", "private": true}