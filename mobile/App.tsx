import React, { useEffect, useState } from 'react';
import {
  StatusBar,
  StyleSheet,
  View,
  Text,
  Alert,
  AppState,
  AppStateStatus,
} from 'react-native';
import { NavigationContainer } from '@react-navigation/native';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { createStackNavigator } from '@react-navigation/stack';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import LinearGradient from 'react-native-linear-gradient';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import Toast from 'react-native-toast-message';
import DeviceInfo from 'react-native-device-info';

// Screens
import DashboardScreen from './src/screens/DashboardScreen';
import ScannerScreen from './src/screens/ScannerScreen';
import ReportsScreen from './src/screens/ReportsScreen';
import SettingsScreen from './src/screens/SettingsScreen';
import ScanResultScreen from './src/screens/ScanResultScreen';
import PluginsScreen from './src/screens/PluginsScreen';

// Services
import { ApiService } from './src/services/ApiService';
import { StorageService } from './src/services/StorageService';
import { NotificationService } from './src/services/NotificationService';

// Types
export type RootStackParamList = {
  MainTabs: undefined;
  ScanResult: { scanId: string };
  Plugins: undefined;
};

export type TabParamList = {
  Dashboard: undefined;
  Scanner: undefined;
  Reports: undefined;
  Settings: undefined;
};

const Tab = createBottomTabNavigator<TabParamList>();
const Stack = createStackNavigator<RootStackParamList>();

const TabNavigator = () => {
  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: string;

          switch (route.name) {
            case 'Dashboard':
              iconName = focused ? 'view-dashboard' : 'view-dashboard-outline';
              break;
            case 'Scanner':
              iconName = focused ? 'shield-search' : 'shield-search-outline';
              break;
            case 'Reports':
              iconName = focused ? 'file-document' : 'file-document-outline';
              break;
            case 'Settings':
              iconName = focused ? 'cog' : 'cog-outline';
              break;
            default:
              iconName = 'help-circle';
          }

          return <Icon name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: '#00bcd4',
        tabBarInactiveTintColor: '#666666',
        tabBarStyle: {
          backgroundColor: '#000000',
          borderTopColor: '#333333',
          borderTopWidth: 1,
          paddingBottom: 5,
          paddingTop: 5,
          height: 60,
        },
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
        },
        headerShown: false,
      })}
    >
      <Tab.Screen name="Dashboard" component={DashboardScreen} />
      <Tab.Screen name="Scanner" component={ScannerScreen} />
      <Tab.Screen name="Reports" component={ReportsScreen} />
      <Tab.Screen name="Settings" component={SettingsScreen} />
    </Tab.Navigator>
  );
};

const App: React.FC = () => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [appState, setAppState] = useState<AppStateStatus>(AppState.currentState);

  useEffect(() => {
    initializeApp();
    
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
    };
  }, []);

  const initializeApp = async () => {
    try {
      // Initialize services
      await StorageService.initialize();
      await NotificationService.initialize();
      
      // Check device compatibility
      const deviceInfo = await DeviceInfo.getDeviceId();
      console.log('Device ID:', deviceInfo);
      
      // Initialize API service
      const apiEndpoint = await StorageService.getItem('apiEndpoint') || 'http://*************:5000';
      ApiService.initialize(apiEndpoint);
      
      // Check API connectivity
      try {
        await ApiService.healthCheck();
        console.log('API connection successful');
      } catch (error) {
        console.warn('API connection failed:', error);
        Alert.alert(
          'Connection Warning',
          'Unable to connect to ByteGuardX server. Some features may be limited.',
          [{ text: 'OK' }]
        );
      }
      
      setIsInitialized(true);
    } catch (error) {
      console.error('App initialization failed:', error);
      Alert.alert(
        'Initialization Error',
        'Failed to initialize the app. Please restart.',
        [{ text: 'OK' }]
      );
    }
  };

  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (appState.match(/inactive|background/) && nextAppState === 'active') {
      // App has come to the foreground
      console.log('App has come to the foreground');
      // Refresh data or check for updates
    }
    setAppState(nextAppState);
  };

  if (!isInitialized) {
    return (
      <SafeAreaProvider>
        <LinearGradient
          colors={['#000000', '#1a1a1a', '#000000']}
          style={styles.loadingContainer}
        >
          <StatusBar barStyle="light-content" backgroundColor="#000000" />
          <Icon name="shield-search" size={80} color="#00bcd4" />
          <Text style={styles.loadingTitle}>ByteGuardX</Text>
          <Text style={styles.loadingSubtitle}>AI-Powered Security Scanner</Text>
          <View style={styles.loadingIndicator}>
            <Icon name="loading" size={24} color="#00bcd4" />
            <Text style={styles.loadingText}>Initializing...</Text>
          </View>
        </LinearGradient>
      </SafeAreaProvider>
    );
  }

  return (
    <SafeAreaProvider>
      <NavigationContainer
        theme={{
          dark: true,
          colors: {
            primary: '#00bcd4',
            background: '#000000',
            card: '#1a1a1a',
            text: '#ffffff',
            border: '#333333',
            notification: '#ff4444',
          },
        }}
      >
        <StatusBar barStyle="light-content" backgroundColor="#000000" />
        <Stack.Navigator
          screenOptions={{
            headerStyle: {
              backgroundColor: '#000000',
              borderBottomColor: '#333333',
              borderBottomWidth: 1,
            },
            headerTintColor: '#ffffff',
            headerTitleStyle: {
              fontWeight: 'bold',
              fontSize: 18,
            },
            cardStyle: {
              backgroundColor: '#000000',
            },
          }}
        >
          <Stack.Screen
            name="MainTabs"
            component={TabNavigator}
            options={{ headerShown: false }}
          />
          <Stack.Screen
            name="ScanResult"
            component={ScanResultScreen}
            options={{
              title: 'Scan Results',
              headerBackTitleVisible: false,
            }}
          />
          <Stack.Screen
            name="Plugins"
            component={PluginsScreen}
            options={{
              title: 'Security Plugins',
              headerBackTitleVisible: false,
            }}
          />
        </Stack.Navigator>
        <Toast />
      </NavigationContainer>
    </SafeAreaProvider>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  loadingTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#ffffff',
    marginTop: 20,
    marginBottom: 8,
  },
  loadingSubtitle: {
    fontSize: 16,
    color: '#cccccc',
    textAlign: 'center',
    marginBottom: 40,
  },
  loadingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#00bcd4',
    marginLeft: 12,
    fontWeight: '600',
  },
});

export default App;
