@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700;800;900&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Space+Grotesk:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    border-color: rgba(255, 255, 255, 0.15);
  }

  body {
    @apply bg-black text-white font-sans;
    font-family: 'Inter', 'Poppins', system-ui, -apple-system, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
    background: linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%);
    background-attachment: fixed;
  }

  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar with glassmorphism */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
  }

  ::-webkit-scrollbar-thumb {
    @apply rounded-full;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: rgba(34, 211, 238, 0.3);
  }

  /* Selection with glassmorphism */
  ::selection {
    background: rgba(34, 211, 238, 0.3);
    backdrop-filter: blur(10px);
  }
}

@layer components {
  /* Glassmorphism Base Classes */
  .glass {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .glass-card {
    @apply glass rounded-2xl p-6;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .glass-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-2px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.4);
  }

  .glass-panel {
    @apply glass rounded-xl p-4;
  }

  .glass-nav {
    @apply glass rounded-2xl;
    background: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(20px);
  }

  /* Gradient text */
  .gradient-text {
    background: linear-gradient(135deg, #0ea5e9 0%, #06b6d4 50%, #0ea5e9 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    background-size: 200% 200%;
    animation: gradient-shift 3s ease-in-out infinite;
  }

  @keyframes gradient-shift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  /* Button variants with glassmorphism */
  .btn-primary {
    @apply inline-flex items-center justify-center px-8 py-4 text-sm font-medium rounded-2xl transition-all duration-300 focus:outline-none;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.15);
    color: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
  }

  .btn-primary:hover {
    @apply text-cyan-400;
    background: rgba(0, 255, 255, 0.1);
    border-color: rgba(0, 255, 255, 0.3);
    box-shadow: 0 8px 32px rgba(0, 255, 255, 0.2);
    transform: scale(1.02);
  }

  .btn-secondary {
    @apply inline-flex items-center justify-center px-8 py-4 text-sm font-medium rounded-2xl transition-all duration-300 focus:outline-none;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
  }

  .btn-secondary:hover {
    @apply text-cyan-400;
    background: rgba(0, 255, 255, 0.05);
    border-color: rgba(0, 255, 255, 0.2);
  }

  .btn-outline {
    @apply inline-flex items-center justify-center px-8 py-4 text-sm font-medium rounded-2xl transition-all duration-300 focus:outline-none;
    background: transparent;
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
  }

  .btn-outline:hover {
    @apply text-cyan-400;
    background: rgba(0, 255, 255, 0.05);
    border-color: rgba(0, 255, 255, 0.3);
  }

  .gradient-text {
    background: linear-gradient(135deg, #ffffff 0%, #00ffff 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .card {
    @apply glass-card;
  }

  .card-hover {
    @apply glass-card hover-lift;
  }

  /* Enhanced hover effects */
  .hover-lift {
    @apply transition-all duration-300;
  }

  .hover-lift:hover {
    transform: translateY(-4px) scale(1.02);
  }

  .hover-glow {
    @apply transition-all duration-300;
  }

  .hover-glow:hover {
    box-shadow: 0 8px 32px rgba(0, 255, 255, 0.2);
  }

  .glow-effect {
    @apply hover-glow;
  }

  .section-padding {
    @apply py-16 sm:py-20 lg:py-24;
  }

  .container-padding {
    @apply px-4 sm:px-6 lg:px-8;
  }

  .text-balance {
    text-wrap: balance;
  }



  /* Enhanced Floating Animations */
  .floating-element {
    animation: float 6s ease-in-out infinite;
  }

  .floating-element:nth-child(2) {
    animation-delay: -2s;
  }

  .floating-element:nth-child(3) {
    animation-delay: -4s;
  }

  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    33% {
      transform: translateY(-10px) rotate(1deg);
    }
    66% {
      transform: translateY(5px) rotate(-1deg);
    }
  }

  /* Enhanced Button Animations */
  .btn-primary, .btn-secondary, .btn-outline {
    position: relative;
    overflow: hidden;
  }

  .btn-primary::before, .btn-secondary::before, .btn-outline::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
  }

  .btn-primary:hover::before, .btn-secondary:hover::before, .btn-outline:hover::before {
    left: 100%;
  }

  /* Enhanced Gradient Text Animation */
  .gradient-text-animated {
    background: linear-gradient(45deg, #0ea5e9, #06b6d4, #0ea5e9, #06b6d4);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient-flow 3s ease-in-out infinite;
  }

  @keyframes gradient-flow {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }

  /* Enhanced Glass Effects */
  .glass-enhanced {
    background: rgba(255, 255, 255, 0.03);
    backdrop-filter: blur(25px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      inset 0 -1px 0 rgba(255, 255, 255, 0.05);
  }

  .glass-enhanced:hover {
    background: rgba(255, 255, 255, 0.05);
    border-color: rgba(0, 255, 255, 0.2);
    box-shadow:
      0 12px 40px rgba(0, 0, 0, 0.4),
      0 0 20px rgba(0, 255, 255, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.15);
  }
}

@layer utilities {
  .animation-delay-200 {
    animation-delay: 200ms;
  }
  
  .animation-delay-400 {
    animation-delay: 400ms;
  }
  
  .animation-delay-600 {
    animation-delay: 600ms;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-900;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-700 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-600;
}

/* Selection */
::selection {
  @apply bg-primary-500 text-white;
}

/* Focus styles */
.focus-visible {
  @apply outline-none ring-2 ring-primary-500 ring-offset-2 ring-offset-black;
}
