{"name": "byteguardx-portal", "private": true, "version": "1.0.0", "type": "module", "description": "Official ByteGuardX download and marketing portal", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:secure": "tsc && vite build --config vite.config.security.ts", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:security": "eslint src/ --config .eslintrc.security.cjs --ext .js,.jsx,.ts,.tsx", "security:lint": "eslint src/ --config .eslintrc.security.cjs --ext .js,.jsx,.ts,.tsx", "preview": "vite preview", "preview:secure": "vite preview --config vite.config.security.ts", "security:audit": "npm audit --audit-level=moderate", "security:test": "npm run security:audit && npm run lint:security", "security:validate": "node scripts/validate-security.cjs", "security:report": "npm run security:audit --json > security-audit.json", "i18n:extract": "i18next-scanner --config i18next-scanner.config.js", "analyze": "npm run build && npx vite-bundle-analyzer dist", "perf:enable": "echo 'true' > .env.local && echo 'VITE_SHOW_PERF=true' >> .env.local", "perf:disable": "echo 'VITE_SHOW_PERF=false' > .env.local"}, "dependencies": {"framer-motion": "^10.16.5", "gsap": "^3.13.0", "i18next": "^25.3.2", "i18next-browser-languagedetector": "^8.2.0", "i18next-http-backend": "^3.0.2", "lenis": "^1.3.4", "lucide-react": "^0.294.0", "ogl": "^1.0.11", "react": "^18.2.0", "react-dom": "^18.2.0", "react-i18next": "^15.6.0", "react-intersection-observer": "^9.5.3", "react-router-dom": "^6.20.1", "react-scroll": "^1.9.3", "zustand": "^5.0.6"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "devDependencies": {"@types/node": "^24.0.13", "@types/react": "^18.3.23", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vitejs/plugin-react": "^4.1.1", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "eslint-plugin-security": "^3.0.1", "license-checker": "^25.0.1", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^4.5.0"}, "keywords": ["security", "vulnerability-scanner", "ai-powered", "offline-first", "byteguardx"], "author": "ByteGuardX Team", "license": "MIT", "homepage": "https://byteguardx.online", "repository": {"type": "git", "url": "https://github.com/byteguardx/byteguardx"}}