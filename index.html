<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="description" content="ByteGuardX - AI-Powered Vulnerability Scanner for Developers" />
    <meta name="keywords" content="security, vulnerability scanner, static analysis, secrets detection" />
    <title>ByteGuardX - AI-Powered Vulnerability Scanner</title>
    
    <!-- Preload fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- Security headers (X-Frame-Options removed - set via HTTP headers only) -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; style-src 'self' 'unsafe-inline' fonts.googleapis.com; font-src 'self' fonts.gstatic.com; script-src 'self'; img-src 'self' data:; connect-src 'self' http://localhost:5000 https://*.herokuapp.com https://*.vercel.app;">
    <meta http-equiv="X-Content-Type-Options" content="nosniff">
    <meta http-equiv="X-XSS-Protection" content="1; mode=block">
  </head>
  <body class="bg-black text-white font-sans antialiased">
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>

    <!-- Aggressive inline styles removed to fix black overlay -->
  </body>
</html>
