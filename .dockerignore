# Security-focused .dockerignore for ByteGuardX
# Prevents sensitive files from being included in Docker images

# Environment and secrets
.env
.env.*
*.env
.environment
secrets/
keys/
certificates/

# Development files
.git/
.gitignore
.github/
.vscode/
.idea/
*.swp
*.swo
*~

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/
.venv/
.env/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn-integrity

# Logs and databases
*.log
logs/
*.db
*.sqlite
*.sqlite3

# Test files
tests/
test_*.py
*_test.py
pytest.ini
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/

# Documentation
docs/
*.md
README*
CHANGELOG*
LICENSE*

# Temporary files
tmp/
temp/
.tmp/
*.tmp
*.temp

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
*.sublime-project
*.sublime-workspace
.vscode/
.idea/

# Security scan reports
security_test_report.txt
bandit-report.json
safety-report.json
trivy-results.sarif

# Backup files
*.bak
*.backup
*.old

# Large files that shouldn't be in containers
*.iso
*.dmg
*.zip
*.tar.gz
*.rar

# Development scripts
scripts/dev/
scripts/test/
dev_tools/

# Configuration files that might contain secrets
config/local/
config/development/
config/test/

# User data and uploads
uploads/
user_data/
scan_results/

# Cache directories
.cache/
.npm/
.yarn/
.pip/
