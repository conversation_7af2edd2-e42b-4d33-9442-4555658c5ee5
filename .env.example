# ByteGuardX Frontend Environment Variables

# API Configuration
VITE_API_URL=http://localhost:5000

# App Configuration
VITE_APP_NAME=ByteGuardX
VITE_APP_VERSION=1.0.0

# Feature Flags
VITE_ENABLE_AUTH=true
VITE_ENABLE_ANALYTICS=false
VITE_ENABLE_DEBUG=false

# External Services (Optional)
VITE_GITHUB_URL=https://github.com/byteguardx/byteguardx
VITE_DOCS_URL=https://docs.byteguardx.com
VITE_SUPPORT_EMAIL=<EMAIL>
