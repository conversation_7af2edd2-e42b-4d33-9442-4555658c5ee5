apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: byteguardx-ingress
  namespace: byteguardx
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/force-ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
    nginx.ingress.kubernetes.io/rate-limit: "100"
    nginx.ingress.kubernetes.io/rate-limit-window: "1m"
    nginx.ingress.kubernetes.io/enable-cors: "true"
    nginx.ingress.kubernetes.io/cors-allow-origin: "https://byteguardx.com,https://app.byteguardx.com"
    nginx.ingress.kubernetes.io/cors-allow-methods: "GET, POST, PUT, DELETE, OPTIONS"
    nginx.ingress.kubernetes.io/cors-allow-headers: "DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range,Authorization"
spec:
  tls:
  - hosts:
    - byteguardx.com
    - app.byteguardx.com
    - api.byteguardx.com
    secretName: byteguardx-tls
  rules:
  - host: byteguardx.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: byteguardx-frontend
            port:
              number: 3000
  - host: app.byteguardx.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: byteguardx-frontend
            port:
              number: 3000
  - host: api.byteguardx.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: byteguardx-backend
            port:
              number: 5000

---
apiVersion: v1
kind: Service
metadata:
  name: byteguardx-nginx
  namespace: byteguardx
  annotations:
    service.beta.kubernetes.io/aws-load-balancer-type: "nlb"
    service.beta.kubernetes.io/aws-load-balancer-ssl-cert: "arn:aws:acm:us-east-1:123456789012:certificate/12345678-1234-1234-1234-123456789012"
    service.beta.kubernetes.io/aws-load-balancer-ssl-ports: "https"
    service.beta.kubernetes.io/aws-load-balancer-backend-protocol: "http"
spec:
  type: LoadBalancer
  ports:
  - name: http
    port: 80
    targetPort: 80
    protocol: TCP
  - name: https
    port: 443
    targetPort: 443
    protocol: TCP
  selector:
    app.kubernetes.io/name: ingress-nginx
    app.kubernetes.io/component: controller
