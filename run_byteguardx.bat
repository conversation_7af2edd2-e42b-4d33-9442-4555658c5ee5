@echo off
REM ByteGuardX Windows Launcher
REM Automatically sets up and runs the complete ByteGuardX application

echo.
echo ========================================
echo   ByteGuardX - AI Security Scanner
echo ========================================
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

REM Check if Node.js is installed
node --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 16+ from https://nodejs.org
    pause
    exit /b 1
)

echo Starting ByteGuardX setup and launch...
echo.

REM Run the setup script
python setup_and_run_byteguardx.py

echo.
echo ByteGuardX has been stopped.
pause
