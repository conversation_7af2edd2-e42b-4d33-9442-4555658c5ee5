[metadata]
name = "xss_scanner"
version = "1.0.0"
description = "Cross-Site Scripting (XSS) vulnerability scanner"
author = "ByteGuardX Team"
license = "MIT"

[security]
signing_key_id = "ed25519:byteguardx-official-2024"
signature = "ed25519:xss_signature_placeholder"
trusted = true
security_level = "high"

[capabilities]
filesystem = ["read"]
network = ["none"]
memory_limit_mb = 256
cpu_limit_percent = 15
execution_timeout_seconds = 120

[runtime]
entrypoint = "src/main.py"
python_version = ">=3.9,<4.0"

[detection]
xss_patterns = [
    "innerHTML\\s*=",
    "document\\.write\\s*\\(",
    "eval\\s*\\(",
    "setTimeout\\s*\\(",
    "setInterval\\s*\\(",
    "\\$\\([^)]*\\)\\.html\\s*\\(",
    "dangerouslySetInnerHTML"
]

user_input_sources = [
    "request\\.",
    "\\$_GET",
    "\\$_POST",
    "params\\.",
    "query\\.",
    "body\\."
]
