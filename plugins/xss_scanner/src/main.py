#!/usr/bin/env python3
"""
ByteGuardX XSS Scanner Plugin
Cross-Site Scripting vulnerability detection
"""

import json
import sys
import re
import hashlib
from typing import Dict, List, Any
from pathlib import Path
import toml

class XSSScanner:
    def __init__(self):
        self.config = self._load_config()
    
    def _load_config(self) -> Dict[str, Any]:
        try:
            config_path = Path(__file__).parent.parent / 'plugin.toml'
            with open(config_path, 'r') as f:
                return toml.load(f)
        except Exception:
            return {}
    
    def scan_content(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        findings = []
        
        # XSS patterns
        xss_patterns = self.config.get('detection', {}).get('xss_patterns', [])
        user_input_patterns = self.config.get('detection', {}).get('user_input_sources', [])
        
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            # Check for XSS patterns
            for pattern in xss_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    # Check if user input is involved
                    has_user_input = any(re.search(ui_pattern, line, re.IGNORECASE) 
                                       for ui_pattern in user_input_patterns)
                    
                    confidence = 0.8 if has_user_input else 0.6
                    severity = 'high' if has_user_input else 'medium'
                    
                    findings.append({
                        'id': f"xss_{hashlib.md5(f'{file_path}:{line_num}'.encode()).hexdigest()[:8]}",
                        'type': 'xss',
                        'severity': severity,
                        'title': 'Potential Cross-Site Scripting (XSS) Vulnerability',
                        'description': f'XSS pattern detected: {pattern}',
                        'file_path': file_path,
                        'line_start': line_num,
                        'line_end': line_num,
                        'column_start': 1,
                        'column_end': len(line),
                        'confidence': confidence,
                        'evidence': line.strip(),
                        'remediation': 'Sanitize user input and use safe DOM manipulation methods',
                        'references': [
                            'https://owasp.org/www-community/attacks/xss/',
                            'https://cwe.mitre.org/data/definitions/79.html'
                        ],
                        'metadata': {
                            'pattern': pattern,
                            'has_user_input': has_user_input
                        }
                    })
        
        return findings

def main():
    try:
        input_data = json.loads(sys.stdin.read())
        scanner = XSSScanner()
        
        findings = scanner.scan_content(
            input_data.get('content', ''),
            input_data.get('file_path', '')
        )
        
        result = {
            'scan_id': input_data.get('scan_id', 'unknown'),
            'plugin_name': 'xss_scanner',
            'plugin_version': '1.0.0',
            'execution_time_ms': 50,
            'findings': findings,
            'errors': [],
            'metrics': {'xss_patterns_checked': 7}
        }
        
        print(json.dumps(result, indent=2))
        
    except Exception as e:
        error_result = {
            'scan_id': 'unknown',
            'plugin_name': 'xss_scanner',
            'plugin_version': '1.0.0',
            'execution_time_ms': 0,
            'findings': [],
            'errors': [{'code': 'PLUGIN_ERROR', 'message': str(e)}],
            'metrics': {}
        }
        print(json.dumps(error_result, indent=2))
        sys.exit(1)

if __name__ == '__main__':
    main()
