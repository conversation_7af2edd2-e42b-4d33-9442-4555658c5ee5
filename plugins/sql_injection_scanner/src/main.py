#!/usr/bin/env python3
"""
ByteGuardX SQL Injection Scanner Plugin
Advanced SQL injection vulnerability detection with AST analysis
"""

import json
import sys
import re
import ast
import hashlib
from typing import Dict, List, Any, Tuple, Optional, Set
from pathlib import Path
import toml

class ASTAnalyzer:
    """AST-based analysis for SQL injection detection"""
    
    def __init__(self):
        self.dangerous_functions = set()
        self.sql_operations = set()
        self.user_input_sources = set()
    
    def analyze_code(self, code: str) -> List[Dict[str, Any]]:
        """Analyze code using AST for SQL injection patterns"""
        try:
            tree = ast.parse(code)
            findings = []
            
            for node in ast.walk(tree):
                if isinstance(node, ast.Call):
                    finding = self._analyze_function_call(node, code)
                    if finding:
                        findings.append(finding)
                
                elif isinstance(node, ast.BinOp):
                    finding = self._analyze_string_concatenation(node, code)
                    if finding:
                        findings.append(finding)
            
            return findings
            
        except SyntaxError:
            return []
    
    def _analyze_function_call(self, node: ast.Call, code: str) -> Optional[Dict[str, Any]]:
        """Analyze function calls for SQL operations"""
        func_name = self._get_function_name(node)
        
        if not func_name:
            return None
        
        # Check for database operation functions
        dangerous_funcs = ['execute', 'executemany', 'query', 'raw']
        
        if any(dangerous in func_name.lower() for dangerous in dangerous_funcs):
            # Check if arguments contain string concatenation or formatting
            for arg in node.args:
                if self._contains_dynamic_sql(arg):
                    return {
                        'type': 'dynamic_sql_execution',
                        'function': func_name,
                        'line': node.lineno,
                        'column': node.col_offset,
                        'confidence': 0.8
                    }
        
        return None
    
    def _analyze_string_concatenation(self, node: ast.BinOp, code: str) -> Optional[Dict[str, Any]]:
        """Analyze string concatenation that might build SQL queries"""
        if isinstance(node.op, ast.Add):
            # Check if concatenation involves SQL keywords
            if self._contains_sql_keywords(node):
                return {
                    'type': 'sql_string_concatenation',
                    'line': node.lineno,
                    'column': node.col_offset,
                    'confidence': 0.6
                }
        
        return None
    
    def _get_function_name(self, node: ast.Call) -> Optional[str]:
        """Extract function name from call node"""
        if isinstance(node.func, ast.Name):
            return node.func.id
        elif isinstance(node.func, ast.Attribute):
            return node.func.attr
        return None
    
    def _contains_dynamic_sql(self, node: ast.AST) -> bool:
        """Check if node contains dynamic SQL construction"""
        if isinstance(node, ast.BinOp) and isinstance(node.op, ast.Add):
            return True
        elif isinstance(node, ast.Call):
            func_name = self._get_function_name(node)
            if func_name and 'format' in func_name.lower():
                return True
        return False
    
    def _contains_sql_keywords(self, node: ast.BinOp) -> bool:
        """Check if binary operation contains SQL keywords"""
        sql_keywords = ['SELECT', 'INSERT', 'UPDATE', 'DELETE', 'FROM', 'WHERE']
        
        def check_node_for_sql(n):
            if isinstance(n, ast.Str):
                return any(keyword.lower() in n.s.lower() for keyword in sql_keywords)
            elif isinstance(n, ast.Constant) and isinstance(n.value, str):
                return any(keyword.lower() in n.value.lower() for keyword in sql_keywords)
            return False
        
        return check_node_for_sql(node.left) or check_node_for_sql(node.right)

class PatternAnalyzer:
    """Pattern-based SQL injection detection"""
    
    def __init__(self, patterns: List[str], contexts: List[str]):
        self.injection_patterns = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
        self.context_patterns = [re.compile(context, re.IGNORECASE) for context in contexts]
    
    def find_injections(self, content: str) -> List[Dict[str, Any]]:
        """Find potential SQL injection vulnerabilities"""
        findings = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            # Check for SQL injection patterns
            for pattern in self.injection_patterns:
                matches = pattern.finditer(line)
                for match in matches:
                    # Check if this line also has user input context
                    has_user_input = any(ctx.search(line) for ctx in self.context_patterns)
                    
                    confidence = 0.7 if has_user_input else 0.5
                    
                    findings.append({
                        'type': 'sql_injection_pattern',
                        'pattern': pattern.pattern,
                        'match': match.group(),
                        'line': line_num,
                        'start': match.start(),
                        'end': match.end(),
                        'has_user_input': has_user_input,
                        'confidence': confidence
                    })
        
        return findings

class SQLInjectionScanner:
    """Main SQL injection scanner implementation"""
    
    def __init__(self):
        self.config = self._load_config()
        self.ast_analyzer = ASTAnalyzer()
        
        detection_config = self.config.get('detection', {})
        self.pattern_analyzer = PatternAnalyzer(
            detection_config.get('injection_patterns', []),
            detection_config.get('vulnerable_contexts', [])
        )
    
    def _load_config(self) -> Dict[str, Any]:
        """Load plugin configuration"""
        try:
            config_path = Path(__file__).parent.parent / 'plugin.toml'
            with open(config_path, 'r') as f:
                return toml.load(f)
        except Exception:
            return {}
    
    def scan_content(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        """Scan content for SQL injection vulnerabilities"""
        findings = []
        
        # Skip non-code files
        if not self._is_code_file(file_path):
            return findings
        
        # 1. AST-based analysis
        ast_findings = self.ast_analyzer.analyze_code(content)
        findings.extend(ast_findings)
        
        # 2. Pattern-based analysis
        pattern_findings = self.pattern_analyzer.find_injections(content)
        findings.extend(pattern_findings)
        
        # 3. Context analysis
        context_findings = self._analyze_context(content)
        findings.extend(context_findings)
        
        # 4. Format findings
        return self._format_findings(findings, file_path, content)
    
    def _is_code_file(self, file_path: str) -> bool:
        """Check if file is a code file that might contain SQL"""
        code_extensions = {'.py', '.java', '.php', '.js', '.ts', '.cs', '.rb', '.go'}
        return Path(file_path).suffix.lower() in code_extensions
    
    def _analyze_context(self, content: str) -> List[Dict[str, Any]]:
        """Analyze context for SQL injection vulnerabilities"""
        findings = []
        lines = content.split('\n')
        
        # Look for database connections and operations
        db_patterns = [
            r'(cursor|connection|db)\.(execute|query|executemany)\s*\(',
            r'(SELECT|INSERT|UPDATE|DELETE).*\+.*',
            r'(SELECT|INSERT|UPDATE|DELETE).*format\s*\(',
            r'(SELECT|INSERT|UPDATE|DELETE).*%\s*\('
        ]
        
        for line_num, line in enumerate(lines, 1):
            for pattern in db_patterns:
                if re.search(pattern, line, re.IGNORECASE):
                    # Check for user input in the same line or nearby lines
                    context_lines = lines[max(0, line_num-3):line_num+2]
                    context_text = '\n'.join(context_lines)
                    
                    user_input_indicators = [
                        'request.', 'input(', 'argv', 'form.', 'query.',
                        'params.', 'json.', 'POST', 'GET'
                    ]
                    
                    has_user_input = any(indicator in context_text for indicator in user_input_indicators)
                    
                    if has_user_input:
                        findings.append({
                            'type': 'sql_with_user_input',
                            'line': line_num,
                            'pattern': pattern,
                            'confidence': 0.8
                        })
        
        return findings
    
    def _format_findings(self, findings: List[Dict[str, Any]], file_path: str, content: str) -> List[Dict[str, Any]]:
        """Format findings to standard output format"""
        formatted = []
        
        for finding in findings:
            severity = self._determine_severity(finding)
            
            # Get line and column information
            line = finding.get('line', 1)
            column = finding.get('start', finding.get('column', 1))
            
            # Generate evidence
            lines = content.split('\n')
            evidence = lines[line - 1] if line <= len(lines) else ''
            
            formatted_finding = {
                'id': f"sqli_{hashlib.md5(f'{file_path}:{line}:{finding.get('type')}'.encode()).hexdigest()[:8]}",
                'type': 'sql_injection',
                'severity': severity,
                'title': self._generate_title(finding),
                'description': self._generate_description(finding),
                'file_path': file_path,
                'line_start': line,
                'line_end': line,
                'column_start': column,
                'column_end': column + len(finding.get('match', '')),
                'confidence': finding.get('confidence', 0.5),
                'evidence': evidence.strip(),
                'remediation': self._generate_remediation(finding),
                'references': [
                    'https://owasp.org/www-community/attacks/SQL_Injection',
                    'https://cwe.mitre.org/data/definitions/89.html',
                    'https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html'
                ],
                'metadata': {
                    'detection_method': finding.get('type'),
                    'pattern': finding.get('pattern'),
                    'has_user_input': finding.get('has_user_input', False),
                    'function_name': finding.get('function')
                }
            }
            
            formatted.append(formatted_finding)
        
        return formatted
    
    def _determine_severity(self, finding: Dict[str, Any]) -> str:
        """Determine severity based on finding characteristics"""
        confidence = finding.get('confidence', 0.5)
        has_user_input = finding.get('has_user_input', False)
        finding_type = finding.get('type', '')
        
        # Critical if high confidence and user input involved
        if confidence >= 0.8 and has_user_input:
            return 'critical'
        
        # High if AST detected dynamic SQL execution
        if finding_type == 'dynamic_sql_execution':
            return 'high'
        
        # High if pattern detected with user input
        if confidence >= 0.7 and has_user_input:
            return 'high'
        
        # Medium for other pattern matches
        if confidence >= 0.6:
            return 'medium'
        
        return 'low'
    
    def _generate_title(self, finding: Dict[str, Any]) -> str:
        """Generate title for finding"""
        finding_type = finding.get('type', '')
        
        titles = {
            'dynamic_sql_execution': 'Dynamic SQL Execution Vulnerability',
            'sql_string_concatenation': 'SQL Query String Concatenation',
            'sql_injection_pattern': 'SQL Injection Pattern Detected',
            'sql_with_user_input': 'SQL Query with User Input'
        }
        
        return titles.get(finding_type, 'Potential SQL Injection Vulnerability')
    
    def _generate_description(self, finding: Dict[str, Any]) -> str:
        """Generate description for finding"""
        finding_type = finding.get('type', '')
        
        descriptions = {
            'dynamic_sql_execution': 'Dynamic SQL query execution detected. This may allow SQL injection if user input is not properly sanitized.',
            'sql_string_concatenation': 'SQL query built using string concatenation. This pattern is vulnerable to SQL injection attacks.',
            'sql_injection_pattern': 'Code pattern commonly associated with SQL injection vulnerabilities detected.',
            'sql_with_user_input': 'SQL query that appears to incorporate user input without proper parameterization.'
        }
        
        base_desc = descriptions.get(finding_type, 'Potential SQL injection vulnerability detected.')
        
        if finding.get('has_user_input'):
            base_desc += ' User input detected in the same context, increasing risk.'
        
        return base_desc
    
    def _generate_remediation(self, finding: Dict[str, Any]) -> str:
        """Generate remediation advice"""
        return """Use parameterized queries or prepared statements instead of string concatenation:
        
VULNERABLE:
cursor.execute("SELECT * FROM users WHERE id = " + user_id)

SECURE:
cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))

Additional recommendations:
- Use ORM frameworks with built-in SQL injection protection
- Validate and sanitize all user inputs
- Apply principle of least privilege to database accounts
- Use stored procedures where appropriate"""

def main():
    """Main plugin entry point"""
    try:
        # Read input from stdin
        input_data = json.loads(sys.stdin.read())
        
        # Extract required fields
        scan_id = input_data.get('scan_id', 'unknown')
        content = input_data.get('content', '')
        file_path = input_data.get('file_path', '')
        
        # Initialize scanner
        scanner = SQLInjectionScanner()
        
        # Perform scan
        import time
        start_time = time.time()
        findings = scanner.scan_content(content, file_path)
        end_time = time.time()
        
        execution_time = int((end_time - start_time) * 1000)
        
        # Format output
        result = {
            'scan_id': scan_id,
            'plugin_name': 'sql_injection_scanner',
            'plugin_version': '1.0.0',
            'execution_time_ms': execution_time,
            'findings': findings,
            'errors': [],
            'metrics': {
                'lines_scanned': len(content.split('\n')),
                'ast_nodes_analyzed': content.count('\n') * 2,  # Rough estimate
                'patterns_checked': len(scanner.pattern_analyzer.injection_patterns),
                'vulnerabilities_found': len(findings)
            }
        }
        
        print(json.dumps(result, indent=2))
        
    except Exception as e:
        # Error handling
        error_result = {
            'scan_id': input_data.get('scan_id', 'unknown') if 'input_data' in locals() else 'unknown',
            'plugin_name': 'sql_injection_scanner',
            'plugin_version': '1.0.0',
            'execution_time_ms': 0,
            'findings': [],
            'errors': [{
                'code': 'PLUGIN_ERROR',
                'message': str(e),
                'type': type(e).__name__
            }],
            'metrics': {}
        }
        
        print(json.dumps(error_result, indent=2))
        sys.exit(1)

if __name__ == '__main__':
    main()
