[metadata]
name = "sql_injection_scanner"
version = "1.0.0"
description = "Advanced SQL injection vulnerability scanner with AST analysis"
author = "ByteGuardX Team"
license = "MIT"
homepage = "https://github.com/byteguardx/plugins/sql-injection-scanner"

[build]
content_hash = "sha256:b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef12345678"
build_timestamp = "2024-01-15T10:35:00Z"
reproducible_build = true
build_environment = "python-3.11-alpine"

[security]
signing_key_id = "ed25519:byteguardx-official-2024"
signature = "ed25519:4056022100b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef12345678022100edcba0987654321098765432109876543210fedcba0987654321098765432109"
trusted = true
security_level = "high"

[capabilities]
filesystem = ["read"]
network = ["none"]
models = ["sql_classifier", "ast_analyzer"]
memory_limit_mb = 512
cpu_limit_percent = 20
execution_timeout_seconds = 180

[runtime]
entrypoint = "src/main.py"
python_version = ">=3.9,<4.0"
dependencies = [
    "sqlparse>=0.4.4",
    "ast>=1.0.0",
    "regex>=2023.10.3"
]

[api]
input_schema = "schemas/input.json"
output_schema = "schemas/output.json"
ipc_version = "1.2"

[models]
required_models = [
    {
        name = "sql_classifier",
        path = "models/sql_injection_classifier.onnx",
        hash = "sha256:8765432109abcdef8765432109abcdef8765432109abcdef8765432109abcdef",
        size_mb = 15.2
    }
]

[detection]
# SQL injection patterns
dangerous_functions = [
    "execute", "executemany", "query", "raw", "extra",
    "cursor.execute", "db.execute", "connection.execute"
]

sql_keywords = [
    "SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "CREATE",
    "ALTER", "UNION", "ORDER BY", "GROUP BY", "HAVING"
]

injection_patterns = [
    "\\s+(OR|AND)\\s+['\"]?\\w+['\"]?\\s*=\\s*['\"]?\\w+['\"]?",
    "\\s+UNION\\s+SELECT",
    "['\"]\\s*;\\s*(DROP|DELETE|UPDATE|INSERT)",
    "\\s+--\\s*",
    "/\\*.*\\*/",
    "\\s+(EXEC|EXECUTE)\\s*\\(",
    "\\s+xp_\\w+",
    "\\s+sp_\\w+"
]

# Context patterns for vulnerability detection
vulnerable_contexts = [
    "request\\.GET",
    "request\\.POST",
    "request\\.form",
    "input\\(",
    "raw_input\\(",
    "sys\\.argv",
    "os\\.environ"
]
