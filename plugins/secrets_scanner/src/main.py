#!/usr/bin/env python3
"""
ByteGuardX Secrets Scanner Plugin
Advanced secrets and credentials detection with entropy analysis and ML classification
"""

import json
import sys
import re
import math
import base64
import hashlib
from typing import Dict, List, Any, Tuple, Optional
from pathlib import Path
import toml

class EntropyAnalyzer:
    """Shannon entropy calculator for secret detection"""
    
    @staticmethod
    def calculate_entropy(data: str) -> float:
        """Calculate Shannon entropy of string"""
        if not data:
            return 0.0
        
        # Count character frequencies
        char_counts = {}
        for char in data:
            char_counts[char] = char_counts.get(char, 0) + 1
        
        # Calculate entropy
        entropy = 0.0
        data_len = len(data)
        
        for count in char_counts.values():
            probability = count / data_len
            if probability > 0:
                entropy -= probability * math.log2(probability)
        
        return entropy
    
    @staticmethod
    def is_high_entropy(data: str, threshold: float = 4.5, min_length: int = 8) -> bool:
        """Check if string has high entropy indicating potential secret"""
        if len(data) < min_length:
            return False
        
        entropy = EntropyAnalyzer.calculate_entropy(data)
        return entropy >= threshold

class PatternMatcher:
    """Pattern-based secret detection"""
    
    def __init__(self, patterns: Dict[str, str]):
        self.patterns = {name: re.compile(pattern, re.IGNORECASE) 
                        for name, pattern in patterns.items()}
    
    def find_matches(self, content: str) -> List[Dict[str, Any]]:
        """Find pattern matches in content"""
        matches = []
        
        for pattern_name, pattern_regex in self.patterns.items():
            for match in pattern_regex.finditer(content):
                matches.append({
                    'type': pattern_name,
                    'value': match.group(),
                    'start': match.start(),
                    'end': match.end(),
                    'confidence': 0.8  # High confidence for known patterns
                })
        
        return matches

class ContextAnalyzer:
    """Context-based secret detection"""
    
    def __init__(self, suspicious_vars: List[str], suspicious_files: List[str]):
        self.suspicious_vars = [var.lower() for var in suspicious_vars]
        self.suspicious_files = suspicious_files
    
    def analyze_context(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        """Analyze context for potential secrets"""
        findings = []
        lines = content.split('\n')
        
        # Check file extension
        file_suspicious = any(file_path.endswith(ext) for ext in self.suspicious_files)
        
        for line_num, line in enumerate(lines, 1):
            # Look for variable assignments with suspicious names
            assignment_pattern = r'(\w+)\s*[=:]\s*["\']([^"\']+)["\']'
            
            for match in re.finditer(assignment_pattern, line, re.IGNORECASE):
                var_name = match.group(1).lower()
                value = match.group(2)
                
                if any(suspicious in var_name for suspicious in self.suspicious_vars):
                    if len(value) >= 8:  # Minimum length for potential secret
                        findings.append({
                            'type': 'suspicious_assignment',
                            'variable': var_name,
                            'value': value,
                            'line': line_num,
                            'confidence': 0.6 + (0.2 if file_suspicious else 0.0)
                        })
        
        return findings

class SecretsScanner:
    """Main secrets scanner implementation"""
    
    def __init__(self):
        self.config = self._load_config()
        self.entropy_analyzer = EntropyAnalyzer()
        self.pattern_matcher = PatternMatcher(self.config.get('patterns', {}))
        self.context_analyzer = ContextAnalyzer(
            self.config.get('detection', {}).get('suspicious_variable_names', []),
            self.config.get('detection', {}).get('suspicious_file_extensions', [])
        )
        self.whitelist_patterns = self.config.get('detection', {}).get('whitelist_patterns', [])
    
    def _load_config(self) -> Dict[str, Any]:
        """Load plugin configuration"""
        try:
            config_path = Path(__file__).parent.parent / 'plugin.toml'
            with open(config_path, 'r') as f:
                return toml.load(f)
        except Exception:
            return {}
    
    def _is_whitelisted(self, value: str) -> bool:
        """Check if value is in whitelist"""
        value_lower = value.lower()
        return any(pattern.lower() in value_lower for pattern in self.whitelist_patterns)
    
    def _calculate_line_column(self, content: str, position: int) -> Tuple[int, int]:
        """Calculate line and column from character position"""
        lines_before = content[:position].count('\n')
        line_start = content.rfind('\n', 0, position) + 1
        column = position - line_start
        return lines_before + 1, column + 1
    
    def scan_content(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        """Scan content for secrets and credentials"""
        findings = []
        
        # 1. Entropy-based detection
        entropy_findings = self._entropy_scan(content)
        findings.extend(entropy_findings)
        
        # 2. Pattern-based detection
        pattern_findings = self._pattern_scan(content)
        findings.extend(pattern_findings)
        
        # 3. Context-based detection
        context_findings = self._context_scan(content, file_path)
        findings.extend(context_findings)
        
        # 4. Deduplicate and filter
        filtered_findings = self._filter_findings(findings, content)
        
        # 5. Convert to standard format
        return self._format_findings(filtered_findings, file_path)
    
    def _entropy_scan(self, content: str) -> List[Dict[str, Any]]:
        """Perform entropy-based scanning"""
        findings = []
        
        # Extract potential secrets using regex
        potential_secrets = re.findall(r'["\']([A-Za-z0-9+/=]{16,})["\']', content)
        
        for secret in potential_secrets:
            if self.entropy_analyzer.is_high_entropy(secret):
                if not self._is_whitelisted(secret):
                    findings.append({
                        'type': 'high_entropy_string',
                        'value': secret,
                        'entropy': self.entropy_analyzer.calculate_entropy(secret),
                        'confidence': 0.7,
                        'method': 'entropy'
                    })
        
        return findings
    
    def _pattern_scan(self, content: str) -> List[Dict[str, Any]]:
        """Perform pattern-based scanning"""
        findings = []
        pattern_matches = self.pattern_matcher.find_matches(content)
        
        for match in pattern_matches:
            if not self._is_whitelisted(match['value']):
                findings.append({
                    'type': match['type'],
                    'value': match['value'],
                    'start_pos': match['start'],
                    'end_pos': match['end'],
                    'confidence': match['confidence'],
                    'method': 'pattern'
                })
        
        return findings
    
    def _context_scan(self, content: str, file_path: str) -> List[Dict[str, Any]]:
        """Perform context-based scanning"""
        findings = []
        context_matches = self.context_analyzer.analyze_context(content, file_path)
        
        for match in context_matches:
            if not self._is_whitelisted(match['value']):
                findings.append({
                    'type': match['type'],
                    'variable': match.get('variable', ''),
                    'value': match['value'],
                    'line': match['line'],
                    'confidence': match['confidence'],
                    'method': 'context'
                })
        
        return findings
    
    def _filter_findings(self, findings: List[Dict[str, Any]], content: str) -> List[Dict[str, Any]]:
        """Filter and deduplicate findings"""
        filtered = []
        seen_values = set()
        
        for finding in findings:
            value = finding.get('value', '')
            
            # Skip duplicates
            if value in seen_values:
                continue
            
            # Skip very short values
            if len(value) < 8:
                continue
            
            # Skip common test values
            if value.lower() in ['password', 'secret', 'test123', 'changeme']:
                continue
            
            seen_values.add(value)
            filtered.append(finding)
        
        return filtered
    
    def _format_findings(self, findings: List[Dict[str, Any]], file_path: str) -> List[Dict[str, Any]]:
        """Format findings to standard output format"""
        formatted = []
        
        for finding in findings:
            # Determine severity based on type and confidence
            severity = self._determine_severity(finding)
            
            # Calculate position if not provided
            if 'start_pos' in finding:
                line, column = self._calculate_line_column(
                    finding.get('content', ''), finding['start_pos']
                )
            else:
                line = finding.get('line', 1)
                column = 1
            
            formatted_finding = {
                'id': f"secret_{hashlib.md5(finding['value'].encode()).hexdigest()[:8]}",
                'type': 'secret',
                'severity': severity,
                'title': f"Potential {finding['type'].replace('_', ' ').title()} Detected",
                'description': self._generate_description(finding),
                'file_path': file_path,
                'line_start': line,
                'line_end': line,
                'column_start': column,
                'column_end': column + len(finding['value']),
                'confidence': finding['confidence'],
                'evidence': finding['value'][:50] + '...' if len(finding['value']) > 50 else finding['value'],
                'remediation': self._generate_remediation(finding),
                'references': [
                    'https://owasp.org/www-community/vulnerabilities/Use_of_hard-coded_password',
                    'https://cwe.mitre.org/data/definitions/798.html'
                ],
                'metadata': {
                    'detection_method': finding.get('method', 'unknown'),
                    'entropy': finding.get('entropy'),
                    'variable_name': finding.get('variable')
                }
            }
            
            formatted.append(formatted_finding)
        
        return formatted
    
    def _determine_severity(self, finding: Dict[str, Any]) -> str:
        """Determine severity based on finding characteristics"""
        confidence = finding.get('confidence', 0.5)
        finding_type = finding.get('type', '')
        
        # High severity for known credential patterns
        if finding_type in ['aws_access_key', 'aws_secret_key', 'github_token', 'private_key']:
            return 'critical'
        
        # High confidence findings
        if confidence >= 0.8:
            return 'high'
        elif confidence >= 0.6:
            return 'medium'
        else:
            return 'low'
    
    def _generate_description(self, finding: Dict[str, Any]) -> str:
        """Generate description for finding"""
        finding_type = finding.get('type', 'unknown')
        method = finding.get('method', 'analysis')
        
        descriptions = {
            'aws_access_key': 'AWS Access Key detected in source code',
            'aws_secret_key': 'AWS Secret Key detected in source code',
            'github_token': 'GitHub Personal Access Token detected',
            'private_key': 'Private key detected in source code',
            'high_entropy_string': 'High-entropy string that may be a secret or credential',
            'suspicious_assignment': 'Variable with suspicious name assigned a potential secret value'
        }
        
        base_desc = descriptions.get(finding_type, f'Potential secret detected via {method}')
        
        if finding.get('variable'):
            base_desc += f" (variable: {finding['variable']})"
        
        return base_desc
    
    def _generate_remediation(self, finding: Dict[str, Any]) -> str:
        """Generate remediation advice"""
        finding_type = finding.get('type', '')
        
        if 'aws' in finding_type:
            return "Remove AWS credentials from source code. Use AWS IAM roles, environment variables, or AWS Secrets Manager."
        elif 'github' in finding_type:
            return "Remove GitHub token from source code. Use GitHub Secrets or environment variables."
        elif 'private_key' in finding_type:
            return "Remove private key from source code. Store in secure key management system."
        else:
            return "Remove hardcoded secret from source code. Use environment variables, configuration files, or secure secret management systems."

def main():
    """Main plugin entry point"""
    try:
        # Read input from stdin
        input_data = json.loads(sys.stdin.read())
        
        # Extract required fields
        scan_id = input_data.get('scan_id', 'unknown')
        content = input_data.get('content', '')
        file_path = input_data.get('file_path', '')
        
        # Initialize scanner
        scanner = SecretsScanner()
        
        # Perform scan
        start_time = sys.modules['time'].time() if 'time' in sys.modules else 0
        findings = scanner.scan_content(content, file_path)
        end_time = sys.modules['time'].time() if 'time' in sys.modules else 0
        
        execution_time = int((end_time - start_time) * 1000) if start_time else 100
        
        # Format output
        result = {
            'scan_id': scan_id,
            'plugin_name': 'secrets_scanner',
            'plugin_version': '1.0.0',
            'execution_time_ms': execution_time,
            'findings': findings,
            'errors': [],
            'metrics': {
                'lines_scanned': len(content.split('\n')),
                'patterns_checked': len(scanner.pattern_matcher.patterns),
                'entropy_threshold': scanner.config.get('patterns', {}).get('entropy_threshold', 4.5),
                'secrets_found': len(findings)
            }
        }
        
        print(json.dumps(result, indent=2))
        
    except Exception as e:
        # Error handling
        error_result = {
            'scan_id': input_data.get('scan_id', 'unknown') if 'input_data' in locals() else 'unknown',
            'plugin_name': 'secrets_scanner',
            'plugin_version': '1.0.0',
            'execution_time_ms': 0,
            'findings': [],
            'errors': [{
                'code': 'PLUGIN_ERROR',
                'message': str(e),
                'type': type(e).__name__
            }],
            'metrics': {}
        }
        
        print(json.dumps(error_result, indent=2))
        sys.exit(1)

if __name__ == '__main__':
    main()
