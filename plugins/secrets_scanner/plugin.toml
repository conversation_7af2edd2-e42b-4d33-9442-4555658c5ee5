[metadata]
name = "secrets_scanner"
version = "1.0.0"
description = "Advanced secrets and credentials scanner with entropy analysis"
author = "ByteGuardX Team"
license = "MIT"
homepage = "https://github.com/byteguardx/plugins/secrets-scanner"

[build]
content_hash = "sha256:a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456"
build_timestamp = "2024-01-15T10:30:00Z"
reproducible_build = true
build_environment = "python-3.11-alpine"

[security]
signing_key_id = "ed25519:byteguardx-official-2024"
signature = "ed25519:3045022100a1b2c3d4e5f6789012345678901234567890abcdef1234567890abcdef123456022100fedcba0987654321098765432109876543210fedcba0987654321098765432109"
trusted = true
security_level = "high"

[capabilities]
filesystem = ["read"]
network = ["none"]
models = ["entropy_model", "pattern_classifier"]
memory_limit_mb = 256
cpu_limit_percent = 15
execution_timeout_seconds = 120

[runtime]
entrypoint = "src/main.py"
python_version = ">=3.9,<4.0"
dependencies = [
    "regex>=2023.10.3",
    "entropy>=1.0.1",
    "base64>=1.0.0",
    "hashlib>=1.0.0"
]

[api]
input_schema = "schemas/input.json"
output_schema = "schemas/output.json"
ipc_version = "1.2"

[models]
required_models = [
    {
        name = "entropy_model",
        path = "models/entropy_classifier.onnx",
        hash = "sha256:9876543210abcdef9876543210abcdef9876543210abcdef9876543210abcdef",
        size_mb = 12.5
    },
    {
        name = "pattern_classifier",
        path = "models/secret_patterns.json",
        hash = "sha256:fedcba0987654321fedcba0987654321fedcba0987654321fedcba0987654321",
        size_mb = 2.1
    }
]

[patterns]
# High-entropy patterns for secret detection
entropy_threshold = 4.5
min_length = 8
max_length = 128

# Known secret patterns
aws_access_key = "AKIA[0-9A-Z]{16}"
aws_secret_key = "[0-9a-zA-Z/+]{40}"
github_token = "ghp_[0-9a-zA-Z]{36}"
slack_token = "xox[baprs]-[0-9a-zA-Z-]+"
jwt_token = "eyJ[0-9a-zA-Z_-]+\\.[0-9a-zA-Z_-]+\\.[0-9a-zA-Z_-]+"
api_key = "[aA][pP][iI]_?[kK][eE][yY].*['\"][0-9a-zA-Z]{32,45}['\"]"
password = "[pP][aA][sS][sS][wW][oO][rR][dD].*['\"][^'\"]{8,}['\"]"
private_key = "-----BEGIN [A-Z ]+PRIVATE KEY-----"
certificate = "-----BEGIN CERTIFICATE-----"

[detection]
# Detection strategies
strategies = ["entropy", "pattern", "context", "ml_classification"]
confidence_weights = {
    entropy = 0.3,
    pattern = 0.4,
    context = 0.2,
    ml_classification = 0.1
}

# Context analysis
suspicious_variable_names = [
    "password", "passwd", "pwd", "secret", "key", "token", "auth",
    "credential", "cred", "api_key", "apikey", "access_key", "private_key",
    "cert", "certificate", "oauth", "jwt", "session", "cookie"
]

suspicious_file_extensions = [".pem", ".key", ".p12", ".pfx", ".jks", ".keystore"]

# Whitelist patterns to reduce false positives
whitelist_patterns = [
    "example.com",
    "localhost",
    "127.0.0.1",
    "<EMAIL>",
    "password123",
    "changeme",
    "placeholder"
]
